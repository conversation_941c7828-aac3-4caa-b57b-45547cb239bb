{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/IMPORTANT/work/export/front/src/app/page.tsx"], "sourcesContent": ["\"use client\";\n\nimport Link from \"next/link\";\nimport { useState, useEffect } from \"react\";\nimport {\n  <PERSON>,\n  <PERSON>,\n  <PERSON>,\n  Star,\n  Users,\n  ArrowLeft,\n  Spark<PERSON>,\n} from \"lucide-react\";\nimport useTranslation from \"@/hooks/useTranslation\";\n\nexport default function Home() {\n  const [isAuthenticated, setIsAuthenticated] = useState(false);\n\n  useEffect(() => {\n    const token = localStorage.getItem(\"access_token\");\n    setIsAuthenticated(!!token);\n  }, []);\n  const { t } = useTranslation();\n\n  return (\n    <div className=\"min-h-screen\">\n      {/* Hero Section */}\n      <section className=\"relative overflow-hidden bg-white py-12 sm:py-16 lg:py-20\">\n        {/* Decorative elements - hidden on mobile */}\n        <div className=\"absolute top-10 right-10 text-pink-200 animate-bounce-slow hidden lg:block\">\n          <Star className=\"w-8 h-8\" />\n        </div>\n        <div className=\"absolute top-32 left-20 text-pink-200 animate-float hidden lg:block\">\n          <Heart className=\"w-6 h-6\" />\n        </div>\n        <div className=\"absolute bottom-20 right-32 text-pink-200 animate-bounce-slow hidden lg:block\">\n          <Sparkles className=\"w-10 h-10\" />\n        </div>\n\n        <div className=\"container mx-auto px-4 text-center\">\n          <div className=\"max-w-4xl mx-auto\">\n            {/* Logo and Title */}\n            <div className=\"flex flex-col sm:flex-row items-center justify-center space-y-4 sm:space-y-0 sm:space-x-4 sm:space-x-reverse mb-8\">\n              <div className=\"bg-pink-500 p-3 sm:p-4 rounded-xl\">\n                <Baby className=\"w-10 h-10 sm:w-12 sm:h-12 text-white\" />\n              </div>\n              <div>\n                <h1 className=\"text-3xl sm:text-4xl md:text-5xl lg:text-6xl font-bold text-pink-600\">\n                  حضانة الأمل\n                </h1>\n                <p className=\"text-base sm:text-lg md:text-xl text-gray-600 mt-2\">\n                  مكان آمن لنمو أطفالكم\n                </p>\n              </div>\n            </div>\n\n            <h2 className=\"text-2xl sm:text-3xl md:text-4xl font-bold text-gray-800 mb-6\">\n              مرحباً بكم في حضانة الأمل\n            </h2>\n\n            <p className=\"text-lg sm:text-xl text-gray-600 mb-8 sm:mb-12 max-w-3xl mx-auto leading-relaxed\">\n              نوفر بيئة تعليمية آمنة ومحبة لأطفالكم مع أحدث أنظمة الأمان\n              والمتابعة\n            </p>\n\n            {/* CTA Buttons */}\n            <div className=\"flex flex-col sm:flex-row gap-4 sm:gap-6 justify-center\">\n              {isAuthenticated ? (\n                <Link\n                  href=\"/account\"\n                  className=\"btn-nursery inline-flex items-center justify-center space-x-3 space-x-reverse\"\n                >\n                  <Users className=\"w-5 h-5\" />\n                  <span>حسابي</span>\n                  <ArrowLeft className=\"w-4 h-4\" />\n                </Link>\n              ) : (\n                <>\n                  <Link\n                    href=\"/login\"\n                    className=\"btn-nursery inline-flex items-center justify-center space-x-3 space-x-reverse\"\n                  >\n                    <Users className=\"w-5 h-5\" />\n                    <span>تسجيل الدخول</span>\n                    <ArrowLeft className=\"w-4 h-4\" />\n                  </Link>\n                  <Link\n                    href=\"/signup\"\n                    className=\"btn-nursery-outline inline-flex items-center justify-center space-x-3 space-x-reverse\"\n                  >\n                    <Baby className=\"w-5 h-5\" />\n                    <span>إنشاء حساب</span>\n                  </Link>\n                </>\n              )}\n            </div>\n          </div>\n        </div>\n      </section>\n\n      {/* Features Section */}\n      <section className=\"py-12 sm:py-16 lg:py-20 bg-gray-50\">\n        <div className=\"container mx-auto px-4 sm:px-6 lg:px-8\">\n          <div className=\"text-center mb-12 sm:mb-16\">\n            <h3 className=\"text-2xl sm:text-3xl md:text-4xl font-bold text-gray-800 mb-4\">\n              لماذا تختار حضانة الأمل؟\n            </h3>\n            <p className=\"text-lg sm:text-xl text-gray-600 max-w-2xl mx-auto\">\n              نوفر أفضل الخدمات والمميزات لضمان راحة وأمان أطفالكم\n            </p>\n          </div>\n\n          <div className=\"grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-6 sm:gap-8\">\n            {/* Safety Feature */}\n            <div className=\"card-nursery p-8 text-center group hover:scale-105\">\n              <div className=\"bg-gradient-to-br from-green-400 to-green-600 w-16 h-16 rounded-2xl flex items-center justify-center mx-auto mb-6 group-hover:rotate-12 transition-transform\">\n                <Shield className=\"w-8 h-8 text-white\" />\n              </div>\n              <h4 className=\"text-xl font-bold text-gray-800 mb-4\">\n                {t(\"nursery.features.safety\")}\n              </h4>\n              <p className=\"text-gray-600 leading-relaxed\">\n                نظام أمان متطور مع تصاريح إلكترونية وتتبع دقيق لضمان سلامة\n                الأطفال\n              </p>\n            </div>\n\n            {/* Education Feature */}\n            <div className=\"card-nursery p-8 text-center group hover:scale-105\">\n              <div className=\"bg-gradient-to-br from-blue-400 to-blue-600 w-16 h-16 rounded-2xl flex items-center justify-center mx-auto mb-6 group-hover:rotate-12 transition-transform\">\n                <Star className=\"w-8 h-8 text-white\" />\n              </div>\n              <h4 className=\"text-xl font-bold text-gray-800 mb-4\">\n                {t(\"nursery.features.education\")}\n              </h4>\n              <p className=\"text-gray-600 leading-relaxed\">\n                برامج تعليمية حديثة ومتطورة تناسب جميع المراحل العمرية للأطفال\n              </p>\n            </div>\n\n            {/* Care Feature */}\n            <div className=\"card-nursery p-8 text-center group hover:scale-105\">\n              <div className=\"bg-gradient-to-br from-pink-400 to-pink-600 w-16 h-16 rounded-2xl flex items-center justify-center mx-auto mb-6 group-hover:rotate-12 transition-transform\">\n                <Heart className=\"w-8 h-8 text-white\" />\n              </div>\n              <h4 className=\"text-xl font-bold text-gray-800 mb-4\">\n                {t(\"nursery.features.care\")}\n              </h4>\n              <p className=\"text-gray-600 leading-relaxed\">\n                رعاية شاملة ومحبة من فريق مؤهل ومدرب على أعلى المستويات\n              </p>\n            </div>\n\n            {/* Technology Feature */}\n            <div className=\"card-nursery p-8 text-center group hover:scale-105\">\n              <div className=\"bg-gradient-to-br from-purple-400 to-purple-600 w-16 h-16 rounded-2xl flex items-center justify-center mx-auto mb-6 group-hover:rotate-12 transition-transform\">\n                <Sparkles className=\"w-8 h-8 text-white\" />\n              </div>\n              <h4 className=\"text-xl font-bold text-gray-800 mb-4\">\n                {t(\"nursery.features.technology\")}\n              </h4>\n              <p className=\"text-gray-600 leading-relaxed\">\n                تقنيات حديثة لمتابعة تطور الأطفال والتواصل المستمر مع الأهالي\n              </p>\n            </div>\n          </div>\n        </div>\n      </section>\n\n      {/* How it Works Section */}\n      <section className=\"py-20 bg-gradient-to-br from-purple-50 to-pink-50\">\n        <div className=\"container mx-auto px-4\">\n          <div className=\"text-center mb-16\">\n            <h3 className=\"text-3xl md:text-4xl font-bold text-gray-800 mb-4\">\n              كيف يعمل نظام التصاريح؟\n            </h3>\n            <p className=\"text-xl text-gray-600 max-w-2xl mx-auto\">\n              خطوات بسيطة لضمان أمان استلام أطفالكم\n            </p>\n          </div>\n\n          <div className=\"grid grid-cols-1 md:grid-cols-3 gap-8 max-w-4xl mx-auto\">\n            <div className=\"text-center\">\n              <div className=\"bg-gradient-to-br from-pink-500 to-purple-600 w-20 h-20 rounded-full flex items-center justify-center mx-auto mb-6\">\n                <span className=\"text-2xl font-bold text-white\">1</span>\n              </div>\n              <h4 className=\"text-xl font-bold text-gray-800 mb-4\">\n                إنشاء حساب\n              </h4>\n              <p className=\"text-gray-600\">\n                سجل حسابك وأضف معلومات أطفالك بشكل آمن\n              </p>\n            </div>\n\n            <div className=\"text-center\">\n              <div className=\"bg-gradient-to-br from-purple-500 to-blue-600 w-20 h-20 rounded-full flex items-center justify-center mx-auto mb-6\">\n                <span className=\"text-2xl font-bold text-white\">2</span>\n              </div>\n              <h4 className=\"text-xl font-bold text-gray-800 mb-4\">\n                إنشاء تصريح\n              </h4>\n              <p className=\"text-gray-600\">\n                أنشئ تصريح استلام مع صورة هوية الشخص المخول\n              </p>\n            </div>\n\n            <div className=\"text-center\">\n              <div className=\"bg-gradient-to-br from-blue-500 to-green-600 w-20 h-20 rounded-full flex items-center justify-center mx-auto mb-6\">\n                <span className=\"text-2xl font-bold text-white\">3</span>\n              </div>\n              <h4 className=\"text-xl font-bold text-gray-800 mb-4\">\n                استلام آمن\n              </h4>\n              <p className=\"text-gray-600\">\n                استخدم رمز التصريح لاستلام آمن ومؤكد\n              </p>\n            </div>\n          </div>\n        </div>\n      </section>\n\n      {/* Stats Section */}\n      <section className=\"py-20 bg-white\">\n        <div className=\"container mx-auto px-4\">\n          <div className=\"grid grid-cols-2 md:grid-cols-4 gap-8 text-center\">\n            <div>\n              <div className=\"text-4xl md:text-5xl font-bold bg-gradient-to-r from-pink-600 to-purple-600 bg-clip-text text-transparent mb-2\">\n                500+\n              </div>\n              <p className=\"text-gray-600 font-medium\">عائلة سعيدة</p>\n            </div>\n            <div>\n              <div className=\"text-4xl md:text-5xl font-bold bg-gradient-to-r from-purple-600 to-blue-600 bg-clip-text text-transparent mb-2\">\n                1000+\n              </div>\n              <p className=\"text-gray-600 font-medium\">طفل محبوب</p>\n            </div>\n            <div>\n              <div className=\"text-4xl md:text-5xl font-bold bg-gradient-to-r from-blue-600 to-green-600 bg-clip-text text-transparent mb-2\">\n                99%\n              </div>\n              <p className=\"text-gray-600 font-medium\">رضا الأهالي</p>\n            </div>\n            <div>\n              <div className=\"text-4xl md:text-5xl font-bold bg-gradient-to-r from-green-600 to-pink-600 bg-clip-text text-transparent mb-2\">\n                24/7\n              </div>\n              <p className=\"text-gray-600 font-medium\">دعم مستمر</p>\n            </div>\n          </div>\n        </div>\n      </section>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AASA;;;AAbA;;;;;AAee,SAAS;;IACtB,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAEvD,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;0BAAE;YACR,MAAM,QAAQ,aAAa,OAAO,CAAC;YACnC,mBAAmB,CAAC,CAAC;QACvB;yBAAG,EAAE;IACL,MAAM,EAAE,CAAC,EAAE,GAAG,CAAA,GAAA,iIAAA,CAAA,UAAc,AAAD;IAE3B,qBACE,6LAAC;QAAI,WAAU;;0BAEb,6LAAC;gBAAQ,WAAU;;kCAEjB,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC,qMAAA,CAAA,OAAI;4BAAC,WAAU;;;;;;;;;;;kCAElB,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC,uMAAA,CAAA,QAAK;4BAAC,WAAU;;;;;;;;;;;kCAEnB,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC,6MAAA,CAAA,WAAQ;4BAAC,WAAU;;;;;;;;;;;kCAGtB,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC;4BAAI,WAAU;;8CAEb,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAI,WAAU;sDACb,cAAA,6LAAC,qMAAA,CAAA,OAAI;gDAAC,WAAU;;;;;;;;;;;sDAElB,6LAAC;;8DACC,6LAAC;oDAAG,WAAU;8DAAuE;;;;;;8DAGrF,6LAAC;oDAAE,WAAU;8DAAqD;;;;;;;;;;;;;;;;;;8CAMtE,6LAAC;oCAAG,WAAU;8CAAgE;;;;;;8CAI9E,6LAAC;oCAAE,WAAU;8CAAmF;;;;;;8CAMhG,6LAAC;oCAAI,WAAU;8CACZ,gCACC,6LAAC,+JAAA,CAAA,UAAI;wCACH,MAAK;wCACL,WAAU;;0DAEV,6LAAC,uMAAA,CAAA,QAAK;gDAAC,WAAU;;;;;;0DACjB,6LAAC;0DAAK;;;;;;0DACN,6LAAC,mNAAA,CAAA,YAAS;gDAAC,WAAU;;;;;;;;;;;6DAGvB;;0DACE,6LAAC,+JAAA,CAAA,UAAI;gDACH,MAAK;gDACL,WAAU;;kEAEV,6LAAC,uMAAA,CAAA,QAAK;wDAAC,WAAU;;;;;;kEACjB,6LAAC;kEAAK;;;;;;kEACN,6LAAC,mNAAA,CAAA,YAAS;wDAAC,WAAU;;;;;;;;;;;;0DAEvB,6LAAC,+JAAA,CAAA,UAAI;gDACH,MAAK;gDACL,WAAU;;kEAEV,6LAAC,qMAAA,CAAA,OAAI;wDAAC,WAAU;;;;;;kEAChB,6LAAC;kEAAK;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAUpB,6LAAC;gBAAQ,WAAU;0BACjB,cAAA,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAG,WAAU;8CAAgE;;;;;;8CAG9E,6LAAC;oCAAE,WAAU;8CAAqD;;;;;;;;;;;;sCAKpE,6LAAC;4BAAI,WAAU;;8CAEb,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAI,WAAU;sDACb,cAAA,6LAAC,yMAAA,CAAA,SAAM;gDAAC,WAAU;;;;;;;;;;;sDAEpB,6LAAC;4CAAG,WAAU;sDACX,EAAE;;;;;;sDAEL,6LAAC;4CAAE,WAAU;sDAAgC;;;;;;;;;;;;8CAO/C,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAI,WAAU;sDACb,cAAA,6LAAC,qMAAA,CAAA,OAAI;gDAAC,WAAU;;;;;;;;;;;sDAElB,6LAAC;4CAAG,WAAU;sDACX,EAAE;;;;;;sDAEL,6LAAC;4CAAE,WAAU;sDAAgC;;;;;;;;;;;;8CAM/C,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAI,WAAU;sDACb,cAAA,6LAAC,uMAAA,CAAA,QAAK;gDAAC,WAAU;;;;;;;;;;;sDAEnB,6LAAC;4CAAG,WAAU;sDACX,EAAE;;;;;;sDAEL,6LAAC;4CAAE,WAAU;sDAAgC;;;;;;;;;;;;8CAM/C,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAI,WAAU;sDACb,cAAA,6LAAC,6MAAA,CAAA,WAAQ;gDAAC,WAAU;;;;;;;;;;;sDAEtB,6LAAC;4CAAG,WAAU;sDACX,EAAE;;;;;;sDAEL,6LAAC;4CAAE,WAAU;sDAAgC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BASrD,6LAAC;gBAAQ,WAAU;0BACjB,cAAA,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAG,WAAU;8CAAoD;;;;;;8CAGlE,6LAAC;oCAAE,WAAU;8CAA0C;;;;;;;;;;;;sCAKzD,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAI,WAAU;sDACb,cAAA,6LAAC;gDAAK,WAAU;0DAAgC;;;;;;;;;;;sDAElD,6LAAC;4CAAG,WAAU;sDAAuC;;;;;;sDAGrD,6LAAC;4CAAE,WAAU;sDAAgB;;;;;;;;;;;;8CAK/B,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAI,WAAU;sDACb,cAAA,6LAAC;gDAAK,WAAU;0DAAgC;;;;;;;;;;;sDAElD,6LAAC;4CAAG,WAAU;sDAAuC;;;;;;sDAGrD,6LAAC;4CAAE,WAAU;sDAAgB;;;;;;;;;;;;8CAK/B,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAI,WAAU;sDACb,cAAA,6LAAC;gDAAK,WAAU;0DAAgC;;;;;;;;;;;sDAElD,6LAAC;4CAAG,WAAU;sDAAuC;;;;;;sDAGrD,6LAAC;4CAAE,WAAU;sDAAgB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BASrC,6LAAC;gBAAQ,WAAU;0BACjB,cAAA,6LAAC;oBAAI,WAAU;8BACb,cAAA,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;;kDACC,6LAAC;wCAAI,WAAU;kDAAiH;;;;;;kDAGhI,6LAAC;wCAAE,WAAU;kDAA4B;;;;;;;;;;;;0CAE3C,6LAAC;;kDACC,6LAAC;wCAAI,WAAU;kDAAiH;;;;;;kDAGhI,6LAAC;wCAAE,WAAU;kDAA4B;;;;;;;;;;;;0CAE3C,6LAAC;;kDACC,6LAAC;wCAAI,WAAU;kDAAgH;;;;;;kDAG/H,6LAAC;wCAAE,WAAU;kDAA4B;;;;;;;;;;;;0CAE3C,6LAAC;;kDACC,6LAAC;wCAAI,WAAU;kDAAgH;;;;;;kDAG/H,6LAAC;wCAAE,WAAU;kDAA4B;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAOvD;GA/OwB;;QAOR,iIAAA,CAAA,UAAc;;;KAPN", "debugId": null}}, {"offset": {"line": 786, "column": 0}, "map": {"version": 3, "file": "arrow-left.js", "sources": ["file:///D:/IMPORTANT/work/export/front/node_modules/lucide-react/src/icons/arrow-left.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\nimport { IconNode } from '../types';\n\nexport const __iconNode: IconNode = [\n  ['path', { d: 'm12 19-7-7 7-7', key: '1l729n' }],\n  ['path', { d: 'M19 12H5', key: 'x3x0zl' }],\n];\n\n/**\n * @component @name ArrowLeft\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cGF0aCBkPSJtMTIgMTktNy03IDctNyIgLz4KICA8cGF0aCBkPSJNMTkgMTJINSIgLz4KPC9zdmc+Cg==) - https://lucide.dev/icons/arrow-left\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst ArrowLeft = createLucideIcon('arrow-left', __iconNode);\n\nexport default ArrowLeft;\n"], "names": [], "mappings": ";;;;;;;;;;;AAGO,CAAA,CAAA,CAAA,CAAA,CAAA,CAAM,UAAuB,CAAA,CAAA,CAAA;IAClC;QAAC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAQ,CAAA;QAAA,CAAA;YAAE,GAAG,CAAkB,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YAAA,CAAA,CAAA,CAAA,CAAA,CAAK;QAAA,CAAU;KAAA;IAC/C;QAAC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAQ,CAAA;QAAA,CAAA;YAAE,GAAG,CAAY,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YAAA,CAAA,CAAA,CAAA,CAAA,CAAK;QAAA,CAAU;KAAA;CAC3C;AAaM,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,SAAA,CAAY,CAAA,2KAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,AAAiB,CAAjB,CAAA,AAAiB,CAAjB,AAAiB,CAAjB,AAAiB,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAc,CAAU,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 832, "column": 0}, "map": {"version": 3, "file": "sparkles.js", "sources": ["file:///D:/IMPORTANT/work/export/front/node_modules/lucide-react/src/icons/sparkles.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\nimport { IconNode } from '../types';\n\nexport const __iconNode: IconNode = [\n  [\n    'path',\n    {\n      d: 'M9.937 15.5A2 2 0 0 0 8.5 14.063l-6.135-1.582a.5.5 0 0 1 0-.962L8.5 9.936A2 2 0 0 0 9.937 8.5l1.582-6.135a.5.5 0 0 1 .963 0L14.063 8.5A2 2 0 0 0 15.5 9.937l6.135 1.581a.5.5 0 0 1 0 .964L15.5 14.063a2 2 0 0 0-1.437 1.437l-1.582 6.135a.5.5 0 0 1-.963 0z',\n      key: '4pj2yx',\n    },\n  ],\n  ['path', { d: 'M20 3v4', key: '1olli1' }],\n  ['path', { d: 'M22 5h-4', key: '1gvqau' }],\n  ['path', { d: 'M4 17v2', key: 'vumght' }],\n  ['path', { d: 'M5 18H3', key: 'zchphs' }],\n];\n\n/**\n * @component @name Sparkles\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cGF0aCBkPSJNOS45MzcgMTUuNUEyIDIgMCAwIDAgOC41IDE0LjA2M2wtNi4xMzUtMS41ODJhLjUuNSAwIDAgMSAwLS45NjJMOC41IDkuOTM2QTIgMiAwIDAgMCA5LjkzNyA4LjVsMS41ODItNi4xMzVhLjUuNSAwIDAgMSAuOTYzIDBMMTQuMDYzIDguNUEyIDIgMCAwIDAgMTUuNSA5LjkzN2w2LjEzNSAxLjU4MWEuNS41IDAgMCAxIDAgLjk2NEwxNS41IDE0LjA2M2EyIDIgMCAwIDAtMS40MzcgMS40MzdsLTEuNTgyIDYuMTM1YS41LjUgMCAwIDEtLjk2MyAweiIgLz4KICA8cGF0aCBkPSJNMjAgM3Y0IiAvPgogIDxwYXRoIGQ9Ik0yMiA1aC00IiAvPgogIDxwYXRoIGQ9Ik00IDE3djIiIC8+CiAgPHBhdGggZD0iTTUgMThIMyIgLz4KPC9zdmc+Cg==) - https://lucide.dev/icons/sparkles\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst Sparkles = createLucideIcon('sparkles', __iconNode);\n\nexport default Sparkles;\n"], "names": [], "mappings": ";;;;;;;;;;;AAGO,CAAA,CAAA,CAAA,CAAA,CAAA,CAAM,UAAuB,CAAA,CAAA,CAAA;IAClC;QACE,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QACA;YACE,CAAG,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YACH,CAAA,CAAA,CAAK,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QACP;KACF;IACA;QAAC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAQ,CAAA;QAAA,CAAA;YAAE,GAAG,CAAW,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YAAA,CAAA,CAAA,CAAA,CAAA,CAAK;QAAA,CAAU;KAAA;IACxC;QAAC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAQ,CAAA;QAAA,CAAA;YAAE,GAAG,CAAY,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YAAA,CAAA,CAAA,CAAA,CAAA,CAAK;QAAA,CAAU;KAAA;IACzC;QAAC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAQ,CAAA;QAAA,CAAA;YAAE,GAAG,CAAW,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YAAA,CAAA,CAAA,CAAA,CAAA,CAAK;QAAA,CAAU;KAAA;IACxC;QAAC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAQ,CAAA;QAAA,CAAA;YAAE,GAAG,CAAW,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YAAA,CAAA,CAAA,CAAA,CAAA,CAAK;QAAA,CAAU;KAAA;CAC1C;AAaM,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,QAAA,CAAW,CAAA,2KAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,AAAiB,CAAjB,CAAiB,AAAjB,CAAA,AAAiB,CAAjB,AAAiB,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAY,CAAU,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA", "ignoreList": [0], "debugId": null}}]}
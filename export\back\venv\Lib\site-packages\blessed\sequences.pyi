# std imports
import textwrap
from typing import Any, <PERSON>, <PERSON><PERSON>, <PERSON>tern, <PERSON>V<PERSON>, Iterator, Optional, SupportsIndex

# local
from .terminal import Terminal

_T = TypeVar("_T")

class Termcap:
    name: str = ...
    pattern: str = ...
    attribute: str = ...
    def __init__(self, name: str, pattern: str, attribute: str) -> None: ...
    @property
    def named_pattern(self) -> str: ...
    @property
    def re_compiled(self) -> Pattern[str]: ...
    @property
    def will_move(self) -> bool: ...
    def horizontal_distance(self, text: str) -> int: ...
    @classmethod
    def build(
        cls,
        name: str,
        capability: str,
        attribute: str,
        nparams: int = ...,
        numeric: int = ...,
        match_grouped: bool = ...,
        match_any: bool = ...,
        match_optional: bool = ...,
    ) -> "Termcap": ...

class SequenceTextWrapper(textwrap.TextWrapper):
    term: Terminal = ...
    def __init__(self, width: int, term: Terminal, **kwargs: Any) -> None: ...

class Sequence(str):
    def __new__(cls: Type[_T], sequence_text: str, term: Terminal) -> _T: ...
    def ljust(self, width: SupportsIndex, fillchar: str = ...) -> str: ...
    def rjust(self, width: SupportsIndex, fillchar: str = ...) -> str: ...
    def center(self, width: SupportsIndex, fillchar: str = ...) -> str: ...
    def truncate(self, width: SupportsIndex) -> str: ...
    def length(self) -> int: ...
    def strip(self, chars: Optional[str] = ...) -> str: ...
    def lstrip(self, chars: Optional[str] = ...) -> str: ...
    def rstrip(self, chars: Optional[str] = ...) -> str: ...
    def strip_seqs(self) -> str: ...
    def padd(self, strip: bool = ...) -> str: ...

def iter_parse(
    term: Terminal, text: str
) -> Iterator[Tuple[str, Optional[Termcap]]]: ...
def measure_length(text: str, term: Terminal) -> int: ...

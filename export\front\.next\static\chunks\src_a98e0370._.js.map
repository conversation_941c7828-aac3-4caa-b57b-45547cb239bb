{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/IMPORTANT/work/export/front/src/app/admin/components/AdminSidebar.tsx"], "sourcesContent": ["\"use client\";\n\nimport React from \"react\";\nimport { Users, Shield, User, Lock, Settings } from \"lucide-react\";\n\ninterface SidebarItem {\n  label: string;\n  href: string;\n  icon: React.ReactNode;\n}\n\ninterface AdminSidebarProps {\n  activeTab: \"users\" | \"permissions\" | \"profile\" | \"password\" | \"settings\";\n  setActiveTab: (\n    tab: \"users\" | \"permissions\" | \"profile\" | \"password\" | \"settings\"\n  ) => void;\n  isSuperAdmin: boolean;\n  isNormalAdmin: boolean;\n}\n\nconst sidebarItems: SidebarItem[] = [\n  {\n    label: \"Users\",\n    href: \"/admin/users\",\n    icon: <Users className=\"w-5 h-5\" />,\n  },\n  {\n    label: \"Permissions\",\n    href: \"/admin/permissions\",\n    icon: <Shield className=\"w-5 h-5\" />,\n  },\n  {\n    label: \"Profile\",\n    href: \"/admin/profile\",\n    icon: <User className=\"w-5 h-5\" />,\n  },\n  {\n    label: \"Password\",\n    href: \"/admin/password\",\n    icon: <Lock className=\"w-5 h-5\" />,\n  },\n  {\n    label: \"Settings\",\n    href: \"/admin/settings\",\n    icon: <Settings className=\"w-5 h-5\" />,\n  },\n];\n\nconst AdminSidebar: React.FC<AdminSidebarProps> = ({\n  activeTab,\n  setActiveTab,\n  isSuperAdmin,\n  isNormalAdmin,\n}) => {\n  const getTabFromHref = (\n    href: string\n  ): \"users\" | \"permissions\" | \"profile\" | \"password\" | \"settings\" => {\n    if (href.includes(\"users\")) return \"users\";\n    if (href.includes(\"permissions\")) return \"permissions\";\n    if (href.includes(\"profile\")) return \"profile\";\n    if (href.includes(\"password\")) return \"password\";\n    if (href.includes(\"settings\")) return \"settings\";\n    return \"users\";\n  };\n\n  // Filter sidebar items based on admin type\n  const getFilteredSidebarItems = () => {\n    if (isSuperAdmin) {\n      // Super admin sees all items\n      return sidebarItems;\n    } else if (isNormalAdmin) {\n      // Normal admin only sees permissions, profile, and password\n      return sidebarItems.filter(\n        (item) =>\n          item.href.includes(\"permissions\") ||\n          item.href.includes(\"profile\") ||\n          item.href.includes(\"password\")\n      );\n    }\n    return sidebarItems;\n  };\n\n  const filteredItems = getFilteredSidebarItems();\n\n  return (\n    <div className=\"bg-white rounded-2xl shadow-xl border border-gray-100 overflow-hidden\">\n      {/* Header */}\n      <div className=\"bg-gradient-to-r from-slate-900 to-slate-700 px-6 py-4\">\n        <h2 className=\"text-xl font-bold text-white\">Admin Panel</h2>\n        <p className=\"text-sm text-slate-200 mt-1\">System Administration</p>\n      </div>\n\n      {/* Navigation */}\n      <nav className=\"p-2\">\n        {filteredItems.map((item) => {\n          const tabKey = getTabFromHref(item.href);\n          const isActive = activeTab === tabKey;\n\n          return (\n            <button\n              key={item.href}\n              onClick={() => setActiveTab(tabKey)}\n              className={`w-full flex items-center px-4 py-3 mb-1 rounded-xl text-left transition-all duration-200 ${\n                isActive\n                  ? \"bg-gradient-to-r from-slate-700 to-slate-900 text-white shadow-lg\"\n                  : \"text-gray-700 hover:bg-gray-100\"\n              }`}\n            >\n              <span\n                className={`${isActive ? \"text-white\" : \"text-gray-400\"} mr-3`}\n              >\n                {item.icon}\n              </span>\n              <span className=\"font-medium\">{item.label}</span>\n            </button>\n          );\n        })}\n      </nav>\n\n      {/* Footer */}\n      <div className=\"px-6 py-4 bg-gray-50 border-t border-gray-100\">\n        <p className=\"text-xs text-gray-500 text-center\">\n          Admin Dashboard v1.0\n        </p>\n      </div>\n    </div>\n  );\n};\n\nexport default AdminSidebar;\n"], "names": [], "mappings": ";;;;AAGA;AAAA;AAAA;AAAA;AAAA;AAHA;;;AAoBA,MAAM,eAA8B;IAClC;QACE,OAAO;QACP,MAAM;QACN,oBAAM,6LAAC,uMAAA,CAAA,QAAK;YAAC,WAAU;;;;;;IACzB;IACA;QACE,OAAO;QACP,MAAM;QACN,oBAAM,6LAAC,yMAAA,CAAA,SAAM;YAAC,WAAU;;;;;;IAC1B;IACA;QACE,OAAO;QACP,MAAM;QACN,oBAAM,6LAAC,qMAAA,CAAA,OAAI;YAAC,WAAU;;;;;;IACxB;IACA;QACE,OAAO;QACP,MAAM;QACN,oBAAM,6LAAC,qMAAA,CAAA,OAAI;YAAC,WAAU;;;;;;IACxB;IACA;QACE,OAAO;QACP,MAAM;QACN,oBAAM,6LAAC,6MAAA,CAAA,WAAQ;YAAC,WAAU;;;;;;IAC5B;CACD;AAED,MAAM,eAA4C,CAAC,EACjD,SAAS,EACT,YAAY,EACZ,YAAY,EACZ,aAAa,EACd;IACC,MAAM,iBAAiB,CACrB;QAEA,IAAI,KAAK,QAAQ,CAAC,UAAU,OAAO;QACnC,IAAI,KAAK,QAAQ,CAAC,gBAAgB,OAAO;QACzC,IAAI,KAAK,QAAQ,CAAC,YAAY,OAAO;QACrC,IAAI,KAAK,QAAQ,CAAC,aAAa,OAAO;QACtC,IAAI,KAAK,QAAQ,CAAC,aAAa,OAAO;QACtC,OAAO;IACT;IAEA,2CAA2C;IAC3C,MAAM,0BAA0B;QAC9B,IAAI,cAAc;YAChB,6BAA6B;YAC7B,OAAO;QACT,OAAO,IAAI,eAAe;YACxB,4DAA4D;YAC5D,OAAO,aAAa,MAAM,CACxB,CAAC,OACC,KAAK,IAAI,CAAC,QAAQ,CAAC,kBACnB,KAAK,IAAI,CAAC,QAAQ,CAAC,cACnB,KAAK,IAAI,CAAC,QAAQ,CAAC;QAEzB;QACA,OAAO;IACT;IAEA,MAAM,gBAAgB;IAEtB,qBACE,6LAAC;QAAI,WAAU;;0BAEb,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAG,WAAU;kCAA+B;;;;;;kCAC7C,6LAAC;wBAAE,WAAU;kCAA8B;;;;;;;;;;;;0BAI7C,6LAAC;gBAAI,WAAU;0BACZ,cAAc,GAAG,CAAC,CAAC;oBAClB,MAAM,SAAS,eAAe,KAAK,IAAI;oBACvC,MAAM,WAAW,cAAc;oBAE/B,qBACE,6LAAC;wBAEC,SAAS,IAAM,aAAa;wBAC5B,WAAW,CAAC,yFAAyF,EACnG,WACI,sEACA,mCACJ;;0CAEF,6LAAC;gCACC,WAAW,GAAG,WAAW,eAAe,gBAAgB,KAAK,CAAC;0CAE7D,KAAK,IAAI;;;;;;0CAEZ,6LAAC;gCAAK,WAAU;0CAAe,KAAK,KAAK;;;;;;;uBAbpC,KAAK,IAAI;;;;;gBAgBpB;;;;;;0BAIF,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBAAE,WAAU;8BAAoC;;;;;;;;;;;;;;;;;AAMzD;KA/EM;uCAiFS", "debugId": null}}, {"offset": {"line": 197, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/IMPORTANT/work/export/front/src/services/adminService.ts"], "sourcesContent": ["const BACKEND_URL = \"http://localhost:8000\";\n\n// Helper function to get auth headers\nconst getAuthHeaders = () => {\n  const token = localStorage.getItem(\"access_token\");\n  return {\n    Authorization: `Bearer ${token}`,\n  };\n};\n\n// Check admin status\nexport const checkAdminStatus = async () => {\n  const response = await fetch(`${BACKEND_URL}/api/admin/check-status/`, {\n    headers: getAuthHeaders(),\n  });\n\n  if (!response.ok) {\n    throw new Error(\"Failed to check admin status\");\n  }\n\n  return await response.json();\n};\n\n// Get all users\nexport const getAllUsers = async () => {\n  const response = await fetch(`${BACKEND_URL}/api/admin/users/`, {\n    headers: getAuthHeaders(),\n  });\n\n  if (!response.ok) {\n    throw new Error(\"Failed to fetch users\");\n  }\n\n  return await response.json();\n};\n\n// Approve user\nexport const approveUser = async (userId: number) => {\n  const response = await fetch(`${BACKEND_URL}/api/admin/users/${userId}/approve/`, {\n    method: \"POST\",\n    headers: getAuthHeaders(),\n  });\n\n  if (!response.ok) {\n    throw new Error(\"Failed to approve user\");\n  }\n\n  return await response.json();\n};\n\n// Delete user\nexport const deleteUser = async (userId: number) => {\n  const response = await fetch(`${BACKEND_URL}/api/admin/users/${userId}/delete/`, {\n    method: \"DELETE\",\n    headers: getAuthHeaders(),\n  });\n\n  if (!response.ok) {\n    throw new Error(\"Failed to delete user\");\n  }\n\n  return await response.json();\n};\n\n// Get all permissions\nexport const getAllPermissions = async () => {\n  const response = await fetch(`${BACKEND_URL}/api/admin/permissions/`, {\n    headers: getAuthHeaders(),\n  });\n\n  if (!response.ok) {\n    throw new Error(\"Failed to fetch permissions\");\n  }\n\n  return await response.json();\n};\n\n// Approve permission\nexport const approvePermission = async (permissionId: number) => {\n  const response = await fetch(`${BACKEND_URL}/api/admin/permissions/${permissionId}/approve/`, {\n    method: \"POST\",\n    headers: getAuthHeaders(),\n  });\n\n  if (!response.ok) {\n    throw new Error(\"Failed to approve permission\");\n  }\n\n  return await response.json();\n};\n\n// Decline permission\nexport const declinePermission = async (permissionId: number) => {\n  const response = await fetch(`${BACKEND_URL}/api/admin/permissions/${permissionId}/decline/`, {\n    method: \"POST\",\n    headers: getAuthHeaders(),\n  });\n\n  if (!response.ok) {\n    throw new Error(\"Failed to decline permission\");\n  }\n\n  return await response.json();\n};\n\n// Cleanup expired permissions\nexport const cleanupExpiredPermissions = async () => {\n  const response = await fetch(`${BACKEND_URL}/api/admin/permissions/cleanup-expired/`, {\n    method: \"DELETE\",\n    headers: getAuthHeaders(),\n  });\n\n  if (!response.ok) {\n    throw new Error(\"Failed to cleanup expired permissions\");\n  }\n\n  return await response.json();\n};\n"], "names": [], "mappings": ";;;;;;;;;;AAAA,MAAM,cAAc;AAEpB,sCAAsC;AACtC,MAAM,iBAAiB;IACrB,MAAM,QAAQ,aAAa,OAAO,CAAC;IACnC,OAAO;QACL,eAAe,CAAC,OAAO,EAAE,OAAO;IAClC;AACF;AAGO,MAAM,mBAAmB;IAC9B,MAAM,WAAW,MAAM,MAAM,GAAG,YAAY,wBAAwB,CAAC,EAAE;QACrE,SAAS;IACX;IAEA,IAAI,CAAC,SAAS,EAAE,EAAE;QAChB,MAAM,IAAI,MAAM;IAClB;IAEA,OAAO,MAAM,SAAS,IAAI;AAC5B;AAGO,MAAM,cAAc;IACzB,MAAM,WAAW,MAAM,MAAM,GAAG,YAAY,iBAAiB,CAAC,EAAE;QAC9D,SAAS;IACX;IAEA,IAAI,CAAC,SAAS,EAAE,EAAE;QAChB,MAAM,IAAI,MAAM;IAClB;IAEA,OAAO,MAAM,SAAS,IAAI;AAC5B;AAGO,MAAM,cAAc,OAAO;IAChC,MAAM,WAAW,MAAM,MAAM,GAAG,YAAY,iBAAiB,EAAE,OAAO,SAAS,CAAC,EAAE;QAChF,QAAQ;QACR,SAAS;IACX;IAEA,IAAI,CAAC,SAAS,EAAE,EAAE;QAChB,MAAM,IAAI,MAAM;IAClB;IAEA,OAAO,MAAM,SAAS,IAAI;AAC5B;AAGO,MAAM,aAAa,OAAO;IAC/B,MAAM,WAAW,MAAM,MAAM,GAAG,YAAY,iBAAiB,EAAE,OAAO,QAAQ,CAAC,EAAE;QAC/E,QAAQ;QACR,SAAS;IACX;IAEA,IAAI,CAAC,SAAS,EAAE,EAAE;QAChB,MAAM,IAAI,MAAM;IAClB;IAEA,OAAO,MAAM,SAAS,IAAI;AAC5B;AAGO,MAAM,oBAAoB;IAC/B,MAAM,WAAW,MAAM,MAAM,GAAG,YAAY,uBAAuB,CAAC,EAAE;QACpE,SAAS;IACX;IAEA,IAAI,CAAC,SAAS,EAAE,EAAE;QAChB,MAAM,IAAI,MAAM;IAClB;IAEA,OAAO,MAAM,SAAS,IAAI;AAC5B;AAGO,MAAM,oBAAoB,OAAO;IACtC,MAAM,WAAW,MAAM,MAAM,GAAG,YAAY,uBAAuB,EAAE,aAAa,SAAS,CAAC,EAAE;QAC5F,QAAQ;QACR,SAAS;IACX;IAEA,IAAI,CAAC,SAAS,EAAE,EAAE;QAChB,MAAM,IAAI,MAAM;IAClB;IAEA,OAAO,MAAM,SAAS,IAAI;AAC5B;AAGO,MAAM,oBAAoB,OAAO;IACtC,MAAM,WAAW,MAAM,MAAM,GAAG,YAAY,uBAAuB,EAAE,aAAa,SAAS,CAAC,EAAE;QAC5F,QAAQ;QACR,SAAS;IACX;IAEA,IAAI,CAAC,SAAS,EAAE,EAAE;QAChB,MAAM,IAAI,MAAM;IAClB;IAEA,OAAO,MAAM,SAAS,IAAI;AAC5B;AAGO,MAAM,4BAA4B;IACvC,MAAM,WAAW,MAAM,MAAM,GAAG,YAAY,uCAAuC,CAAC,EAAE;QACpF,QAAQ;QACR,SAAS;IACX;IAEA,IAAI,CAAC,SAAS,EAAE,EAAE;QAChB,MAAM,IAAI,MAAM;IAClB;IAEA,OAAO,MAAM,SAAS,IAAI;AAC5B", "debugId": null}}, {"offset": {"line": 301, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/IMPORTANT/work/export/front/src/config.ts"], "sourcesContent": ["// import { useSelector } from \"react-redux\";\r\n\r\n// export const BACKEND_URL = \"https://api.uud.io\";\r\nexport const BACKEND_URL: string = \"http://127.0.0.1:8000\";\r\n// const lang = useSelector((state) => state.lang);\r\n// structure layout\r\n"], "names": [], "mappings": "AAAA,6CAA6C;AAE7C,mDAAmD;;;;AAC5C,MAAM,cAAsB,yBACnC,mDAAmD;CACnD,mBAAmB", "debugId": null}}, {"offset": {"line": 317, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/IMPORTANT/work/export/front/src/app/admin/components/AdminUsersManagement.tsx"], "sourcesContent": ["\"use client\";\n\nimport React, { useState, useEffect } from \"react\";\nimport { toast } from \"react-toastify\";\nimport {\n  Loader2,\n  CheckCircle,\n  Trash2,\n  User,\n  Mail,\n  Phone,\n  MapPin,\n  Calendar,\n  Shield,\n  AlertTriangle,\n  X,\n} from \"lucide-react\";\nimport Image from \"next/image\";\nimport { AdminUserData } from \"@/types/account\";\nimport { getAllUsers, approveUser, deleteUser } from \"@/services/adminService\";\nimport { BACKEND_URL } from \"@/config\";\n\nconst AdminUsersManagement: React.FC = () => {\n  const [users, setUsers] = useState<AdminUserData[]>([]);\n  const [loading, setLoading] = useState(true);\n  const [actionLoading, setActionLoading] = useState<{\n    [key: number]: boolean;\n  }>({});\n  const [selectedUser, setSelectedUser] = useState<AdminUserData | null>(null);\n  const [showUserModal, setShowUserModal] = useState(false);\n  const [selectedImage, setSelectedImage] = useState<string | null>(null);\n  const [showImageModal, setShowImageModal] = useState(false);\n\n  useEffect(() => {\n    fetchUsers();\n  }, []);\n\n  const fetchUsers = async () => {\n    setLoading(true);\n    try {\n      const data = await getAllUsers();\n      setUsers(data);\n    } catch (error) {\n      console.error(\"Error fetching users:\", error);\n      toast.error(\"Failed to fetch users\", {\n        autoClose: false,\n        hideProgressBar: true,\n        closeOnClick: true,\n        pauseOnHover: false,\n        draggable: true,\n      });\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  const handleApproveUser = async (userId: number) => {\n    setActionLoading({ ...actionLoading, [userId]: true });\n    try {\n      const data = await approveUser(userId);\n      toast.success(data.message, {\n        autoClose: 1500,\n        hideProgressBar: true,\n        closeOnClick: true,\n        pauseOnHover: false,\n        draggable: true,\n      });\n      fetchUsers(); // Refresh the list\n    } catch (error) {\n      console.error(\"Error approving user:\", error);\n      toast.error(\"Failed to approve user\", {\n        autoClose: false,\n        hideProgressBar: true,\n        closeOnClick: true,\n        pauseOnHover: false,\n        draggable: true,\n      });\n    } finally {\n      setActionLoading({ ...actionLoading, [userId]: false });\n    }\n  };\n\n  const handleDeleteUser = async (userId: number, userEmail: string) => {\n    if (\n      !confirm(\n        `Are you sure you want to delete user ${userEmail}? This action cannot be undone.`\n      )\n    ) {\n      return;\n    }\n\n    setActionLoading({ ...actionLoading, [userId]: true });\n    try {\n      const data = await deleteUser(userId);\n      toast.success(data.message, {\n        autoClose: 1500,\n        hideProgressBar: true,\n        closeOnClick: true,\n        pauseOnHover: false,\n        draggable: true,\n      });\n      fetchUsers(); // Refresh the list\n    } catch (error) {\n      console.error(\"Error deleting user:\", error);\n      toast.error(\"Failed to delete user\", {\n        autoClose: false,\n        hideProgressBar: true,\n        closeOnClick: true,\n        pauseOnHover: false,\n        draggable: true,\n      });\n    } finally {\n      setActionLoading({ ...actionLoading, [userId]: false });\n    }\n  };\n\n  const handleUserClick = (user: AdminUserData) => {\n    setSelectedUser(user);\n    setShowUserModal(true);\n  };\n\n  const handleImageClick = (imageUrl: string) => {\n    setSelectedImage(imageUrl);\n    setShowImageModal(true);\n  };\n\n  const closeUserModal = () => {\n    setShowUserModal(false);\n    setSelectedUser(null);\n  };\n\n  const closeImageModal = () => {\n    setShowImageModal(false);\n    setSelectedImage(null);\n  };\n\n  if (loading) {\n    return (\n      <div className=\"flex items-center justify-center py-8\">\n        <div className=\"text-center\">\n          <Loader2 className=\"w-8 h-8 animate-spin mx-auto text-slate-600\" />\n          <p className=\"mt-4 text-gray-600\">Loading users...</p>\n        </div>\n      </div>\n    );\n  }\n\n  return (\n    <div className=\"space-y-6\">\n      {/* Header */}\n      <div className=\"flex justify-between items-center\">\n        <div>\n          <h4 className=\"text-lg font-semibold text-gray-900\">All Users</h4>\n          <p className=\"text-sm text-gray-600\">\n            Manage user accounts and approvals\n          </p>\n        </div>\n        <div className=\"text-sm text-gray-500\">Total: {users.length} users</div>\n      </div>\n\n      {/* Users List */}\n      <div className=\"space-y-4\">\n        {users.length === 0 ? (\n          <div className=\"text-center py-8\">\n            <User className=\"w-12 h-12 text-gray-400 mx-auto mb-4\" />\n            <p className=\"text-gray-500\">No users found.</p>\n          </div>\n        ) : (\n          users.map((user) => (\n            <div\n              key={user.id}\n              className=\"border border-gray-200 rounded-xl p-6 bg-gradient-to-br from-white to-gray-50\"\n            >\n              <div className=\"flex justify-between items-start\">\n                <div className=\"flex-1\">\n                  <div className=\"flex items-center mb-3\">\n                    <div className=\"flex items-center\">\n                      {user.id_image ? (\n                        <Image\n                          src={`${BACKEND_URL}/${user.id_image}`}\n                          alt=\"User ID\"\n                          width={40}\n                          height={40}\n                          className=\"rounded-full object-cover mr-3 cursor-pointer hover:opacity-80 transition-opacity\"\n                          onClick={() =>\n                            handleImageClick(`${BACKEND_URL}/${user.id_image}`)\n                          }\n                        />\n                      ) : (\n                        <div className=\"w-10 h-10 bg-gray-200 rounded-full flex items-center justify-center mr-3\">\n                          <User className=\"w-5 h-5 text-gray-500\" />\n                        </div>\n                      )}\n                      <div>\n                        <h6\n                          className=\"font-semibold text-gray-900 cursor-pointer hover:text-blue-600 transition-colors\"\n                          onClick={() => handleUserClick(user)}\n                        >\n                          {user.first_name} {user.last_name}\n                        </h6>\n                        <div className=\"flex items-center space-x-2\">\n                          <span\n                            className={`px-2 py-1 text-xs rounded-full ${\n                              user.is_approved\n                                ? \"bg-green-100 text-green-800\"\n                                : \"bg-yellow-100 text-yellow-800\"\n                            }`}\n                          >\n                            {user.is_approved ? \"Approved\" : \"Pending\"}\n                          </span>\n                          {user.is_admin && (\n                            <span className=\"px-2 py-1 text-xs rounded-full bg-blue-100 text-blue-800\">\n                              <Shield className=\"w-3 h-3 inline mr-1\" />\n                              Admin\n                            </span>\n                          )}\n                          {!user.is_email_verified && (\n                            <span className=\"px-2 py-1 text-xs rounded-full bg-red-100 text-red-800\">\n                              <AlertTriangle className=\"w-3 h-3 inline mr-1\" />\n                              Unverified\n                            </span>\n                          )}\n                        </div>\n                      </div>\n                    </div>\n                  </div>\n\n                  <div className=\"grid grid-cols-1 md:grid-cols-2 gap-4 text-sm text-gray-600\">\n                    <div className=\"flex items-center\">\n                      <Mail className=\"w-4 h-4 mr-2 text-gray-400\" />\n                      {user.email}\n                    </div>\n                    <div className=\"flex items-center\">\n                      <Phone className=\"w-4 h-4 mr-2 text-gray-400\" />\n                      {user.phone_number || \"Not provided\"}\n                    </div>\n                    <div className=\"flex items-center\">\n                      <MapPin className=\"w-4 h-4 mr-2 text-gray-400\" />\n                      {user.city && user.governorate\n                        ? `${user.city}, ${user.governorate}`\n                        : \"Location not provided\"}\n                    </div>\n                    <div className=\"flex items-center\">\n                      <Calendar className=\"w-4 h-4 mr-2 text-gray-400\" />\n                      Joined: {new Date(user.date_joined).toLocaleDateString()}\n                    </div>\n                  </div>\n\n                  {user.children && user.children.length > 0 && (\n                    <div className=\"mt-3\">\n                      <p className=\"text-sm font-medium text-gray-700 mb-1\">\n                        Children:\n                      </p>\n                      <div className=\"flex flex-wrap gap-2\">\n                        {user.children.map((child, index) => (\n                          <span\n                            key={index}\n                            className=\"px-2 py-1 text-xs bg-blue-50 text-blue-700 rounded-md\"\n                          >\n                            {child.name} ({child.class} - {child.stage})\n                          </span>\n                        ))}\n                      </div>\n                    </div>\n                  )}\n\n                  {user.approved_by_email && (\n                    <div className=\"mt-3 text-sm text-gray-500\">\n                      Approved by: {user.approved_by_email} on{\" \"}\n                      {user.approved_at\n                        ? new Date(user.approved_at).toLocaleDateString()\n                        : \"N/A\"}\n                    </div>\n                  )}\n                </div>\n\n                {/* Action Buttons */}\n                <div className=\"ml-4 flex flex-col space-y-2\">\n                  {!user.is_approved && (\n                    <button\n                      onClick={() => handleApproveUser(user.id)}\n                      disabled={actionLoading[user.id]}\n                      className=\"flex items-center px-3 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors duration-200 text-sm\"\n                    >\n                      {actionLoading[user.id] ? (\n                        <Loader2 className=\"w-4 h-4 animate-spin\" />\n                      ) : (\n                        <CheckCircle className=\"w-4 h-4\" />\n                      )}\n                      <span className=\"ml-1\">Approve</span>\n                    </button>\n                  )}\n\n                  {!user.is_admin && (\n                    <button\n                      onClick={() => handleDeleteUser(user.id, user.email)}\n                      disabled={actionLoading[user.id]}\n                      className=\"flex items-center px-3 py-2 bg-red-600 text-white rounded-lg hover:bg-red-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors duration-200 text-sm\"\n                    >\n                      {actionLoading[user.id] ? (\n                        <Loader2 className=\"w-4 h-4 animate-spin\" />\n                      ) : (\n                        <Trash2 className=\"w-4 h-4\" />\n                      )}\n                      <span className=\"ml-1\">Delete</span>\n                    </button>\n                  )}\n                </div>\n              </div>\n            </div>\n          ))\n        )}\n      </div>\n\n      {/* User Details Modal */}\n      {showUserModal && selectedUser && (\n        <div className=\"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4\">\n          <div className=\"bg-white rounded-2xl max-w-2xl w-full max-h-[90vh] overflow-y-auto\">\n            {/* Modal Header */}\n            <div className=\"bg-gradient-to-r from-slate-900 to-slate-700 px-6 py-4 rounded-t-2xl\">\n              <div className=\"flex justify-between items-center\">\n                <h3 className=\"text-xl font-bold text-white\">User Details</h3>\n                <button\n                  onClick={closeUserModal}\n                  className=\"text-white hover:text-gray-300 transition-colors\"\n                >\n                  <X className=\"w-6 h-6\" />\n                </button>\n              </div>\n            </div>\n\n            {/* Modal Content */}\n            <div className=\"p-6\">\n              {/* User Info */}\n              <div className=\"flex items-center mb-6\">\n                {selectedUser.id_image ? (\n                  <Image\n                    src={`${BACKEND_URL}/${selectedUser.id_image}`}\n                    alt=\"User ID\"\n                    width={80}\n                    height={80}\n                    className=\"rounded-full object-cover mr-4 cursor-pointer hover:opacity-80 transition-opacity\"\n                    onClick={() =>\n                      handleImageClick(\n                        `${BACKEND_URL}/${selectedUser.id_image}`\n                      )\n                    }\n                  />\n                ) : (\n                  <div className=\"w-20 h-20 bg-gray-200 rounded-full flex items-center justify-center mr-4\">\n                    <User className=\"w-10 h-10 text-gray-500\" />\n                  </div>\n                )}\n                <div>\n                  <h4 className=\"text-2xl font-bold text-gray-900\">\n                    {selectedUser.first_name} {selectedUser.last_name}\n                  </h4>\n                  <div className=\"flex items-center space-x-2 mt-2\">\n                    <span\n                      className={`px-3 py-1 text-sm rounded-full ${\n                        selectedUser.is_approved\n                          ? \"bg-green-100 text-green-800\"\n                          : \"bg-yellow-100 text-yellow-800\"\n                      }`}\n                    >\n                      {selectedUser.is_approved ? \"Approved\" : \"Pending\"}\n                    </span>\n                    {selectedUser.is_admin && (\n                      <span className=\"px-3 py-1 text-sm rounded-full bg-blue-100 text-blue-800\">\n                        <Shield className=\"w-4 h-4 inline mr-1\" />\n                        Admin\n                      </span>\n                    )}\n                    {!selectedUser.is_email_verified && (\n                      <span className=\"px-3 py-1 text-sm rounded-full bg-red-100 text-red-800\">\n                        <AlertTriangle className=\"w-4 h-4 inline mr-1\" />\n                        Unverified\n                      </span>\n                    )}\n                  </div>\n                </div>\n              </div>\n\n              {/* Contact Information */}\n              <div className=\"grid grid-cols-1 md:grid-cols-2 gap-6 mb-6\">\n                <div className=\"space-y-4\">\n                  <h5 className=\"text-lg font-semibold text-gray-900\">\n                    Contact Information\n                  </h5>\n                  <div className=\"space-y-3\">\n                    <div className=\"flex items-center\">\n                      <Mail className=\"w-5 h-5 mr-3 text-gray-400\" />\n                      <span className=\"text-gray-700\">\n                        {selectedUser.email}\n                      </span>\n                    </div>\n                    <div className=\"flex items-center\">\n                      <Phone className=\"w-5 h-5 mr-3 text-gray-400\" />\n                      <span className=\"text-gray-700\">\n                        {selectedUser.phone_number || \"Not provided\"}\n                      </span>\n                    </div>\n                    <div className=\"flex items-center\">\n                      <MapPin className=\"w-5 h-5 mr-3 text-gray-400\" />\n                      <span className=\"text-gray-700\">\n                        {selectedUser.city && selectedUser.governorate\n                          ? `${selectedUser.city}, ${selectedUser.governorate}`\n                          : \"Location not provided\"}\n                      </span>\n                    </div>\n                    <div className=\"flex items-center\">\n                      <Calendar className=\"w-5 h-5 mr-3 text-gray-400\" />\n                      <span className=\"text-gray-700\">\n                        Joined:{\" \"}\n                        {new Date(\n                          selectedUser.date_joined\n                        ).toLocaleDateString()}\n                      </span>\n                    </div>\n                  </div>\n                </div>\n\n                {/* Children Information */}\n                <div className=\"space-y-4\">\n                  <h5 className=\"text-lg font-semibold text-gray-900\">\n                    Children\n                  </h5>\n                  {selectedUser.children && selectedUser.children.length > 0 ? (\n                    <div className=\"space-y-3\">\n                      {selectedUser.children.map((child, index) => (\n                        <div\n                          key={index}\n                          className=\"bg-blue-50 p-3 rounded-lg border border-blue-200\"\n                        >\n                          <div className=\"font-medium text-blue-900\">\n                            {child.name}\n                          </div>\n                          <div className=\"text-sm text-blue-700\">\n                            Class: {child.class} | Stage: {child.stage}\n                          </div>\n                        </div>\n                      ))}\n                    </div>\n                  ) : (\n                    <p className=\"text-gray-500\">No children registered</p>\n                  )}\n                </div>\n              </div>\n\n              {/* Approval Information */}\n              {selectedUser.approved_by_email && (\n                <div className=\"bg-green-50 p-4 rounded-lg border border-green-200\">\n                  <h5 className=\"text-lg font-semibold text-green-900 mb-2\">\n                    Approval Information\n                  </h5>\n                  <p className=\"text-green-700\">\n                    Approved by:{\" \"}\n                    <span className=\"font-medium\">\n                      {selectedUser.approved_by_email}\n                    </span>\n                  </p>\n                  <p className=\"text-green-700\">\n                    Approved on:{\" \"}\n                    {selectedUser.approved_at\n                      ? new Date(selectedUser.approved_at).toLocaleDateString()\n                      : \"N/A\"}\n                  </p>\n                </div>\n              )}\n            </div>\n          </div>\n        </div>\n      )}\n\n      {/* Enhanced Image Modal with Better Close Button */}\n      {showImageModal && selectedImage && (\n        <div className=\"fixed inset-0 bg-black bg-opacity-90 flex items-center justify-center z-50 p-4\">\n          <div className=\"relative max-w-4xl max-h-[90vh] w-full h-full flex items-center justify-center\">\n            {/* Enhanced Close Button */}\n            <button\n              onClick={closeImageModal}\n              className=\"absolute top-4 right-4 z-20 bg-white bg-opacity-20 backdrop-blur-sm hover:bg-opacity-30 text-white border-2 border-white border-opacity-50 hover:border-opacity-100 rounded-full p-3 transition-all duration-200 shadow-lg\"\n              style={{\n                background: \"rgba(255, 255, 255, 0.1)\",\n                backdropFilter: \"blur(10px)\",\n              }}\n            >\n              <X className=\"w-6 h-6 stroke-2\" />\n            </button>\n\n            {/* Image Container */}\n            <div className=\"relative bg-white rounded-lg shadow-2xl overflow-hidden max-w-full max-h-full\">\n              <Image\n                src={selectedImage}\n                alt=\"User ID - Full Size\"\n                width={800}\n                height={600}\n                className=\"max-w-full max-h-[85vh] object-contain\"\n                style={{ width: \"auto\", height: \"auto\" }}\n              />\n            </div>\n\n            {/* Alternative Close Area - Click outside */}\n            <div className=\"absolute inset-0 -z-10\" onClick={closeImageModal} />\n          </div>\n        </div>\n      )}\n    </div>\n  );\n};\n\nexport default AdminUsersManagement;\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAaA;AAEA;AACA;;;AApBA;;;;;;;AAsBA,MAAM,uBAAiC;;IACrC,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAmB,EAAE;IACtD,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAE9C,CAAC;IACJ,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAwB;IACvE,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACnD,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAiB;IAClE,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAErD,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;0CAAE;YACR;QACF;yCAAG,EAAE;IAEL,MAAM,aAAa;QACjB,WAAW;QACX,IAAI;YACF,MAAM,OAAO,MAAM,CAAA,GAAA,kIAAA,CAAA,cAAW,AAAD;YAC7B,SAAS;QACX,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,yBAAyB;YACvC,sJAAA,CAAA,QAAK,CAAC,KAAK,CAAC,yBAAyB;gBACnC,WAAW;gBACX,iBAAiB;gBACjB,cAAc;gBACd,cAAc;gBACd,WAAW;YACb;QACF,SAAU;YACR,WAAW;QACb;IACF;IAEA,MAAM,oBAAoB,OAAO;QAC/B,iBAAiB;YAAE,GAAG,aAAa;YAAE,CAAC,OAAO,EAAE;QAAK;QACpD,IAAI;YACF,MAAM,OAAO,MAAM,CAAA,GAAA,kIAAA,CAAA,cAAW,AAAD,EAAE;YAC/B,sJAAA,CAAA,QAAK,CAAC,OAAO,CAAC,KAAK,OAAO,EAAE;gBAC1B,WAAW;gBACX,iBAAiB;gBACjB,cAAc;gBACd,cAAc;gBACd,WAAW;YACb;YACA,cAAc,mBAAmB;QACnC,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,yBAAyB;YACvC,sJAAA,CAAA,QAAK,CAAC,KAAK,CAAC,0BAA0B;gBACpC,WAAW;gBACX,iBAAiB;gBACjB,cAAc;gBACd,cAAc;gBACd,WAAW;YACb;QACF,SAAU;YACR,iBAAiB;gBAAE,GAAG,aAAa;gBAAE,CAAC,OAAO,EAAE;YAAM;QACvD;IACF;IAEA,MAAM,mBAAmB,OAAO,QAAgB;QAC9C,IACE,CAAC,QACC,CAAC,qCAAqC,EAAE,UAAU,+BAA+B,CAAC,GAEpF;YACA;QACF;QAEA,iBAAiB;YAAE,GAAG,aAAa;YAAE,CAAC,OAAO,EAAE;QAAK;QACpD,IAAI;YACF,MAAM,OAAO,MAAM,CAAA,GAAA,kIAAA,CAAA,aAAU,AAAD,EAAE;YAC9B,sJAAA,CAAA,QAAK,CAAC,OAAO,CAAC,KAAK,OAAO,EAAE;gBAC1B,WAAW;gBACX,iBAAiB;gBACjB,cAAc;gBACd,cAAc;gBACd,WAAW;YACb;YACA,cAAc,mBAAmB;QACnC,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,wBAAwB;YACtC,sJAAA,CAAA,QAAK,CAAC,KAAK,CAAC,yBAAyB;gBACnC,WAAW;gBACX,iBAAiB;gBACjB,cAAc;gBACd,cAAc;gBACd,WAAW;YACb;QACF,SAAU;YACR,iBAAiB;gBAAE,GAAG,aAAa;gBAAE,CAAC,OAAO,EAAE;YAAM;QACvD;IACF;IAEA,MAAM,kBAAkB,CAAC;QACvB,gBAAgB;QAChB,iBAAiB;IACnB;IAEA,MAAM,mBAAmB,CAAC;QACxB,iBAAiB;QACjB,kBAAkB;IACpB;IAEA,MAAM,iBAAiB;QACrB,iBAAiB;QACjB,gBAAgB;IAClB;IAEA,MAAM,kBAAkB;QACtB,kBAAkB;QAClB,iBAAiB;IACnB;IAEA,IAAI,SAAS;QACX,qBACE,6LAAC;YAAI,WAAU;sBACb,cAAA,6LAAC;gBAAI,WAAU;;kCACb,6LAAC,oNAAA,CAAA,UAAO;wBAAC,WAAU;;;;;;kCACnB,6LAAC;wBAAE,WAAU;kCAAqB;;;;;;;;;;;;;;;;;IAI1C;IAEA,qBACE,6LAAC;QAAI,WAAU;;0BAEb,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;;0CACC,6LAAC;gCAAG,WAAU;0CAAsC;;;;;;0CACpD,6LAAC;gCAAE,WAAU;0CAAwB;;;;;;;;;;;;kCAIvC,6LAAC;wBAAI,WAAU;;4BAAwB;4BAAQ,MAAM,MAAM;4BAAC;;;;;;;;;;;;;0BAI9D,6LAAC;gBAAI,WAAU;0BACZ,MAAM,MAAM,KAAK,kBAChB,6LAAC;oBAAI,WAAU;;sCACb,6LAAC,qMAAA,CAAA,OAAI;4BAAC,WAAU;;;;;;sCAChB,6LAAC;4BAAE,WAAU;sCAAgB;;;;;;;;;;;2BAG/B,MAAM,GAAG,CAAC,CAAC,qBACT,6LAAC;wBAEC,WAAU;kCAEV,cAAA,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAI,WAAU;sDACb,cAAA,6LAAC;gDAAI,WAAU;;oDACZ,KAAK,QAAQ,iBACZ,6LAAC,gIAAA,CAAA,UAAK;wDACJ,KAAK,GAAG,gHAAA,CAAA,cAAW,CAAC,CAAC,EAAE,KAAK,QAAQ,EAAE;wDACtC,KAAI;wDACJ,OAAO;wDACP,QAAQ;wDACR,WAAU;wDACV,SAAS,IACP,iBAAiB,GAAG,gHAAA,CAAA,cAAW,CAAC,CAAC,EAAE,KAAK,QAAQ,EAAE;;;;;6EAItD,6LAAC;wDAAI,WAAU;kEACb,cAAA,6LAAC,qMAAA,CAAA,OAAI;4DAAC,WAAU;;;;;;;;;;;kEAGpB,6LAAC;;0EACC,6LAAC;gEACC,WAAU;gEACV,SAAS,IAAM,gBAAgB;;oEAE9B,KAAK,UAAU;oEAAC;oEAAE,KAAK,SAAS;;;;;;;0EAEnC,6LAAC;gEAAI,WAAU;;kFACb,6LAAC;wEACC,WAAW,CAAC,+BAA+B,EACzC,KAAK,WAAW,GACZ,gCACA,iCACJ;kFAED,KAAK,WAAW,GAAG,aAAa;;;;;;oEAElC,KAAK,QAAQ,kBACZ,6LAAC;wEAAK,WAAU;;0FACd,6LAAC,yMAAA,CAAA,SAAM;gFAAC,WAAU;;;;;;4EAAwB;;;;;;;oEAI7C,CAAC,KAAK,iBAAiB,kBACtB,6LAAC;wEAAK,WAAU;;0FACd,6LAAC,2NAAA,CAAA,gBAAa;gFAAC,WAAU;;;;;;4EAAwB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;sDAS7D,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAI,WAAU;;sEACb,6LAAC,qMAAA,CAAA,OAAI;4DAAC,WAAU;;;;;;wDACf,KAAK,KAAK;;;;;;;8DAEb,6LAAC;oDAAI,WAAU;;sEACb,6LAAC,uMAAA,CAAA,QAAK;4DAAC,WAAU;;;;;;wDAChB,KAAK,YAAY,IAAI;;;;;;;8DAExB,6LAAC;oDAAI,WAAU;;sEACb,6LAAC,6MAAA,CAAA,SAAM;4DAAC,WAAU;;;;;;wDACjB,KAAK,IAAI,IAAI,KAAK,WAAW,GAC1B,GAAG,KAAK,IAAI,CAAC,EAAE,EAAE,KAAK,WAAW,EAAE,GACnC;;;;;;;8DAEN,6LAAC;oDAAI,WAAU;;sEACb,6LAAC,6MAAA,CAAA,WAAQ;4DAAC,WAAU;;;;;;wDAA+B;wDAC1C,IAAI,KAAK,KAAK,WAAW,EAAE,kBAAkB;;;;;;;;;;;;;wCAIzD,KAAK,QAAQ,IAAI,KAAK,QAAQ,CAAC,MAAM,GAAG,mBACvC,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAE,WAAU;8DAAyC;;;;;;8DAGtD,6LAAC;oDAAI,WAAU;8DACZ,KAAK,QAAQ,CAAC,GAAG,CAAC,CAAC,OAAO,sBACzB,6LAAC;4DAEC,WAAU;;gEAET,MAAM,IAAI;gEAAC;gEAAG,MAAM,KAAK;gEAAC;gEAAI,MAAM,KAAK;gEAAC;;2DAHtC;;;;;;;;;;;;;;;;wCAUd,KAAK,iBAAiB,kBACrB,6LAAC;4CAAI,WAAU;;gDAA6B;gDAC5B,KAAK,iBAAiB;gDAAC;gDAAI;gDACxC,KAAK,WAAW,GACb,IAAI,KAAK,KAAK,WAAW,EAAE,kBAAkB,KAC7C;;;;;;;;;;;;;8CAMV,6LAAC;oCAAI,WAAU;;wCACZ,CAAC,KAAK,WAAW,kBAChB,6LAAC;4CACC,SAAS,IAAM,kBAAkB,KAAK,EAAE;4CACxC,UAAU,aAAa,CAAC,KAAK,EAAE,CAAC;4CAChC,WAAU;;gDAET,aAAa,CAAC,KAAK,EAAE,CAAC,iBACrB,6LAAC,oNAAA,CAAA,UAAO;oDAAC,WAAU;;;;;yEAEnB,6LAAC,8NAAA,CAAA,cAAW;oDAAC,WAAU;;;;;;8DAEzB,6LAAC;oDAAK,WAAU;8DAAO;;;;;;;;;;;;wCAI1B,CAAC,KAAK,QAAQ,kBACb,6LAAC;4CACC,SAAS,IAAM,iBAAiB,KAAK,EAAE,EAAE,KAAK,KAAK;4CACnD,UAAU,aAAa,CAAC,KAAK,EAAE,CAAC;4CAChC,WAAU;;gDAET,aAAa,CAAC,KAAK,EAAE,CAAC,iBACrB,6LAAC,oNAAA,CAAA,UAAO;oDAAC,WAAU;;;;;yEAEnB,6LAAC,6MAAA,CAAA,SAAM;oDAAC,WAAU;;;;;;8DAEpB,6LAAC;oDAAK,WAAU;8DAAO;;;;;;;;;;;;;;;;;;;;;;;;uBAtI1B,KAAK,EAAE;;;;;;;;;;YAiJnB,iBAAiB,8BAChB,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBAAI,WAAU;;sCAEb,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAG,WAAU;kDAA+B;;;;;;kDAC7C,6LAAC;wCACC,SAAS;wCACT,WAAU;kDAEV,cAAA,6LAAC,+LAAA,CAAA,IAAC;4CAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;sCAMnB,6LAAC;4BAAI,WAAU;;8CAEb,6LAAC;oCAAI,WAAU;;wCACZ,aAAa,QAAQ,iBACpB,6LAAC,gIAAA,CAAA,UAAK;4CACJ,KAAK,GAAG,gHAAA,CAAA,cAAW,CAAC,CAAC,EAAE,aAAa,QAAQ,EAAE;4CAC9C,KAAI;4CACJ,OAAO;4CACP,QAAQ;4CACR,WAAU;4CACV,SAAS,IACP,iBACE,GAAG,gHAAA,CAAA,cAAW,CAAC,CAAC,EAAE,aAAa,QAAQ,EAAE;;;;;iEAK/C,6LAAC;4CAAI,WAAU;sDACb,cAAA,6LAAC,qMAAA,CAAA,OAAI;gDAAC,WAAU;;;;;;;;;;;sDAGpB,6LAAC;;8DACC,6LAAC;oDAAG,WAAU;;wDACX,aAAa,UAAU;wDAAC;wDAAE,aAAa,SAAS;;;;;;;8DAEnD,6LAAC;oDAAI,WAAU;;sEACb,6LAAC;4DACC,WAAW,CAAC,+BAA+B,EACzC,aAAa,WAAW,GACpB,gCACA,iCACJ;sEAED,aAAa,WAAW,GAAG,aAAa;;;;;;wDAE1C,aAAa,QAAQ,kBACpB,6LAAC;4DAAK,WAAU;;8EACd,6LAAC,yMAAA,CAAA,SAAM;oEAAC,WAAU;;;;;;gEAAwB;;;;;;;wDAI7C,CAAC,aAAa,iBAAiB,kBAC9B,6LAAC;4DAAK,WAAU;;8EACd,6LAAC,2NAAA,CAAA,gBAAa;oEAAC,WAAU;;;;;;gEAAwB;;;;;;;;;;;;;;;;;;;;;;;;;8CAS3D,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAG,WAAU;8DAAsC;;;;;;8DAGpD,6LAAC;oDAAI,WAAU;;sEACb,6LAAC;4DAAI,WAAU;;8EACb,6LAAC,qMAAA,CAAA,OAAI;oEAAC,WAAU;;;;;;8EAChB,6LAAC;oEAAK,WAAU;8EACb,aAAa,KAAK;;;;;;;;;;;;sEAGvB,6LAAC;4DAAI,WAAU;;8EACb,6LAAC,uMAAA,CAAA,QAAK;oEAAC,WAAU;;;;;;8EACjB,6LAAC;oEAAK,WAAU;8EACb,aAAa,YAAY,IAAI;;;;;;;;;;;;sEAGlC,6LAAC;4DAAI,WAAU;;8EACb,6LAAC,6MAAA,CAAA,SAAM;oEAAC,WAAU;;;;;;8EAClB,6LAAC;oEAAK,WAAU;8EACb,aAAa,IAAI,IAAI,aAAa,WAAW,GAC1C,GAAG,aAAa,IAAI,CAAC,EAAE,EAAE,aAAa,WAAW,EAAE,GACnD;;;;;;;;;;;;sEAGR,6LAAC;4DAAI,WAAU;;8EACb,6LAAC,6MAAA,CAAA,WAAQ;oEAAC,WAAU;;;;;;8EACpB,6LAAC;oEAAK,WAAU;;wEAAgB;wEACtB;wEACP,IAAI,KACH,aAAa,WAAW,EACxB,kBAAkB;;;;;;;;;;;;;;;;;;;;;;;;;sDAO5B,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAG,WAAU;8DAAsC;;;;;;gDAGnD,aAAa,QAAQ,IAAI,aAAa,QAAQ,CAAC,MAAM,GAAG,kBACvD,6LAAC;oDAAI,WAAU;8DACZ,aAAa,QAAQ,CAAC,GAAG,CAAC,CAAC,OAAO,sBACjC,6LAAC;4DAEC,WAAU;;8EAEV,6LAAC;oEAAI,WAAU;8EACZ,MAAM,IAAI;;;;;;8EAEb,6LAAC;oEAAI,WAAU;;wEAAwB;wEAC7B,MAAM,KAAK;wEAAC;wEAAW,MAAM,KAAK;;;;;;;;2DAPvC;;;;;;;;;yEAaX,6LAAC;oDAAE,WAAU;8DAAgB;;;;;;;;;;;;;;;;;;gCAMlC,aAAa,iBAAiB,kBAC7B,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAG,WAAU;sDAA4C;;;;;;sDAG1D,6LAAC;4CAAE,WAAU;;gDAAiB;gDACf;8DACb,6LAAC;oDAAK,WAAU;8DACb,aAAa,iBAAiB;;;;;;;;;;;;sDAGnC,6LAAC;4CAAE,WAAU;;gDAAiB;gDACf;gDACZ,aAAa,WAAW,GACrB,IAAI,KAAK,aAAa,WAAW,EAAE,kBAAkB,KACrD;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;YAUjB,kBAAkB,+BACjB,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBAAI,WAAU;;sCAEb,6LAAC;4BACC,SAAS;4BACT,WAAU;4BACV,OAAO;gCACL,YAAY;gCACZ,gBAAgB;4BAClB;sCAEA,cAAA,6LAAC,+LAAA,CAAA,IAAC;gCAAC,WAAU;;;;;;;;;;;sCAIf,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC,gIAAA,CAAA,UAAK;gCACJ,KAAK;gCACL,KAAI;gCACJ,OAAO;gCACP,QAAQ;gCACR,WAAU;gCACV,OAAO;oCAAE,OAAO;oCAAQ,QAAQ;gCAAO;;;;;;;;;;;sCAK3C,6LAAC;4BAAI,WAAU;4BAAyB,SAAS;;;;;;;;;;;;;;;;;;;;;;;AAM7D;GAveM;KAAA;uCAyeS", "debugId": null}}, {"offset": {"line": 1411, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/IMPORTANT/work/export/front/src/app/admin/components/AdminPermissionsManagement.tsx"], "sourcesContent": ["\"use client\";\n\nimport React, { useState, useEffect } from \"react\";\nimport { BACKEND_URL } from \"../../../config\";\nimport { toast } from \"react-toastify\";\nimport {\n  Loader2,\n  CheckCircle,\n  XCircle,\n  Shield,\n  User,\n  Mail,\n  Calendar,\n  Clock,\n  AlertTriangle,\n  Key,\n  X,\n  Eye,\n  Phone,\n  MapPin,\n} from \"lucide-react\";\nimport Image from \"next/image\";\nimport { PermissionData } from \"@/types/account\";\nimport {\n  getAllPermissions,\n  approvePermission,\n  declinePermission,\n} from \"@/services/adminService\";\n\ninterface AdminPermissionsManagementProps {\n  isSuperAdmin: boolean;\n}\n\nconst AdminPermissionsManagement: React.FC<AdminPermissionsManagementProps> = ({\n  isSuperAdmin,\n}) => {\n  const [permissions, setPermissions] = useState<PermissionData[]>([]);\n  const [loading, setLoading] = useState(true);\n  const [actionLoading, setActionLoading] = useState<{\n    [key: number]: boolean;\n  }>({});\n  const [filter, setFilter] = useState<\n    \"all\" | \"pending\" | \"approved\" | \"declined\" | \"expired\"\n  >(\"all\");\n  const [selectedUser, setSelectedUser] = useState<any>(null);\n  const [showUserModal, setShowUserModal] = useState(false);\n  const [selectedImage, setSelectedImage] = useState<string | null>(null);\n  const [showImageModal, setShowImageModal] = useState(false);\n\n  useEffect(() => {\n    fetchPermissions();\n  }, []);\n\n  const fetchPermissions = async () => {\n    setLoading(true);\n    try {\n      const data = await getAllPermissions();\n      setPermissions(data);\n    } catch (error) {\n      console.error(\"Error fetching permissions:\", error);\n      toast.error(\"Failed to fetch permissions\", {\n        autoClose: false,\n        hideProgressBar: true,\n        closeOnClick: true,\n        pauseOnHover: false,\n        draggable: true,\n      });\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  const handleApprovePermission = async (permissionId: number) => {\n    setActionLoading({ ...actionLoading, [permissionId]: true });\n    try {\n      const data = await approvePermission(permissionId);\n      toast.success(data.message, {\n        autoClose: 1500,\n        hideProgressBar: true,\n        closeOnClick: true,\n        pauseOnHover: false,\n        draggable: true,\n      });\n      fetchPermissions(); // Refresh the list\n    } catch (error) {\n      console.error(\"Error approving permission:\", error);\n      toast.error(\"Failed to approve permission\", {\n        autoClose: false,\n        hideProgressBar: true,\n        closeOnClick: true,\n        pauseOnHover: false,\n        draggable: true,\n      });\n    } finally {\n      setActionLoading({ ...actionLoading, [permissionId]: false });\n    }\n  };\n\n  const handleDeclinePermission = async (permissionId: number) => {\n    setActionLoading({ ...actionLoading, [permissionId]: true });\n    try {\n      const data = await declinePermission(permissionId);\n      toast.success(data.message, {\n        autoClose: 1500,\n        hideProgressBar: true,\n        closeOnClick: true,\n        pauseOnHover: false,\n        draggable: true,\n      });\n      fetchPermissions(); // Refresh the list\n    } catch (error) {\n      console.error(\"Error declining permission:\", error);\n      toast.error(\"Failed to decline permission\", {\n        autoClose: false,\n        hideProgressBar: true,\n        closeOnClick: true,\n        pauseOnHover: false,\n        draggable: true,\n      });\n    } finally {\n      setActionLoading({ ...actionLoading, [permissionId]: false });\n    }\n  };\n\n  const getStatusColor = (status: string, isExpired: boolean) => {\n    if (isExpired) return \"bg-gray-100 text-gray-800\";\n    switch (status) {\n      case \"approved\":\n        return \"bg-green-100 text-green-800\";\n      case \"declined\":\n        return \"bg-red-100 text-red-800\";\n      case \"pending\":\n        return \"bg-yellow-100 text-yellow-800\";\n      default:\n        return \"bg-gray-100 text-gray-800\";\n    }\n  };\n\n  const handleUserClick = async (userEmail: string) => {\n    try {\n      const response = await fetch(`http://localhost:8000/api/admin/users/`, {\n        headers: {\n          Authorization: `Bearer ${localStorage.getItem(\"access_token\")}`,\n        },\n      });\n\n      if (response.ok) {\n        const users = await response.json();\n        const user = users.find((u: any) => u.email === userEmail);\n        if (user) {\n          setSelectedUser(user);\n          setShowUserModal(true);\n        }\n      }\n    } catch (error) {\n      console.error(\"Error fetching user details:\", error);\n    }\n  };\n\n  const handleImageClick = (imageUrl: string) => {\n    setSelectedImage(imageUrl);\n    setShowImageModal(true);\n  };\n\n  const closeUserModal = () => {\n    setShowUserModal(false);\n    setSelectedUser(null);\n  };\n\n  const closeImageModal = () => {\n    setShowImageModal(false);\n    setSelectedImage(null);\n  };\n\n  const filteredPermissions = permissions.filter((permission) => {\n    // For normal admins, always show only approved permissions\n    if (!isSuperAdmin) {\n      return permission.approval_status === \"approved\";\n    }\n\n    // For super admins, apply the selected filter\n    switch (filter) {\n      case \"pending\":\n        return (\n          permission.approval_status === \"pending\" && !permission.is_expired\n        );\n      case \"approved\":\n        return permission.approval_status === \"approved\";\n      case \"declined\":\n        return permission.approval_status === \"declined\";\n      case \"expired\":\n        return permission.is_expired;\n      default:\n        return true;\n    }\n  });\n\n  if (loading) {\n    return (\n      <div className=\"flex items-center justify-center py-8\">\n        <div className=\"text-center\">\n          <Loader2 className=\"w-8 h-8 animate-spin mx-auto text-slate-600\" />\n          <p className=\"mt-4 text-gray-600\">Loading permissions...</p>\n        </div>\n      </div>\n    );\n  }\n\n  return (\n    <div className=\"space-y-6\">\n      {/* Header with Filter */}\n      <div className=\"flex justify-between items-center\">\n        <div>\n          <h4 className=\"text-lg font-semibold text-gray-900\">\n            Permission Requests\n          </h4>\n          <p className=\"text-sm text-gray-600\">\n            Approve or decline permission requests\n          </p>\n        </div>\n        <div className=\"flex items-center space-x-4\">\n          <select\n            value={filter}\n            onChange={(e) =>\n              setFilter(\n                e.target.value as\n                  | \"all\"\n                  | \"pending\"\n                  | \"approved\"\n                  | \"declined\"\n                  | \"expired\"\n              )\n            }\n            className=\"px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-slate-500\"\n          >\n            {isSuperAdmin ? (\n              <>\n                <option value=\"all\">All Permissions</option>\n                <option value=\"pending\">Pending</option>\n                <option value=\"approved\">Approved</option>\n                <option value=\"declined\">Declined</option>\n                <option value=\"expired\">Expired</option>\n              </>\n            ) : (\n              <option value=\"approved\">Approved Permissions</option>\n            )}\n          </select>\n          <div className=\"text-sm text-gray-500\">\n            {filteredPermissions.length} of {permissions.length}\n          </div>\n        </div>\n      </div>\n\n      {/* Permissions List */}\n      <div className=\"space-y-4\">\n        {filteredPermissions.length === 0 ? (\n          <div className=\"text-center py-8\">\n            <Shield className=\"w-12 h-12 text-gray-400 mx-auto mb-4\" />\n            <p className=\"text-gray-500\">\n              No permissions found for the selected filter.\n            </p>\n          </div>\n        ) : (\n          filteredPermissions.map((permission) => (\n            <div\n              key={permission.id}\n              className=\"border border-gray-200 rounded-xl p-6 bg-gradient-to-br from-white to-gray-50\"\n            >\n              <div className=\"flex justify-between items-start\">\n                <div className=\"flex-1\">\n                  <div className=\"flex items-center mb-3\">\n                    <Shield className=\"w-5 h-5 text-slate-600 mr-2\" />\n                    <h6 className=\"font-semibold text-gray-900\">\n                      {permission.receiver_name}\n                    </h6>\n                    <div className=\"ml-3 flex items-center space-x-2\">\n                      <span\n                        className={`px-2 py-1 text-xs rounded-full ${getStatusColor(\n                          permission.approval_status,\n                          permission.is_expired\n                        )}`}\n                      >\n                        {permission.is_expired\n                          ? \"Expired\"\n                          : permission.approval_status}\n                      </span>\n                      {permission.is_expired && (\n                        <AlertTriangle className=\"w-4 h-4 text-red-500\" />\n                      )}\n                    </div>\n                  </div>\n\n                  <div className=\"grid grid-cols-1 md:grid-cols-2 gap-4 text-sm text-gray-600 mb-4\">\n                    <div className=\"flex items-center\">\n                      <User className=\"w-4 h-4 mr-2 text-gray-400\" />\n                      Parent:{\" \"}\n                      <span\n                        className=\"cursor-pointer hover:text-blue-600 transition-colors font-medium ml-1\"\n                        onClick={() => handleUserClick(permission.user_email)}\n                      >\n                        {permission.user_email}\n                      </span>\n                    </div>\n                    <div className=\"flex items-center\">\n                      <User className=\"w-4 h-4 mr-2 text-gray-400\" />\n                      Child: {permission.child_name}\n                    </div>\n                    <div className=\"flex items-center\">\n                      <Calendar className=\"w-4 h-4 mr-2 text-gray-400\" />\n                      Created:{\" \"}\n                      {new Date(permission.created_at).toLocaleDateString()}\n                    </div>\n                    <div className=\"flex items-center\">\n                      <Clock className=\"w-4 h-4 mr-2 text-gray-400\" />\n                      Expires:{\" \"}\n                      {new Date(permission.expires_at).toLocaleDateString()}\n                    </div>\n                  </div>\n\n                  {permission.otp && (\n                    <div className=\"bg-slate-100 rounded-lg p-3 inline-block mb-4\">\n                      <div className=\"flex items-center\">\n                        <Key className=\"w-4 h-4 mr-2 text-slate-600\" />\n                        <p className=\"text-sm font-medium text-slate-700\">\n                          OTP:{\" \"}\n                          <span className=\"font-mono text-lg\">\n                            {permission.otp}\n                          </span>\n                        </p>\n                      </div>\n                    </div>\n                  )}\n\n                  {permission.approved_by_email && (\n                    <div className=\"text-sm text-gray-500\">\n                      {permission.approval_status === \"approved\"\n                        ? \"Approved\"\n                        : \"Declined\"}{\" \"}\n                      by: {permission.approved_by_email} on{\" \"}\n                      {permission.approved_at\n                        ? new Date(permission.approved_at).toLocaleDateString()\n                        : \"N/A\"}\n                    </div>\n                  )}\n                </div>\n\n                {/* ID Image */}\n                {permission.id_image && (\n                  <div className=\"ml-4\">\n                    <Image\n                      src={`${BACKEND_URL}/${permission.id_image}`}\n                      alt=\"Receiver ID\"\n                      width={100}\n                      height={100}\n                      className=\"rounded-lg shadow-md object-cover cursor-pointer hover:opacity-80 transition-opacity\"\n                      onClick={() =>\n                        handleImageClick(\n                          `${BACKEND_URL}/${permission.id_image}`\n                        )\n                      }\n                    />\n                  </div>\n                )}\n              </div>\n\n              {/* Action Buttons - Super Admin Only */}\n              {isSuperAdmin &&\n                permission.approval_status === \"pending\" &&\n                !permission.is_expired && (\n                  <div className=\"mt-4 flex space-x-3\">\n                    <button\n                      onClick={() => handleApprovePermission(permission.id)}\n                      disabled={actionLoading[permission.id]}\n                      className=\"flex items-center px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors duration-200\"\n                    >\n                      {actionLoading[permission.id] ? (\n                        <Loader2 className=\"w-4 h-4 animate-spin mr-2\" />\n                      ) : (\n                        <CheckCircle className=\"w-4 h-4 mr-2\" />\n                      )}\n                      Approve\n                    </button>\n\n                    <button\n                      onClick={() => handleDeclinePermission(permission.id)}\n                      disabled={actionLoading[permission.id]}\n                      className=\"flex items-center px-4 py-2 bg-red-600 text-white rounded-lg hover:bg-red-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors duration-200\"\n                    >\n                      {actionLoading[permission.id] ? (\n                        <Loader2 className=\"w-4 h-4 animate-spin mr-2\" />\n                      ) : (\n                        <XCircle className=\"w-4 h-4 mr-2\" />\n                      )}\n                      Decline\n                    </button>\n                  </div>\n                )}\n            </div>\n          ))\n        )}\n      </div>\n\n      {/* User Details Modal */}\n      {showUserModal && selectedUser && (\n        <div className=\"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4\">\n          <div className=\"bg-white rounded-2xl max-w-2xl w-full max-h-[90vh] overflow-y-auto\">\n            {/* Modal Header */}\n            <div className=\"bg-gradient-to-r from-slate-900 to-slate-700 px-6 py-4 rounded-t-2xl\">\n              <div className=\"flex justify-between items-center\">\n                <h3 className=\"text-xl font-bold text-white\">Parent Details</h3>\n                <button\n                  onClick={closeUserModal}\n                  className=\"text-white hover:text-gray-300 transition-colors\"\n                >\n                  <X className=\"w-6 h-6\" />\n                </button>\n              </div>\n            </div>\n\n            {/* Modal Content */}\n            <div className=\"p-6\">\n              {/* User Info */}\n              <div className=\"flex items-center mb-6\">\n                {selectedUser.id_image ? (\n                  <Image\n                    src={selectedUser.id_image}\n                    alt=\"User ID\"\n                    width={80}\n                    height={80}\n                    className=\"rounded-full object-cover mr-4 cursor-pointer hover:opacity-80 transition-opacity\"\n                    onClick={() => handleImageClick(selectedUser.id_image)}\n                  />\n                ) : (\n                  <div className=\"w-20 h-20 bg-gray-200 rounded-full flex items-center justify-center mr-4\">\n                    <User className=\"w-10 h-10 text-gray-500\" />\n                  </div>\n                )}\n                <div>\n                  <h4 className=\"text-2xl font-bold text-gray-900\">\n                    {selectedUser.first_name} {selectedUser.last_name}\n                  </h4>\n                  <div className=\"flex items-center space-x-2 mt-2\">\n                    <span\n                      className={`px-3 py-1 text-sm rounded-full ${\n                        selectedUser.is_approved\n                          ? \"bg-green-100 text-green-800\"\n                          : \"bg-yellow-100 text-yellow-800\"\n                      }`}\n                    >\n                      {selectedUser.is_approved ? \"Approved\" : \"Pending\"}\n                    </span>\n                    {selectedUser.is_admin && (\n                      <span className=\"px-3 py-1 text-sm rounded-full bg-blue-100 text-blue-800\">\n                        <Shield className=\"w-4 h-4 inline mr-1\" />\n                        Admin\n                      </span>\n                    )}\n                    {!selectedUser.is_email_verified && (\n                      <span className=\"px-3 py-1 text-sm rounded-full bg-red-100 text-red-800\">\n                        <AlertTriangle className=\"w-4 h-4 inline mr-1\" />\n                        Unverified\n                      </span>\n                    )}\n                  </div>\n                </div>\n              </div>\n\n              {/* Contact Information */}\n              <div className=\"grid grid-cols-1 md:grid-cols-2 gap-6 mb-6\">\n                <div className=\"space-y-4\">\n                  <h5 className=\"text-lg font-semibold text-gray-900\">\n                    Contact Information\n                  </h5>\n                  <div className=\"space-y-3\">\n                    <div className=\"flex items-center\">\n                      <Mail className=\"w-5 h-5 mr-3 text-gray-400\" />\n                      <span className=\"text-gray-700\">\n                        {selectedUser.email}\n                      </span>\n                    </div>\n                    <div className=\"flex items-center\">\n                      <Phone className=\"w-5 h-5 mr-3 text-gray-400\" />\n                      <span className=\"text-gray-700\">\n                        {selectedUser.phone_number || \"Not provided\"}\n                      </span>\n                    </div>\n                    <div className=\"flex items-center\">\n                      <MapPin className=\"w-5 h-5 mr-3 text-gray-400\" />\n                      <span className=\"text-gray-700\">\n                        {selectedUser.city && selectedUser.governorate\n                          ? `${selectedUser.city}, ${selectedUser.governorate}`\n                          : \"Location not provided\"}\n                      </span>\n                    </div>\n                    <div className=\"flex items-center\">\n                      <Calendar className=\"w-5 h-5 mr-3 text-gray-400\" />\n                      <span className=\"text-gray-700\">\n                        Joined:{\" \"}\n                        {new Date(\n                          selectedUser.date_joined\n                        ).toLocaleDateString()}\n                      </span>\n                    </div>\n                  </div>\n                </div>\n\n                {/* Children Information */}\n                <div className=\"space-y-4\">\n                  <h5 className=\"text-lg font-semibold text-gray-900\">\n                    Children\n                  </h5>\n                  {selectedUser.children && selectedUser.children.length > 0 ? (\n                    <div className=\"space-y-3\">\n                      {selectedUser.children.map(\n                        (child: any, index: number) => (\n                          <div\n                            key={index}\n                            className=\"bg-blue-50 p-3 rounded-lg border border-blue-200\"\n                          >\n                            <div className=\"font-medium text-blue-900\">\n                              {child.name}\n                            </div>\n                            <div className=\"text-sm text-blue-700\">\n                              Class: {child.class} | Stage: {child.stage}\n                            </div>\n                          </div>\n                        )\n                      )}\n                    </div>\n                  ) : (\n                    <p className=\"text-gray-500\">No children registered</p>\n                  )}\n                </div>\n              </div>\n\n              {/* Approval Information */}\n              {selectedUser.approved_by_email && (\n                <div className=\"bg-green-50 p-4 rounded-lg border border-green-200\">\n                  <h5 className=\"text-lg font-semibold text-green-900 mb-2\">\n                    Approval Information\n                  </h5>\n                  <p className=\"text-green-700\">\n                    Approved by:{\" \"}\n                    <span className=\"font-medium\">\n                      {selectedUser.approved_by_email}\n                    </span>\n                  </p>\n                  <p className=\"text-green-700\">\n                    Approved on:{\" \"}\n                    {selectedUser.approved_at\n                      ? new Date(selectedUser.approved_at).toLocaleDateString()\n                      : \"N/A\"}\n                  </p>\n                </div>\n              )}\n            </div>\n          </div>\n        </div>\n      )}\n\n      {/* Enhanced Image Modal with Better Close Button */}\n      {showImageModal && selectedImage && (\n        <div className=\"fixed inset-0 bg-black bg-opacity-90 flex items-center justify-center z-50 p-4\">\n          <div className=\"relative max-w-4xl max-h-[90vh] w-full h-full flex items-center justify-center\">\n            {/* Enhanced Close Button */}\n            <button\n              onClick={closeImageModal}\n              className=\"absolute top-4 right-4 z-20 bg-white bg-opacity-20 backdrop-blur-sm hover:bg-opacity-30 text-white border-2 border-white border-opacity-50 hover:border-opacity-100 rounded-full p-3 transition-all duration-200 shadow-lg\"\n              style={{\n                background: \"rgba(255, 255, 255, 0.1)\",\n                backdropFilter: \"blur(10px)\",\n              }}\n            >\n              <X className=\"w-6 h-6 stroke-2\" />\n            </button>\n\n            {/* Image Container */}\n            <div className=\"relative bg-white rounded-lg shadow-2xl overflow-hidden max-w-full max-h-full\">\n              <Image\n                src={selectedImage}\n                alt=\"ID Image - Full Size\"\n                width={800}\n                height={600}\n                className=\"max-w-full max-h-[85vh] object-contain\"\n                style={{ width: \"auto\", height: \"auto\" }}\n              />\n            </div>\n\n            {/* Alternative Close Area - Click outside */}\n            <div className=\"absolute inset-0 -z-10\" onClick={closeImageModal} />\n          </div>\n        </div>\n      )}\n    </div>\n  );\n};\n\nexport default AdminPermissionsManagement;\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAgBA;AAEA;;;AAvBA;;;;;;;AAiCA,MAAM,6BAAwE,CAAC,EAC7E,YAAY,EACb;;IACC,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAoB,EAAE;IACnE,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAE9C,CAAC;IACJ,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAEjC;IACF,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAO;IACtD,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACnD,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAiB;IAClE,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAErD,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;gDAAE;YACR;QACF;+CAAG,EAAE;IAEL,MAAM,mBAAmB;QACvB,WAAW;QACX,IAAI;YACF,MAAM,OAAO,MAAM,CAAA,GAAA,kIAAA,CAAA,oBAAiB,AAAD;YACnC,eAAe;QACjB,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,+BAA+B;YAC7C,sJAAA,CAAA,QAAK,CAAC,KAAK,CAAC,+BAA+B;gBACzC,WAAW;gBACX,iBAAiB;gBACjB,cAAc;gBACd,cAAc;gBACd,WAAW;YACb;QACF,SAAU;YACR,WAAW;QACb;IACF;IAEA,MAAM,0BAA0B,OAAO;QACrC,iBAAiB;YAAE,GAAG,aAAa;YAAE,CAAC,aAAa,EAAE;QAAK;QAC1D,IAAI;YACF,MAAM,OAAO,MAAM,CAAA,GAAA,kIAAA,CAAA,oBAAiB,AAAD,EAAE;YACrC,sJAAA,CAAA,QAAK,CAAC,OAAO,CAAC,KAAK,OAAO,EAAE;gBAC1B,WAAW;gBACX,iBAAiB;gBACjB,cAAc;gBACd,cAAc;gBACd,WAAW;YACb;YACA,oBAAoB,mBAAmB;QACzC,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,+BAA+B;YAC7C,sJAAA,CAAA,QAAK,CAAC,KAAK,CAAC,gCAAgC;gBAC1C,WAAW;gBACX,iBAAiB;gBACjB,cAAc;gBACd,cAAc;gBACd,WAAW;YACb;QACF,SAAU;YACR,iBAAiB;gBAAE,GAAG,aAAa;gBAAE,CAAC,aAAa,EAAE;YAAM;QAC7D;IACF;IAEA,MAAM,0BAA0B,OAAO;QACrC,iBAAiB;YAAE,GAAG,aAAa;YAAE,CAAC,aAAa,EAAE;QAAK;QAC1D,IAAI;YACF,MAAM,OAAO,MAAM,CAAA,GAAA,kIAAA,CAAA,oBAAiB,AAAD,EAAE;YACrC,sJAAA,CAAA,QAAK,CAAC,OAAO,CAAC,KAAK,OAAO,EAAE;gBAC1B,WAAW;gBACX,iBAAiB;gBACjB,cAAc;gBACd,cAAc;gBACd,WAAW;YACb;YACA,oBAAoB,mBAAmB;QACzC,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,+BAA+B;YAC7C,sJAAA,CAAA,QAAK,CAAC,KAAK,CAAC,gCAAgC;gBAC1C,WAAW;gBACX,iBAAiB;gBACjB,cAAc;gBACd,cAAc;gBACd,WAAW;YACb;QACF,SAAU;YACR,iBAAiB;gBAAE,GAAG,aAAa;gBAAE,CAAC,aAAa,EAAE;YAAM;QAC7D;IACF;IAEA,MAAM,iBAAiB,CAAC,QAAgB;QACtC,IAAI,WAAW,OAAO;QACtB,OAAQ;YACN,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT;gBACE,OAAO;QACX;IACF;IAEA,MAAM,kBAAkB,OAAO;QAC7B,IAAI;YACF,MAAM,WAAW,MAAM,MAAM,CAAC,sCAAsC,CAAC,EAAE;gBACrE,SAAS;oBACP,eAAe,CAAC,OAAO,EAAE,aAAa,OAAO,CAAC,iBAAiB;gBACjE;YACF;YAEA,IAAI,SAAS,EAAE,EAAE;gBACf,MAAM,QAAQ,MAAM,SAAS,IAAI;gBACjC,MAAM,OAAO,MAAM,IAAI,CAAC,CAAC,IAAW,EAAE,KAAK,KAAK;gBAChD,IAAI,MAAM;oBACR,gBAAgB;oBAChB,iBAAiB;gBACnB;YACF;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,gCAAgC;QAChD;IACF;IAEA,MAAM,mBAAmB,CAAC;QACxB,iBAAiB;QACjB,kBAAkB;IACpB;IAEA,MAAM,iBAAiB;QACrB,iBAAiB;QACjB,gBAAgB;IAClB;IAEA,MAAM,kBAAkB;QACtB,kBAAkB;QAClB,iBAAiB;IACnB;IAEA,MAAM,sBAAsB,YAAY,MAAM,CAAC,CAAC;QAC9C,2DAA2D;QAC3D,IAAI,CAAC,cAAc;YACjB,OAAO,WAAW,eAAe,KAAK;QACxC;QAEA,8CAA8C;QAC9C,OAAQ;YACN,KAAK;gBACH,OACE,WAAW,eAAe,KAAK,aAAa,CAAC,WAAW,UAAU;YAEtE,KAAK;gBACH,OAAO,WAAW,eAAe,KAAK;YACxC,KAAK;gBACH,OAAO,WAAW,eAAe,KAAK;YACxC,KAAK;gBACH,OAAO,WAAW,UAAU;YAC9B;gBACE,OAAO;QACX;IACF;IAEA,IAAI,SAAS;QACX,qBACE,6LAAC;YAAI,WAAU;sBACb,cAAA,6LAAC;gBAAI,WAAU;;kCACb,6LAAC,oNAAA,CAAA,UAAO;wBAAC,WAAU;;;;;;kCACnB,6LAAC;wBAAE,WAAU;kCAAqB;;;;;;;;;;;;;;;;;IAI1C;IAEA,qBACE,6LAAC;QAAI,WAAU;;0BAEb,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;;0CACC,6LAAC;gCAAG,WAAU;0CAAsC;;;;;;0CAGpD,6LAAC;gCAAE,WAAU;0CAAwB;;;;;;;;;;;;kCAIvC,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCACC,OAAO;gCACP,UAAU,CAAC,IACT,UACE,EAAE,MAAM,CAAC,KAAK;gCAQlB,WAAU;0CAET,6BACC;;sDACE,6LAAC;4CAAO,OAAM;sDAAM;;;;;;sDACpB,6LAAC;4CAAO,OAAM;sDAAU;;;;;;sDACxB,6LAAC;4CAAO,OAAM;sDAAW;;;;;;sDACzB,6LAAC;4CAAO,OAAM;sDAAW;;;;;;sDACzB,6LAAC;4CAAO,OAAM;sDAAU;;;;;;;iEAG1B,6LAAC;oCAAO,OAAM;8CAAW;;;;;;;;;;;0CAG7B,6LAAC;gCAAI,WAAU;;oCACZ,oBAAoB,MAAM;oCAAC;oCAAK,YAAY,MAAM;;;;;;;;;;;;;;;;;;;0BAMzD,6LAAC;gBAAI,WAAU;0BACZ,oBAAoB,MAAM,KAAK,kBAC9B,6LAAC;oBAAI,WAAU;;sCACb,6LAAC,yMAAA,CAAA,SAAM;4BAAC,WAAU;;;;;;sCAClB,6LAAC;4BAAE,WAAU;sCAAgB;;;;;;;;;;;2BAK/B,oBAAoB,GAAG,CAAC,CAAC,2BACvB,6LAAC;wBAEC,WAAU;;0CAEV,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAI,WAAU;;kEACb,6LAAC,yMAAA,CAAA,SAAM;wDAAC,WAAU;;;;;;kEAClB,6LAAC;wDAAG,WAAU;kEACX,WAAW,aAAa;;;;;;kEAE3B,6LAAC;wDAAI,WAAU;;0EACb,6LAAC;gEACC,WAAW,CAAC,+BAA+B,EAAE,eAC3C,WAAW,eAAe,EAC1B,WAAW,UAAU,GACpB;0EAEF,WAAW,UAAU,GAClB,YACA,WAAW,eAAe;;;;;;4DAE/B,WAAW,UAAU,kBACpB,6LAAC,2NAAA,CAAA,gBAAa;gEAAC,WAAU;;;;;;;;;;;;;;;;;;0DAK/B,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAI,WAAU;;0EACb,6LAAC,qMAAA,CAAA,OAAI;gEAAC,WAAU;;;;;;4DAA+B;4DACvC;0EACR,6LAAC;gEACC,WAAU;gEACV,SAAS,IAAM,gBAAgB,WAAW,UAAU;0EAEnD,WAAW,UAAU;;;;;;;;;;;;kEAG1B,6LAAC;wDAAI,WAAU;;0EACb,6LAAC,qMAAA,CAAA,OAAI;gEAAC,WAAU;;;;;;4DAA+B;4DACvC,WAAW,UAAU;;;;;;;kEAE/B,6LAAC;wDAAI,WAAU;;0EACb,6LAAC,6MAAA,CAAA,WAAQ;gEAAC,WAAU;;;;;;4DAA+B;4DAC1C;4DACR,IAAI,KAAK,WAAW,UAAU,EAAE,kBAAkB;;;;;;;kEAErD,6LAAC;wDAAI,WAAU;;0EACb,6LAAC,uMAAA,CAAA,QAAK;gEAAC,WAAU;;;;;;4DAA+B;4DACvC;4DACR,IAAI,KAAK,WAAW,UAAU,EAAE,kBAAkB;;;;;;;;;;;;;4CAItD,WAAW,GAAG,kBACb,6LAAC;gDAAI,WAAU;0DACb,cAAA,6LAAC;oDAAI,WAAU;;sEACb,6LAAC,mMAAA,CAAA,MAAG;4DAAC,WAAU;;;;;;sEACf,6LAAC;4DAAE,WAAU;;gEAAqC;gEAC3C;8EACL,6LAAC;oEAAK,WAAU;8EACb,WAAW,GAAG;;;;;;;;;;;;;;;;;;;;;;;4CAOxB,WAAW,iBAAiB,kBAC3B,6LAAC;gDAAI,WAAU;;oDACZ,WAAW,eAAe,KAAK,aAC5B,aACA;oDAAY;oDAAI;oDACf,WAAW,iBAAiB;oDAAC;oDAAI;oDACrC,WAAW,WAAW,GACnB,IAAI,KAAK,WAAW,WAAW,EAAE,kBAAkB,KACnD;;;;;;;;;;;;;oCAMT,WAAW,QAAQ,kBAClB,6LAAC;wCAAI,WAAU;kDACb,cAAA,6LAAC,gIAAA,CAAA,UAAK;4CACJ,KAAK,GAAG,gHAAA,CAAA,cAAW,CAAC,CAAC,EAAE,WAAW,QAAQ,EAAE;4CAC5C,KAAI;4CACJ,OAAO;4CACP,QAAQ;4CACR,WAAU;4CACV,SAAS,IACP,iBACE,GAAG,gHAAA,CAAA,cAAW,CAAC,CAAC,EAAE,WAAW,QAAQ,EAAE;;;;;;;;;;;;;;;;;4BASlD,gBACC,WAAW,eAAe,KAAK,aAC/B,CAAC,WAAW,UAAU,kBACpB,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCACC,SAAS,IAAM,wBAAwB,WAAW,EAAE;wCACpD,UAAU,aAAa,CAAC,WAAW,EAAE,CAAC;wCACtC,WAAU;;4CAET,aAAa,CAAC,WAAW,EAAE,CAAC,iBAC3B,6LAAC,oNAAA,CAAA,UAAO;gDAAC,WAAU;;;;;qEAEnB,6LAAC,8NAAA,CAAA,cAAW;gDAAC,WAAU;;;;;;4CACvB;;;;;;;kDAIJ,6LAAC;wCACC,SAAS,IAAM,wBAAwB,WAAW,EAAE;wCACpD,UAAU,aAAa,CAAC,WAAW,EAAE,CAAC;wCACtC,WAAU;;4CAET,aAAa,CAAC,WAAW,EAAE,CAAC,iBAC3B,6LAAC,oNAAA,CAAA,UAAO;gDAAC,WAAU;;;;;qEAEnB,6LAAC,+MAAA,CAAA,UAAO;gDAAC,WAAU;;;;;;4CACnB;;;;;;;;;;;;;;uBA/HL,WAAW,EAAE;;;;;;;;;;YA0IzB,iBAAiB,8BAChB,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBAAI,WAAU;;sCAEb,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAG,WAAU;kDAA+B;;;;;;kDAC7C,6LAAC;wCACC,SAAS;wCACT,WAAU;kDAEV,cAAA,6LAAC,+LAAA,CAAA,IAAC;4CAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;sCAMnB,6LAAC;4BAAI,WAAU;;8CAEb,6LAAC;oCAAI,WAAU;;wCACZ,aAAa,QAAQ,iBACpB,6LAAC,gIAAA,CAAA,UAAK;4CACJ,KAAK,aAAa,QAAQ;4CAC1B,KAAI;4CACJ,OAAO;4CACP,QAAQ;4CACR,WAAU;4CACV,SAAS,IAAM,iBAAiB,aAAa,QAAQ;;;;;iEAGvD,6LAAC;4CAAI,WAAU;sDACb,cAAA,6LAAC,qMAAA,CAAA,OAAI;gDAAC,WAAU;;;;;;;;;;;sDAGpB,6LAAC;;8DACC,6LAAC;oDAAG,WAAU;;wDACX,aAAa,UAAU;wDAAC;wDAAE,aAAa,SAAS;;;;;;;8DAEnD,6LAAC;oDAAI,WAAU;;sEACb,6LAAC;4DACC,WAAW,CAAC,+BAA+B,EACzC,aAAa,WAAW,GACpB,gCACA,iCACJ;sEAED,aAAa,WAAW,GAAG,aAAa;;;;;;wDAE1C,aAAa,QAAQ,kBACpB,6LAAC;4DAAK,WAAU;;8EACd,6LAAC,yMAAA,CAAA,SAAM;oEAAC,WAAU;;;;;;gEAAwB;;;;;;;wDAI7C,CAAC,aAAa,iBAAiB,kBAC9B,6LAAC;4DAAK,WAAU;;8EACd,6LAAC,2NAAA,CAAA,gBAAa;oEAAC,WAAU;;;;;;gEAAwB;;;;;;;;;;;;;;;;;;;;;;;;;8CAS3D,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAG,WAAU;8DAAsC;;;;;;8DAGpD,6LAAC;oDAAI,WAAU;;sEACb,6LAAC;4DAAI,WAAU;;8EACb,6LAAC,qMAAA,CAAA,OAAI;oEAAC,WAAU;;;;;;8EAChB,6LAAC;oEAAK,WAAU;8EACb,aAAa,KAAK;;;;;;;;;;;;sEAGvB,6LAAC;4DAAI,WAAU;;8EACb,6LAAC,uMAAA,CAAA,QAAK;oEAAC,WAAU;;;;;;8EACjB,6LAAC;oEAAK,WAAU;8EACb,aAAa,YAAY,IAAI;;;;;;;;;;;;sEAGlC,6LAAC;4DAAI,WAAU;;8EACb,6LAAC,6MAAA,CAAA,SAAM;oEAAC,WAAU;;;;;;8EAClB,6LAAC;oEAAK,WAAU;8EACb,aAAa,IAAI,IAAI,aAAa,WAAW,GAC1C,GAAG,aAAa,IAAI,CAAC,EAAE,EAAE,aAAa,WAAW,EAAE,GACnD;;;;;;;;;;;;sEAGR,6LAAC;4DAAI,WAAU;;8EACb,6LAAC,6MAAA,CAAA,WAAQ;oEAAC,WAAU;;;;;;8EACpB,6LAAC;oEAAK,WAAU;;wEAAgB;wEACtB;wEACP,IAAI,KACH,aAAa,WAAW,EACxB,kBAAkB;;;;;;;;;;;;;;;;;;;;;;;;;sDAO5B,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAG,WAAU;8DAAsC;;;;;;gDAGnD,aAAa,QAAQ,IAAI,aAAa,QAAQ,CAAC,MAAM,GAAG,kBACvD,6LAAC;oDAAI,WAAU;8DACZ,aAAa,QAAQ,CAAC,GAAG,CACxB,CAAC,OAAY,sBACX,6LAAC;4DAEC,WAAU;;8EAEV,6LAAC;oEAAI,WAAU;8EACZ,MAAM,IAAI;;;;;;8EAEb,6LAAC;oEAAI,WAAU;;wEAAwB;wEAC7B,MAAM,KAAK;wEAAC;wEAAW,MAAM,KAAK;;;;;;;;2DAPvC;;;;;;;;;yEAcb,6LAAC;oDAAE,WAAU;8DAAgB;;;;;;;;;;;;;;;;;;gCAMlC,aAAa,iBAAiB,kBAC7B,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAG,WAAU;sDAA4C;;;;;;sDAG1D,6LAAC;4CAAE,WAAU;;gDAAiB;gDACf;8DACb,6LAAC;oDAAK,WAAU;8DACb,aAAa,iBAAiB;;;;;;;;;;;;sDAGnC,6LAAC;4CAAE,WAAU;;gDAAiB;gDACf;gDACZ,aAAa,WAAW,GACrB,IAAI,KAAK,aAAa,WAAW,EAAE,kBAAkB,KACrD;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;YAUjB,kBAAkB,+BACjB,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBAAI,WAAU;;sCAEb,6LAAC;4BACC,SAAS;4BACT,WAAU;4BACV,OAAO;gCACL,YAAY;gCACZ,gBAAgB;4BAClB;sCAEA,cAAA,6LAAC,+LAAA,CAAA,IAAC;gCAAC,WAAU;;;;;;;;;;;sCAIf,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC,gIAAA,CAAA,UAAK;gCACJ,KAAK;gCACL,KAAI;gCACJ,OAAO;gCACP,QAAQ;gCACR,WAAU;gCACV,OAAO;oCAAE,OAAO;oCAAQ,QAAQ;gCAAO;;;;;;;;;;;sCAK3C,6LAAC;4BAAI,WAAU;4BAAyB,SAAS;;;;;;;;;;;;;;;;;;;;;;;AAM7D;GAljBM;KAAA;uCAojBS", "debugId": null}}, {"offset": {"line": 2582, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/IMPORTANT/work/export/front/src/app/admin/page.tsx"], "sourcesContent": ["\"use client\";\n\nimport React, { useState, useEffect, useCallback } from \"react\";\nimport { toast } from \"react-toastify\";\nimport { Loader2 } from \"lucide-react\";\nimport { useRouter } from \"next/navigation\";\n\n// Import admin components\nimport AdminSidebar from \"./components/AdminSidebar\";\nimport AdminUsersManagement from \"./components/AdminUsersManagement\";\nimport AdminPermissionsManagement from \"./components/AdminPermissionsManagement\";\nimport { cleanupExpiredPermissions } from \"@/services/adminService\";\n\nconst AdminPage: React.FC = () => {\n  const router = useRouter();\n  const [activeTab, setActiveTab] = useState<\n    \"users\" | \"permissions\" | \"profile\" | \"password\" | \"settings\"\n  >(\"profile\");\n  const [loading, setLoading] = useState(true);\n  const [isAdmin, setIsAdmin] = useState(false);\n  const [isSuperAdmin, setIsSuperAdmin] = useState(false);\n  const [isNormalAdmin, setIsNormalAdmin] = useState(false);\n\n  const handleLogout = () => {\n    localStorage.removeItem(\"access_token\");\n    localStorage.removeItem(\"refresh_token\");\n    router.push(\"/login\");\n  };\n\n  const checkAdminStatus = useCallback(async () => {\n    try {\n      const response = await fetch(\n        \"http://localhost:8000/api/admin/check-status/\",\n        {\n          headers: {\n            Authorization: `Bearer ${localStorage.getItem(\"access_token\")}`,\n          },\n        }\n      );\n\n      if (response.ok) {\n        const data = await response.json();\n        setIsAdmin(data.is_admin);\n        setIsSuperAdmin(data.is_super_admin);\n        setIsNormalAdmin(data.is_normal_admin);\n\n        if (!data.is_admin) {\n          toast.error(\"تم رفض الوصول. صلاحيات الإدارة مطلوبة.\", {\n            autoClose: false,\n            hideProgressBar: true,\n            closeOnClick: true,\n            pauseOnHover: false,\n            draggable: true,\n          });\n          router.push(\"/account\");\n        }\n      } else {\n        router.push(\"/account\");\n      }\n    } catch (error) {\n      console.error(\"Error checking admin status:\", error);\n      router.push(\"/account\");\n    } finally {\n      setLoading(false);\n    }\n  }, [router]);\n\n  // Check admin status on component mount\n  useEffect(() => {\n    checkAdminStatus();\n  }, [checkAdminStatus]);\n\n  const renderContent = () => {\n    switch (activeTab) {\n      case \"users\":\n        // Only super admins can access user management\n        if (!isSuperAdmin) {\n          return (\n            <div className=\"card-nursery overflow-hidden\">\n              <div className=\"bg-red-500 px-6 py-4\">\n                <h3 className=\"text-xl font-bold text-white\">تم رفض الوصول</h3>\n              </div>\n              <div className=\"p-6 text-center\">\n                <p className=\"text-gray-600\">\n                  صلاحيات المدير العام مطلوبة للوصول إلى إدارة المستخدمين.\n                </p>\n              </div>\n            </div>\n          );\n        }\n        return (\n          <div className=\"card-nursery overflow-hidden\">\n            {/* Header */}\n            <div className=\"bg-pink-500 px-6 py-4\">\n              <h3 className=\"text-xl font-bold text-white\">إدارة المستخدمين</h3>\n              <p className=\"text-sm text-pink-100 mt-1\">\n                إدارة حسابات المستخدمين والموافقات\n              </p>\n            </div>\n\n            <div className=\"p-6\">\n              <AdminUsersManagement />\n            </div>\n          </div>\n        );\n\n      case \"permissions\":\n        return (\n          <div className=\"card-nursery overflow-hidden\">\n            {/* Header */}\n            <div className=\"bg-pink-500 px-6 py-4\">\n              <h3 className=\"text-xl font-bold text-white\">\n                {isSuperAdmin ? \"إدارة التصاريح\" : \"التصاريح المعتمدة\"}\n              </h3>\n              <p className=\"text-sm text-pink-100 mt-1\">\n                {isSuperAdmin\n                  ? \"الموافقة على أو رفض طلبات التصاريح\"\n                  : \"عرض طلبات التصاريح المعتمدة مع التفاصيل الكاملة\"}\n              </p>\n            </div>\n\n            <div className=\"p-6\">\n              <AdminPermissionsManagement isSuperAdmin={isSuperAdmin} />\n            </div>\n          </div>\n        );\n\n      case \"profile\":\n        return (\n          <div className=\"bg-white rounded-2xl shadow-xl border border-gray-100 overflow-hidden\">\n            {/* Header */}\n            <div className=\"bg-gradient-to-r from-slate-900 to-slate-700 px-6 py-4\">\n              <h3 className=\"text-xl font-bold text-white\">Admin Profile</h3>\n              <p className=\"text-sm text-slate-200 mt-1\">\n                Manage your admin account information\n              </p>\n            </div>\n\n            <div className=\"p-6\">\n              <div className=\"text-center py-8\">\n                <p className=\"text-gray-500\">\n                  Admin profile management coming soon...\n                </p>\n                <p className=\"text-sm text-gray-400 mt-2\">\n                  Use the regular account page to manage your profile.\n                </p>\n                <button\n                  onClick={() => router.push(\"/account\")}\n                  className=\"mt-4 px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors duration-200\"\n                >\n                  Go to Account Page\n                </button>\n              </div>\n            </div>\n          </div>\n        );\n\n      case \"password\":\n        return (\n          <div className=\"bg-white rounded-2xl shadow-xl border border-gray-100 overflow-hidden\">\n            {/* Header */}\n            <div className=\"bg-gradient-to-r from-slate-900 to-slate-700 px-6 py-4\">\n              <h3 className=\"text-xl font-bold text-white\">Change Password</h3>\n              <p className=\"text-sm text-slate-200 mt-1\">\n                Update your admin account password\n              </p>\n            </div>\n\n            <div className=\"p-6\">\n              <div className=\"text-center py-8\">\n                <p className=\"text-gray-500\">\n                  Password management coming soon...\n                </p>\n                <p className=\"text-sm text-gray-400 mt-2\">\n                  Use the regular account page to change your password.\n                </p>\n                <button\n                  onClick={() => router.push(\"/account\")}\n                  className=\"mt-4 px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors duration-200\"\n                >\n                  Go to Account Page\n                </button>\n              </div>\n            </div>\n          </div>\n        );\n\n      case \"settings\":\n        return (\n          <div className=\"bg-white rounded-2xl shadow-xl border border-gray-100 overflow-hidden\">\n            {/* Header */}\n            <div className=\"bg-gradient-to-r from-slate-900 to-slate-700 px-6 py-4\">\n              <h3 className=\"text-xl font-bold text-white\">Admin Settings</h3>\n              <p className=\"text-sm text-slate-200 mt-1\">\n                System administration settings\n              </p>\n            </div>\n\n            <div className=\"p-6\">\n              <div className=\"space-y-6\">\n                {/* Cleanup Expired Permissions - Super Admin Only */}\n                {isSuperAdmin && (\n                  <div className=\"border border-gray-200 rounded-xl p-6\">\n                    <h4 className=\"text-lg font-semibold text-gray-900 mb-2\">\n                      System Maintenance\n                    </h4>\n                    <p className=\"text-gray-600 mb-4\">\n                      Clean up expired permissions and maintain system health.\n                    </p>\n                    <button\n                      onClick={handleCleanupExpiredPermissions}\n                      className=\"px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors duration-200\"\n                    >\n                      Cleanup Expired Permissions\n                    </button>\n                  </div>\n                )}\n\n                {/* Admin Type Information */}\n                <div className=\"border border-blue-200 rounded-xl p-6 bg-blue-50\">\n                  <h4 className=\"text-lg font-semibold text-blue-900 mb-2\">\n                    Admin Information\n                  </h4>\n                  <p className=\"text-blue-700 mb-2\">\n                    Admin Type:{\" \"}\n                    <span className=\"font-medium\">\n                      {isSuperAdmin\n                        ? \"Super Administrator\"\n                        : \"Normal Administrator\"}\n                    </span>\n                  </p>\n                  <p className=\"text-blue-600 text-sm\">\n                    {isSuperAdmin\n                      ? \"You have full administrative privileges including user management, permission approval/decline, and system maintenance.\"\n                      : \"You have limited administrative privileges. You can view approved permissions with full details but cannot manage users or approve/decline permissions.\"}\n                  </p>\n                </div>\n\n                {/* Danger Zone */}\n                <div className=\"border border-red-200 rounded-xl p-6 bg-red-50\">\n                  <h4 className=\"text-lg font-semibold text-red-900 mb-2\">\n                    Danger Zone\n                  </h4>\n                  <p className=\"text-red-700 mb-4\">\n                    These actions are irreversible. Please be careful.\n                  </p>\n                  <button\n                    onClick={handleLogout}\n                    className=\"px-4 py-2 bg-red-600 text-white rounded-lg hover:bg-red-700 transition-colors duration-200\"\n                  >\n                    Logout from Admin Panel\n                  </button>\n                </div>\n              </div>\n            </div>\n          </div>\n        );\n\n      default:\n        return null;\n    }\n  };\n\n  const handleCleanupExpiredPermissions = async () => {\n    try {\n      const data = await cleanupExpiredPermissions();\n      toast.success(data.message, {\n        autoClose: 1500,\n        hideProgressBar: true,\n        closeOnClick: true,\n        pauseOnHover: false,\n        draggable: true,\n      });\n    } catch (error) {\n      console.error(\"Error cleaning up permissions:\", error);\n      toast.error(\"Failed to cleanup expired permissions\", {\n        autoClose: false,\n        hideProgressBar: true,\n        closeOnClick: true,\n        pauseOnHover: false,\n        draggable: true,\n      });\n    }\n  };\n\n  if (loading) {\n    return (\n      <div className=\"min-h-screen bg-gradient-to-br from-slate-50 to-blue-50 flex items-center justify-center\">\n        <div className=\"text-center\">\n          <Loader2 className=\"w-12 h-12 animate-spin mx-auto text-slate-600\" />\n          <p className=\"mt-4 text-gray-600\">Checking admin access...</p>\n        </div>\n      </div>\n    );\n  }\n\n  if (!isAdmin) {\n    return null; // Will redirect to account page\n  }\n\n  return (\n    <div className=\"min-h-screen bg-gradient-to-br from-slate-50 to-blue-50\">\n      <div className=\"container mx-auto px-4 py-8\">\n        <div className=\"flex flex-col lg:flex-row gap-8\">\n          {/* Sidebar */}\n          <div className=\"lg:w-1/4\">\n            <AdminSidebar\n              activeTab={activeTab}\n              setActiveTab={setActiveTab}\n              isSuperAdmin={isSuperAdmin}\n              isNormalAdmin={isNormalAdmin}\n            />\n          </div>\n\n          {/* Main Content */}\n          <div className=\"lg:w-3/4\">{renderContent()}</div>\n        </div>\n      </div>\n    </div>\n  );\n};\n\nexport default AdminPage;\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AAEA,0BAA0B;AAC1B;AACA;AACA;AACA;;;AAXA;;;;;;;;;AAaA,MAAM,YAAsB;;IAC1B,MAAM,SAAS,CAAA,GAAA,qIAAA,CAAA,YAAS,AAAD;IACvB,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAEvC;IACF,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACjD,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAEnD,MAAM,eAAe;QACnB,aAAa,UAAU,CAAC;QACxB,aAAa,UAAU,CAAC;QACxB,OAAO,IAAI,CAAC;IACd;IAEA,MAAM,mBAAmB,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;mDAAE;YACnC,IAAI;gBACF,MAAM,WAAW,MAAM,MACrB,iDACA;oBACE,SAAS;wBACP,eAAe,CAAC,OAAO,EAAE,aAAa,OAAO,CAAC,iBAAiB;oBACjE;gBACF;gBAGF,IAAI,SAAS,EAAE,EAAE;oBACf,MAAM,OAAO,MAAM,SAAS,IAAI;oBAChC,WAAW,KAAK,QAAQ;oBACxB,gBAAgB,KAAK,cAAc;oBACnC,iBAAiB,KAAK,eAAe;oBAErC,IAAI,CAAC,KAAK,QAAQ,EAAE;wBAClB,sJAAA,CAAA,QAAK,CAAC,KAAK,CAAC,0CAA0C;4BACpD,WAAW;4BACX,iBAAiB;4BACjB,cAAc;4BACd,cAAc;4BACd,WAAW;wBACb;wBACA,OAAO,IAAI,CAAC;oBACd;gBACF,OAAO;oBACL,OAAO,IAAI,CAAC;gBACd;YACF,EAAE,OAAO,OAAO;gBACd,QAAQ,KAAK,CAAC,gCAAgC;gBAC9C,OAAO,IAAI,CAAC;YACd,SAAU;gBACR,WAAW;YACb;QACF;kDAAG;QAAC;KAAO;IAEX,wCAAwC;IACxC,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;+BAAE;YACR;QACF;8BAAG;QAAC;KAAiB;IAErB,MAAM,gBAAgB;QACpB,OAAQ;YACN,KAAK;gBACH,+CAA+C;gBAC/C,IAAI,CAAC,cAAc;oBACjB,qBACE,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC;oCAAG,WAAU;8CAA+B;;;;;;;;;;;0CAE/C,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC;oCAAE,WAAU;8CAAgB;;;;;;;;;;;;;;;;;gBAMrC;gBACA,qBACE,6LAAC;oBAAI,WAAU;;sCAEb,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAG,WAAU;8CAA+B;;;;;;8CAC7C,6LAAC;oCAAE,WAAU;8CAA6B;;;;;;;;;;;;sCAK5C,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC,6JAAA,CAAA,UAAoB;;;;;;;;;;;;;;;;YAK7B,KAAK;gBACH,qBACE,6LAAC;oBAAI,WAAU;;sCAEb,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAG,WAAU;8CACX,eAAe,mBAAmB;;;;;;8CAErC,6LAAC;oCAAE,WAAU;8CACV,eACG,uCACA;;;;;;;;;;;;sCAIR,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC,mKAAA,CAAA,UAA0B;gCAAC,cAAc;;;;;;;;;;;;;;;;;YAKlD,KAAK;gBACH,qBACE,6LAAC;oBAAI,WAAU;;sCAEb,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAG,WAAU;8CAA+B;;;;;;8CAC7C,6LAAC;oCAAE,WAAU;8CAA8B;;;;;;;;;;;;sCAK7C,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAE,WAAU;kDAAgB;;;;;;kDAG7B,6LAAC;wCAAE,WAAU;kDAA6B;;;;;;kDAG1C,6LAAC;wCACC,SAAS,IAAM,OAAO,IAAI,CAAC;wCAC3B,WAAU;kDACX;;;;;;;;;;;;;;;;;;;;;;;YAQX,KAAK;gBACH,qBACE,6LAAC;oBAAI,WAAU;;sCAEb,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAG,WAAU;8CAA+B;;;;;;8CAC7C,6LAAC;oCAAE,WAAU;8CAA8B;;;;;;;;;;;;sCAK7C,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAE,WAAU;kDAAgB;;;;;;kDAG7B,6LAAC;wCAAE,WAAU;kDAA6B;;;;;;kDAG1C,6LAAC;wCACC,SAAS,IAAM,OAAO,IAAI,CAAC;wCAC3B,WAAU;kDACX;;;;;;;;;;;;;;;;;;;;;;;YAQX,KAAK;gBACH,qBACE,6LAAC;oBAAI,WAAU;;sCAEb,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAG,WAAU;8CAA+B;;;;;;8CAC7C,6LAAC;oCAAE,WAAU;8CAA8B;;;;;;;;;;;;sCAK7C,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC;gCAAI,WAAU;;oCAEZ,8BACC,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAG,WAAU;0DAA2C;;;;;;0DAGzD,6LAAC;gDAAE,WAAU;0DAAqB;;;;;;0DAGlC,6LAAC;gDACC,SAAS;gDACT,WAAU;0DACX;;;;;;;;;;;;kDAOL,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAG,WAAU;0DAA2C;;;;;;0DAGzD,6LAAC;gDAAE,WAAU;;oDAAqB;oDACpB;kEACZ,6LAAC;wDAAK,WAAU;kEACb,eACG,wBACA;;;;;;;;;;;;0DAGR,6LAAC;gDAAE,WAAU;0DACV,eACG,4HACA;;;;;;;;;;;;kDAKR,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAG,WAAU;0DAA0C;;;;;;0DAGxD,6LAAC;gDAAE,WAAU;0DAAoB;;;;;;0DAGjC,6LAAC;gDACC,SAAS;gDACT,WAAU;0DACX;;;;;;;;;;;;;;;;;;;;;;;;;;;;;YASb;gBACE,OAAO;QACX;IACF;IAEA,MAAM,kCAAkC;QACtC,IAAI;YACF,MAAM,OAAO,MAAM,CAAA,GAAA,kIAAA,CAAA,4BAAyB,AAAD;YAC3C,sJAAA,CAAA,QAAK,CAAC,OAAO,CAAC,KAAK,OAAO,EAAE;gBAC1B,WAAW;gBACX,iBAAiB;gBACjB,cAAc;gBACd,cAAc;gBACd,WAAW;YACb;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,kCAAkC;YAChD,sJAAA,CAAA,QAAK,CAAC,KAAK,CAAC,yCAAyC;gBACnD,WAAW;gBACX,iBAAiB;gBACjB,cAAc;gBACd,cAAc;gBACd,WAAW;YACb;QACF;IACF;IAEA,IAAI,SAAS;QACX,qBACE,6LAAC;YAAI,WAAU;sBACb,cAAA,6LAAC;gBAAI,WAAU;;kCACb,6LAAC,oNAAA,CAAA,UAAO;wBAAC,WAAU;;;;;;kCACnB,6LAAC;wBAAE,WAAU;kCAAqB;;;;;;;;;;;;;;;;;IAI1C;IAEA,IAAI,CAAC,SAAS;QACZ,OAAO,MAAM,gCAAgC;IAC/C;IAEA,qBACE,6LAAC;QAAI,WAAU;kBACb,cAAA,6LAAC;YAAI,WAAU;sBACb,cAAA,6LAAC;gBAAI,WAAU;;kCAEb,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC,qJAAA,CAAA,UAAY;4BACX,WAAW;4BACX,cAAc;4BACd,cAAc;4BACd,eAAe;;;;;;;;;;;kCAKnB,6LAAC;wBAAI,WAAU;kCAAY;;;;;;;;;;;;;;;;;;;;;;AAKrC;GAnTM;;QACW,qIAAA,CAAA,YAAS;;;KADpB;uCAqTS", "debugId": null}}]}
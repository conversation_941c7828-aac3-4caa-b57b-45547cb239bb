{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/IMPORTANT/work/export/front/src/app/account/components/Sidebar.tsx"], "sourcesContent": ["\"use client\";\n\nimport React, { useState } from \"react\";\nimport { User, Setting<PERSON>, <PERSON>, Lock } from \"lucide-react\";\n\ninterface SidebarItem {\n  label: string;\n  href: string;\n  icon: React.ReactNode;\n}\n\nconst sidebarItems: SidebarItem[] = [\n  {\n    label: \"الملف الشخصي\",\n    href: \"profile\",\n    icon: <User className=\"w-5 h-5\" />,\n  },\n  {\n    label: \"كلمة المرور\",\n    href: \"password\",\n    icon: <Lock className=\"w-5 h-5\" />,\n  },\n  {\n    label: \"التصاريح\",\n    href: \"permissions\",\n    icon: <Shield className=\"w-5 h-5\" />,\n  },\n  {\n    label: \"الإعدادات\",\n    href: \"settings\",\n    icon: <Settings className=\"w-5 h-5\" />,\n  },\n];\n\ninterface SidebarProps {\n  activeTab: string;\n  onTabChange: (tab: string) => void;\n}\n\nconst Sidebar: React.FC<SidebarProps> = ({ activeTab, onTabChange }) => {\n  // Internal state initialized once from prop on first render\n  const [internalActiveTab] = useState(activeTab);\n  const [currentTab, setCurrentTab] = useState(internalActiveTab);\n\n  const handleClick = (href: string) => {\n    setCurrentTab(href);\n    onTabChange(href);\n  };\n\n  return (\n    <nav className=\"space-y-1\">\n      {sidebarItems.map((item) => {\n        const isActive = currentTab === item.href;\n        return (\n          <button\n            key={item.href}\n            onClick={() => handleClick(item.href)}\n            className={`w-full flex items-center px-4 py-2 text-sm font-medium rounded-lg transition-colors ${\n              isActive\n                ? \"bg-pink-500 text-white\"\n                : \"text-gray-600 hover:text-pink-600 hover:bg-pink-50\"\n            }`}\n          >\n            <span\n              className={`${isActive ? \"text-white\" : \"text-gray-400\"} mr-3`}\n            >\n              {item.icon}\n            </span>\n            {item.label}\n          </button>\n        );\n      })}\n    </nav>\n  );\n};\n\nexport default Sidebar;\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AAAA;AAAA;AAHA;;;;AAWA,MAAM,eAA8B;IAClC;QACE,OAAO;QACP,MAAM;QACN,oBAAM,8OAAC,kMAAA,CAAA,OAAI;YAAC,WAAU;;;;;;IACxB;IACA;QACE,OAAO;QACP,MAAM;QACN,oBAAM,8OAAC,kMAAA,CAAA,OAAI;YAAC,WAAU;;;;;;IACxB;IACA;QACE,OAAO;QACP,MAAM;QACN,oBAAM,8OAAC,sMAAA,CAAA,SAAM;YAAC,WAAU;;;;;;IAC1B;IACA;QACE,OAAO;QACP,MAAM;QACN,oBAAM,8OAAC,0MAAA,CAAA,WAAQ;YAAC,WAAU;;;;;;IAC5B;CACD;AAOD,MAAM,UAAkC,CAAC,EAAE,SAAS,EAAE,WAAW,EAAE;IACjE,4DAA4D;IAC5D,MAAM,CAAC,kBAAkB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACrC,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAE7C,MAAM,cAAc,CAAC;QACnB,cAAc;QACd,YAAY;IACd;IAEA,qBACE,8OAAC;QAAI,WAAU;kBACZ,aAAa,GAAG,CAAC,CAAC;YACjB,MAAM,WAAW,eAAe,KAAK,IAAI;YACzC,qBACE,8OAAC;gBAEC,SAAS,IAAM,YAAY,KAAK,IAAI;gBACpC,WAAW,CAAC,oFAAoF,EAC9F,WACI,2BACA,sDACJ;;kCAEF,8OAAC;wBACC,WAAW,GAAG,WAAW,eAAe,gBAAgB,KAAK,CAAC;kCAE7D,KAAK,IAAI;;;;;;oBAEX,KAAK,KAAK;;eAbN,KAAK,IAAI;;;;;QAgBpB;;;;;;AAGN;uCAEe", "debugId": null}}, {"offset": {"line": 111, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/IMPORTANT/work/export/front/src/components/common/Input.tsx"], "sourcesContent": ["import React, { InputHTMLAttributes } from \"react\";\nimport { AlertCircle } from \"lucide-react\";\n\ninterface InputProps extends InputHTMLAttributes<HTMLInputElement> {\n  id: string;\n  label?: string;\n  error?: boolean;\n  errorMessage?: string;\n  icon?: React.ReactNode;\n}\n\nconst Input: React.FC<InputProps> = ({\n  id,\n  label,\n  error,\n  errorMessage,\n  icon,\n  className = \"\",\n  required,\n  ...props\n}) => {\n  return (\n    <div className=\"w-full\">\n      {label && (\n        <label\n          htmlFor={id}\n          className=\"block text-sm font-medium text-gray-700 mb-1\"\n        >\n          {label}\n          {required && <span className=\"text-red-500 ml-1\">*</span>}\n        </label>\n      )}\n      <div className=\"relative\">\n        {icon && (\n          <div className=\"absolute inset-y-0 right-0 pr-3 flex items-center pointer-events-none\">\n            {icon}\n          </div>\n        )}\n        {error && (\n          <div className=\"absolute left-3 top-1/2 transform -translate-y-1/2 text-red-500\">\n            <AlertCircle className=\"h-5 w-5\" />\n          </div>\n        )}\n        <input\n          id={id}\n          className={`appearance-none rounded-lg relative block w-full px-3 py-3 ${\n            icon ? \"pr-10\" : \"pr-3\"\n          } border ${\n            error ? \"border-red-500\" : \"border-gray-300\"\n          } placeholder-gray-500 text-gray-900 focus:outline-none focus:ring-2 focus:ring-black focus:border-transparent ${className}`}\n          {...props}\n          required={false}\n        />\n      </div>\n      {error && errorMessage && (\n        <p className=\"mt-1 text-sm text-red-500\">{errorMessage}</p>\n      )}\n    </div>\n  );\n};\n\nexport default Input;\n"], "names": [], "mappings": ";;;;AACA;;;AAUA,MAAM,QAA8B,CAAC,EACnC,EAAE,EACF,KAAK,EACL,KAAK,EACL,YAAY,EACZ,IAAI,EACJ,YAAY,EAAE,EACd,QAAQ,EACR,GAAG,OACJ;IACC,qBACE,8OAAC;QAAI,WAAU;;YACZ,uBACC,8OAAC;gBACC,SAAS;gBACT,WAAU;;oBAET;oBACA,0BAAY,8OAAC;wBAAK,WAAU;kCAAoB;;;;;;;;;;;;0BAGrD,8OAAC;gBAAI,WAAU;;oBACZ,sBACC,8OAAC;wBAAI,WAAU;kCACZ;;;;;;oBAGJ,uBACC,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC,oNAAA,CAAA,cAAW;4BAAC,WAAU;;;;;;;;;;;kCAG3B,8OAAC;wBACC,IAAI;wBACJ,WAAW,CAAC,2DAA2D,EACrE,OAAO,UAAU,OAClB,QAAQ,EACP,QAAQ,mBAAmB,kBAC5B,8GAA8G,EAAE,WAAW;wBAC3H,GAAG,KAAK;wBACT,UAAU;;;;;;;;;;;;YAGb,SAAS,8BACR,8OAAC;gBAAE,WAAU;0BAA6B;;;;;;;;;;;;AAIlD;uCAEe", "debugId": null}}, {"offset": {"line": 204, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/IMPORTANT/work/export/front/src/components/PhoneInput.tsx"], "sourcesContent": ["import React, { useState, useRef, useEffect } from \"react\";\nimport { ChevronDown } from \"lucide-react\";\nimport ReactCountryFlag from \"react-country-flag\";\nimport { CountryCode } from \"../utils/countryData\";\n\nexport interface PhoneInputProps {\n  value: string;\n  countryCode: string;\n  onChange: (value: string) => void;\n  onCountryChange: (code: string) => void;\n  error?: boolean;\n  errorMessage?: string;\n  countryCodes: CountryCode[];\n  isRTL?: boolean; // New prop for RTL support\n}\n\nconst PhoneInput: React.FC<PhoneInputProps> = ({\n  value,\n  countryCode,\n  onChange,\n  onCountryChange,\n  error,\n  errorMessage,\n  countryCodes,\n  isRTL = true, // default to Arabic/RTL\n}) => {\n  const [showCountryDropdown, setShowCountryDropdown] = useState(false);\n  const [countrySearchTerm, setCountrySearchTerm] = useState(\"\");\n  const phoneInputRef = useRef<HTMLInputElement>(null);\n  const dropdownRef = useRef<HTMLDivElement>(null);\n\n  useEffect(() => {\n    function handleClickOutside(event: MouseEvent) {\n      if (\n        dropdownRef.current &&\n        !dropdownRef.current.contains(event.target as Node)\n      ) {\n        setShowCountryDropdown(false);\n        setCountrySearchTerm(\"\");\n      }\n    }\n\n    document.addEventListener(\"mousedown\", handleClickOutside);\n    return () => {\n      document.removeEventListener(\"mousedown\", handleClickOutside);\n    };\n  }, []);\n\n  return (\n    <div className={`relative ${isRTL ? \"rtl\" : \"\"}`}>\n      <div\n        className={`flex rounded-lg border ${\n          error ? \"border-red-500\" : \"border-gray-300\"\n        } focus-within:ring-2 focus-within:ring-black focus-within:border-transparent`}\n        dir={isRTL ? \"rtl\" : \"ltr\"}\n      >\n        <div\n          className={`flex items-center justify-center px-3 cursor-pointer bg-gray-50 ${\n            isRTL ? \"rounded-r-lg\" : \"rounded-l-lg\"\n          }`}\n          onClick={() => setShowCountryDropdown(!showCountryDropdown)}\n        >\n          <ReactCountryFlag\n            countryCode={countryCode}\n            svg\n            style={{ width: \"1.5em\", height: \"1.5em\" }}\n            className={`${isRTL ? \"ml-2\" : \"mr-2\"}`}\n          />\n          <ChevronDown className=\"h-5 w-5 text-gray-500\" />{\" \"}\n          {/* Increased arrow size */}\n        </div>\n        <input\n          ref={phoneInputRef}\n          type=\"tel\"\n          className=\"appearance-none relative block w-full px-3 py-3 border-0 placeholder-gray-500 text-gray-900 focus:outline-none rounded-lg\"\n          placeholder={isRTL ? \"رقم الهاتف\" : \"Phone number\"}\n          value={value}\n          onChange={(e) => onChange(e.target.value)}\n          onFocus={() => setShowCountryDropdown(false)}\n          required={false}\n          dir={isRTL ? \"rtl\" : \"ltr\"}\n        />\n      </div>\n      {error && errorMessage && (\n        <p className=\"mt-1 text-sm text-red-500 text-right\">{errorMessage}</p>\n      )}\n\n      {showCountryDropdown && (\n        <div\n          ref={dropdownRef}\n          className={`absolute z-50 mt-1 w-72 max-h-72 overflow-auto bg-white border border-gray-300 rounded-md shadow-lg ${\n            isRTL ? \"right-0\" : \"left-0\"\n          }`}\n        >\n          <div className=\"p-2 sticky top-0 bg-white border-b\">\n            <input\n              type=\"text\"\n              className=\"w-full p-2 border border-gray-300 rounded\"\n              placeholder={isRTL ? \"ابحث عن دولة...\" : \"Search countries...\"}\n              onClick={(e) => e.stopPropagation()}\n              onChange={(e) =>\n                setCountrySearchTerm(e.target.value.toLowerCase())\n              }\n              autoFocus\n              dir={isRTL ? \"rtl\" : \"ltr\"}\n            />\n          </div>\n          <div className=\"py-1\">\n            {countryCodes\n              .filter(\n                (country) =>\n                  country.name.toLowerCase().includes(countrySearchTerm) ||\n                  country.dialCode.includes(countrySearchTerm) ||\n                  country.code.toLowerCase().includes(countrySearchTerm)\n              )\n              .map((country) => (\n                <div\n                  key={country.code}\n                  className=\"flex items-center px-3 py-1.5 hover:bg-gray-100 cursor-pointer text-sm\"\n                  onClick={() => {\n                    onCountryChange(country.code);\n                    setShowCountryDropdown(false);\n                    phoneInputRef.current?.focus();\n                  }}\n                >\n                  <ReactCountryFlag\n                    countryCode={country.code}\n                    svg\n                    style={{ width: \"1.2em\", height: \"1.2em\" }}\n                    className={`${isRTL ? \"ml-2\" : \"mr-2\"}`}\n                  />\n                  <span className=\"w-12 inline-block\">{country.dialCode}</span>\n                  <span className=\"truncate\">{country.name}</span>\n                </div>\n              ))}\n          </div>\n        </div>\n      )}\n    </div>\n  );\n};\n\nexport default PhoneInput;\n"], "names": [], "mappings": ";;;;AAAA;AACA;AACA;;;;;AAcA,MAAM,aAAwC,CAAC,EAC7C,KAAK,EACL,WAAW,EACX,QAAQ,EACR,eAAe,EACf,KAAK,EACL,YAAY,EACZ,YAAY,EACZ,QAAQ,IAAI,EACb;IACC,MAAM,CAAC,qBAAqB,uBAAuB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC/D,MAAM,CAAC,mBAAmB,qBAAqB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC3D,MAAM,gBAAgB,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAoB;IAC/C,MAAM,cAAc,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAkB;IAE3C,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,SAAS,mBAAmB,KAAiB;YAC3C,IACE,YAAY,OAAO,IACnB,CAAC,YAAY,OAAO,CAAC,QAAQ,CAAC,MAAM,MAAM,GAC1C;gBACA,uBAAuB;gBACvB,qBAAqB;YACvB;QACF;QAEA,SAAS,gBAAgB,CAAC,aAAa;QACvC,OAAO;YACL,SAAS,mBAAmB,CAAC,aAAa;QAC5C;IACF,GAAG,EAAE;IAEL,qBACE,8OAAC;QAAI,WAAW,CAAC,SAAS,EAAE,QAAQ,QAAQ,IAAI;;0BAC9C,8OAAC;gBACC,WAAW,CAAC,uBAAuB,EACjC,QAAQ,mBAAmB,kBAC5B,4EAA4E,CAAC;gBAC9E,KAAK,QAAQ,QAAQ;;kCAErB,8OAAC;wBACC,WAAW,CAAC,gEAAgE,EAC1E,QAAQ,iBAAiB,gBACzB;wBACF,SAAS,IAAM,uBAAuB,CAAC;;0CAEvC,8OAAC,mLAAA,CAAA,UAAgB;gCACf,aAAa;gCACb,GAAG;gCACH,OAAO;oCAAE,OAAO;oCAAS,QAAQ;gCAAQ;gCACzC,WAAW,GAAG,QAAQ,SAAS,QAAQ;;;;;;0CAEzC,8OAAC,oNAAA,CAAA,cAAW;gCAAC,WAAU;;;;;;4BAA2B;;;;;;;kCAGpD,8OAAC;wBACC,KAAK;wBACL,MAAK;wBACL,WAAU;wBACV,aAAa,QAAQ,eAAe;wBACpC,OAAO;wBACP,UAAU,CAAC,IAAM,SAAS,EAAE,MAAM,CAAC,KAAK;wBACxC,SAAS,IAAM,uBAAuB;wBACtC,UAAU;wBACV,KAAK,QAAQ,QAAQ;;;;;;;;;;;;YAGxB,SAAS,8BACR,8OAAC;gBAAE,WAAU;0BAAwC;;;;;;YAGtD,qCACC,8OAAC;gBACC,KAAK;gBACL,WAAW,CAAC,oGAAoG,EAC9G,QAAQ,YAAY,UACpB;;kCAEF,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BACC,MAAK;4BACL,WAAU;4BACV,aAAa,QAAQ,oBAAoB;4BACzC,SAAS,CAAC,IAAM,EAAE,eAAe;4BACjC,UAAU,CAAC,IACT,qBAAqB,EAAE,MAAM,CAAC,KAAK,CAAC,WAAW;4BAEjD,SAAS;4BACT,KAAK,QAAQ,QAAQ;;;;;;;;;;;kCAGzB,8OAAC;wBAAI,WAAU;kCACZ,aACE,MAAM,CACL,CAAC,UACC,QAAQ,IAAI,CAAC,WAAW,GAAG,QAAQ,CAAC,sBACpC,QAAQ,QAAQ,CAAC,QAAQ,CAAC,sBAC1B,QAAQ,IAAI,CAAC,WAAW,GAAG,QAAQ,CAAC,oBAEvC,GAAG,CAAC,CAAC,wBACJ,8OAAC;gCAEC,WAAU;gCACV,SAAS;oCACP,gBAAgB,QAAQ,IAAI;oCAC5B,uBAAuB;oCACvB,cAAc,OAAO,EAAE;gCACzB;;kDAEA,8OAAC,mLAAA,CAAA,UAAgB;wCACf,aAAa,QAAQ,IAAI;wCACzB,GAAG;wCACH,OAAO;4CAAE,OAAO;4CAAS,QAAQ;wCAAQ;wCACzC,WAAW,GAAG,QAAQ,SAAS,QAAQ;;;;;;kDAEzC,8OAAC;wCAAK,WAAU;kDAAqB,QAAQ,QAAQ;;;;;;kDACrD,8OAAC;wCAAK,WAAU;kDAAY,QAAQ,IAAI;;;;;;;+BAfnC,QAAQ,IAAI;;;;;;;;;;;;;;;;;;;;;;AAuBnC;uCAEe", "debugId": null}}, {"offset": {"line": 393, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/IMPORTANT/work/export/front/src/components/RelationSection.tsx"], "sourcesContent": ["import React from \"react\";\nimport { Users } from \"lucide-react\";\nimport Input from \"./common/Input\";\n\ninterface RelationSectionProps {\n  relation: string;\n  errors: Record<string, string>;\n  onRelationChange: (relation: string) => void;\n}\n\nconst RelationSection: React.FC<RelationSectionProps> = ({\n  relation,\n  errors,\n  onRelationChange,\n}) => {\n  // Arabic options with English keys for functionality\n  const relationOptions = [\n    { key: \"father\", label: \"أب\" },\n    { key: \"mother\", label: \"أم\" },\n    { key: \"other\", label: \"أخرى\" },\n  ];\n\n  // Check if the relation is a standard option or a custom one\n  const isCustomRelation =\n    relation && !relationOptions.some((opt) => opt.key === relation);\n\n  return (\n    <div className=\"\">\n      <div className=\"bg-gray-50 rounded-lg p-4 border border-gray-200\">\n        <div className=\"flex items-center mb-3\">\n          <Users className=\"h-5 w-5 text-gray-500 mr-2\" />\n          <h3 className=\"text-md font-medium text-gray-700\">العلاقة بالطفل</h3>\n        </div>\n\n        <div className=\"flex flex-col gap-3\">\n          <div className=\"flex gap-3\">\n            {relationOptions.map((option) => (\n              <button\n                key={option.key}\n                type=\"button\"\n                className={`flex-1 py-2.5 px-4 rounded-md text-sm font-medium transition-colors ${\n                  relation === option.key ||\n                  (isCustomRelation && option.key === \"other\")\n                    ? \"bg-pink-500 text-white\"\n                    : `bg-white text-gray-700 border ${\n                        errors.relation ? \"border-red-500\" : \"border-gray-300\"\n                      } hover:bg-gray-50`\n                }`}\n                onClick={() => onRelationChange(option.key)}\n              >\n                {option.label}\n              </button>\n            ))}\n          </div>\n\n          {/* Show input field if \"other\" is selected or custom relation exists */}\n          {(relation === \"other\" || isCustomRelation) && (\n            <Input\n              id=\"otherRelation\"\n              name=\"otherRelation\"\n              type=\"text\"\n              placeholder=\"الرجاء تحديد علاقتك (مثل: جد، جدة، ولي أمر، ...)\"\n              value={isCustomRelation ? relation : \"\"}\n              onChange={(e) => onRelationChange(e.target.value)}\n              error={!!errors.otherRelation}\n              errorMessage={errors.otherRelation}\n            />\n          )}\n        </div>\n      </div>\n    </div>\n  );\n};\n\nexport default RelationSection;\n"], "names": [], "mappings": ";;;;AACA;AACA;;;;AAQA,MAAM,kBAAkD,CAAC,EACvD,QAAQ,EACR,MAAM,EACN,gBAAgB,EACjB;IACC,qDAAqD;IACrD,MAAM,kBAAkB;QACtB;YAAE,KAAK;YAAU,OAAO;QAAK;QAC7B;YAAE,KAAK;YAAU,OAAO;QAAK;QAC7B;YAAE,KAAK;YAAS,OAAO;QAAO;KAC/B;IAED,6DAA6D;IAC7D,MAAM,mBACJ,YAAY,CAAC,gBAAgB,IAAI,CAAC,CAAC,MAAQ,IAAI,GAAG,KAAK;IAEzD,qBACE,8OAAC;QAAI,WAAU;kBACb,cAAA,8OAAC;YAAI,WAAU;;8BACb,8OAAC;oBAAI,WAAU;;sCACb,8OAAC,oMAAA,CAAA,QAAK;4BAAC,WAAU;;;;;;sCACjB,8OAAC;4BAAG,WAAU;sCAAoC;;;;;;;;;;;;8BAGpD,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAI,WAAU;sCACZ,gBAAgB,GAAG,CAAC,CAAC,uBACpB,8OAAC;oCAEC,MAAK;oCACL,WAAW,CAAC,oEAAoE,EAC9E,aAAa,OAAO,GAAG,IACtB,oBAAoB,OAAO,GAAG,KAAK,UAChC,2BACA,CAAC,8BAA8B,EAC7B,OAAO,QAAQ,GAAG,mBAAmB,kBACtC,iBAAiB,CAAC,EACvB;oCACF,SAAS,IAAM,iBAAiB,OAAO,GAAG;8CAEzC,OAAO,KAAK;mCAZR,OAAO,GAAG;;;;;;;;;;wBAkBpB,CAAC,aAAa,WAAW,gBAAgB,mBACxC,8OAAC,qIAAA,CAAA,UAAK;4BACJ,IAAG;4BACH,MAAK;4BACL,MAAK;4BACL,aAAY;4BACZ,OAAO,mBAAmB,WAAW;4BACrC,UAAU,CAAC,IAAM,iBAAiB,EAAE,MAAM,CAAC,KAAK;4BAChD,OAAO,CAAC,CAAC,OAAO,aAAa;4BAC7B,cAAc,OAAO,aAAa;;;;;;;;;;;;;;;;;;;;;;;AAOhD;uCAEe", "debugId": null}}, {"offset": {"line": 508, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/IMPORTANT/work/export/front/src/utils/formUtils.ts"], "sourcesContent": ["// Form validation utilities\n\nexport interface FormErrors {\n  [key: string]: string | undefined;\n  email?: string;\n  password?: string;\n  firstName?: string;\n  lastName?: string;\n  phone?: string;\n  relation?: string;\n  otherRelation?: string;\n  idImage?: string;\n  children?: string;\n  address?: string;\n  country?: string;\n  governorate?: string;\n  city?: string;\n  general?: string;\n}\n\nexport type PasswordStrength = \"weak\" | \"medium\" | \"strong\" | \"\";\n\n// Function to check password strength\nexport const checkPasswordStrength = (password: string): PasswordStrength => {\n  if (!password) return \"\";\n\n  // Check for minimum length\n  if (password.length < 6) return \"weak\";\n\n  // Check for complexity\n  const hasUppercase = /[A-Z]/.test(password);\n  const hasLowercase = /[a-z]/.test(password);\n  const hasNumbers = /[0-9]/.test(password);\n  const hasSpecialChars = /[!@#$%^&*(),.?\":{}|<>]/.test(password);\n\n  const complexity =\n    (hasUppercase ? 1 : 0) +\n    (hasLowercase ? 1 : 0) +\n    (hasNumbers ? 1 : 0) +\n    (hasSpecialChars ? 1 : 0);\n\n  if (complexity === 4 && password.length >= 8) return \"strong\";\n  if (complexity >= 2 && password.length >= 6) return \"medium\";\n  return \"weak\";\n};\n\n// Function to validate email format\nexport const isValidEmail = (email: string): boolean => {\n  const emailRegex = /^[^\\s@]+@[^\\s@]+\\.[^\\s@]+$/;\n  return emailRegex.test(email);\n};\n\n// Function to validate form data\nexport interface FormData {\n  email: string;\n  password: string;\n  firstName: string;\n  lastName: string;\n  phone?: string;\n  relation: string;\n  otherRelation?: string;\n  address?: string;\n  country?: string;\n  governorate?: string;\n  city?: string;\n}\n\nexport const validateForm = (formData: FormData, validateOptionalFields: boolean = false): FormErrors => {\n  const newErrors: FormErrors = {};\n\n  // Email validation\n  if (!formData.email) {\n    newErrors.email = \"Email is required\";\n  } else if (!isValidEmail(formData.email)) {\n    newErrors.email = \"Please enter a valid email address\";\n  }\n\n  // Password validation\n  if (!formData.password) {\n    newErrors.password = \"Password is required\";\n  } else if (formData.password.length < 6) {\n    newErrors.password = \"Password must be at least 6 characters\";\n  } else {\n    const strength = checkPasswordStrength(formData.password);\n    if (strength === \"weak\") {\n      newErrors.password =\n        \"Password is too weak. Include uppercase, lowercase, and numbers\";\n    }\n  }\n\n  // First name validation\n  if (!formData.firstName) {\n    newErrors.firstName = \"First name is required\";\n  } else if (formData.firstName.length < 2) {\n    newErrors.firstName = \"First name is too short\";\n  }\n\n  // Last name validation\n  if (!formData.lastName) {\n    newErrors.lastName = \"Last name is required\";\n  } else if (formData.lastName.length < 2) {\n    newErrors.lastName = \"Last name is too short\";\n  }\n\n  // Relation validation\n  if (!formData.relation) {\n    newErrors.relation = \"Relationship is required\";\n  } else if (formData.relation === \"other\" && !formData.otherRelation) {\n    newErrors.relation = \"Please specify your relationship\";\n  } else if (formData.relation === \"other\" && formData.otherRelation && formData.otherRelation.length < 2) {\n    newErrors.relation = \"Relationship description is too short\";\n  }\n\n  // Phone validation (always validate if provided)\n  if (!formData.phone) {\n    newErrors.phone = \"Phone number is required\";\n  } else  {\n    // Remove any non-digit characters for validation\n    const phoneDigits = formData.phone.replace(/\\D/g, \"\");\n\n    // Simple validation that only checks the length of the phone number\n    // Most international phone numbers are between 8 and 15 digits\n    if (phoneDigits.length < 8 || phoneDigits.length > 15) {\n      newErrors.phone = \"Please enter a valid phone number (8-15 digits)\";\n    }\n  }\n  if (formData.phone) {\n\n  }\n\n  // Only validate optional fields if they are shown\n  if (validateOptionalFields) {\n    // Address validation (optional but validate if provided)\n    if (formData.address && formData.address.length < 5) {\n      newErrors.address = \"Address is too short\";\n    }\n\n    // Country validation (required if address is provided)\n    if (formData.address && !formData.country) {\n      newErrors.country = \"Country is required when address is provided\";\n    }\n\n    // Governorate validation (required if address is provided and country is Egypt)\n    if (formData.address && formData.country === \"Egypt\" && !formData.governorate) {\n      newErrors.governorate = \"Governorate is required when address is provided\";\n    }\n\n    // City validation (required if address is provided)\n    if (formData.address && !formData.city) {\n      newErrors.city = \"City is required when address is provided\";\n    }\n  }\n\n  return newErrors;\n};\n\n// Country code interface\nexport interface CountryCode {\n  code: string;\n  dialCode: string;\n  name: string;\n}\n\n// Format phone number with country code\nexport const formatPhoneWithCountryCode = (\n  phone: string,\n  countryCode: string,\n  countryCodes: Record<string, CountryCode>\n): string => {\n  if (!phone) return '';\n\n  // Get the selected country's dial code\n  const selectedCountry = countryCodes[countryCode];\n  if (!selectedCountry) return phone;\n\n  // Format the phone number with the country code\n  return phone ?\n    `${selectedCountry?.dialCode}${phone.startsWith('0') ? phone.substring(1) : phone}` :\n    '';\n};\n"], "names": [], "mappings": "AAAA,4BAA4B;;;;;;;AAuBrB,MAAM,wBAAwB,CAAC;IACpC,IAAI,CAAC,UAAU,OAAO;IAEtB,2BAA2B;IAC3B,IAAI,SAAS,MAAM,GAAG,GAAG,OAAO;IAEhC,uBAAuB;IACvB,MAAM,eAAe,QAAQ,IAAI,CAAC;IAClC,MAAM,eAAe,QAAQ,IAAI,CAAC;IAClC,MAAM,aAAa,QAAQ,IAAI,CAAC;IAChC,MAAM,kBAAkB,yBAAyB,IAAI,CAAC;IAEtD,MAAM,aACJ,CAAC,eAAe,IAAI,CAAC,IACrB,CAAC,eAAe,IAAI,CAAC,IACrB,CAAC,aAAa,IAAI,CAAC,IACnB,CAAC,kBAAkB,IAAI,CAAC;IAE1B,IAAI,eAAe,KAAK,SAAS,MAAM,IAAI,GAAG,OAAO;IACrD,IAAI,cAAc,KAAK,SAAS,MAAM,IAAI,GAAG,OAAO;IACpD,OAAO;AACT;AAGO,MAAM,eAAe,CAAC;IAC3B,MAAM,aAAa;IACnB,OAAO,WAAW,IAAI,CAAC;AACzB;AAiBO,MAAM,eAAe,CAAC,UAAoB,yBAAkC,KAAK;IACtF,MAAM,YAAwB,CAAC;IAE/B,mBAAmB;IACnB,IAAI,CAAC,SAAS,KAAK,EAAE;QACnB,UAAU,KAAK,GAAG;IACpB,OAAO,IAAI,CAAC,aAAa,SAAS,KAAK,GAAG;QACxC,UAAU,KAAK,GAAG;IACpB;IAEA,sBAAsB;IACtB,IAAI,CAAC,SAAS,QAAQ,EAAE;QACtB,UAAU,QAAQ,GAAG;IACvB,OAAO,IAAI,SAAS,QAAQ,CAAC,MAAM,GAAG,GAAG;QACvC,UAAU,QAAQ,GAAG;IACvB,OAAO;QACL,MAAM,WAAW,sBAAsB,SAAS,QAAQ;QACxD,IAAI,aAAa,QAAQ;YACvB,UAAU,QAAQ,GAChB;QACJ;IACF;IAEA,wBAAwB;IACxB,IAAI,CAAC,SAAS,SAAS,EAAE;QACvB,UAAU,SAAS,GAAG;IACxB,OAAO,IAAI,SAAS,SAAS,CAAC,MAAM,GAAG,GAAG;QACxC,UAAU,SAAS,GAAG;IACxB;IAEA,uBAAuB;IACvB,IAAI,CAAC,SAAS,QAAQ,EAAE;QACtB,UAAU,QAAQ,GAAG;IACvB,OAAO,IAAI,SAAS,QAAQ,CAAC,MAAM,GAAG,GAAG;QACvC,UAAU,QAAQ,GAAG;IACvB;IAEA,sBAAsB;IACtB,IAAI,CAAC,SAAS,QAAQ,EAAE;QACtB,UAAU,QAAQ,GAAG;IACvB,OAAO,IAAI,SAAS,QAAQ,KAAK,WAAW,CAAC,SAAS,aAAa,EAAE;QACnE,UAAU,QAAQ,GAAG;IACvB,OAAO,IAAI,SAAS,QAAQ,KAAK,WAAW,SAAS,aAAa,IAAI,SAAS,aAAa,CAAC,MAAM,GAAG,GAAG;QACvG,UAAU,QAAQ,GAAG;IACvB;IAEA,iDAAiD;IACjD,IAAI,CAAC,SAAS,KAAK,EAAE;QACnB,UAAU,KAAK,GAAG;IACpB,OAAQ;QACN,iDAAiD;QACjD,MAAM,cAAc,SAAS,KAAK,CAAC,OAAO,CAAC,OAAO;QAElD,oEAAoE;QACpE,+DAA+D;QAC/D,IAAI,YAAY,MAAM,GAAG,KAAK,YAAY,MAAM,GAAG,IAAI;YACrD,UAAU,KAAK,GAAG;QACpB;IACF;IACA,IAAI,SAAS,KAAK,EAAE,CAEpB;IAEA,kDAAkD;IAClD,IAAI,wBAAwB;QAC1B,yDAAyD;QACzD,IAAI,SAAS,OAAO,IAAI,SAAS,OAAO,CAAC,MAAM,GAAG,GAAG;YACnD,UAAU,OAAO,GAAG;QACtB;QAEA,uDAAuD;QACvD,IAAI,SAAS,OAAO,IAAI,CAAC,SAAS,OAAO,EAAE;YACzC,UAAU,OAAO,GAAG;QACtB;QAEA,gFAAgF;QAChF,IAAI,SAAS,OAAO,IAAI,SAAS,OAAO,KAAK,WAAW,CAAC,SAAS,WAAW,EAAE;YAC7E,UAAU,WAAW,GAAG;QAC1B;QAEA,oDAAoD;QACpD,IAAI,SAAS,OAAO,IAAI,CAAC,SAAS,IAAI,EAAE;YACtC,UAAU,IAAI,GAAG;QACnB;IACF;IAEA,OAAO;AACT;AAUO,MAAM,6BAA6B,CACxC,OACA,aACA;IAEA,IAAI,CAAC,OAAO,OAAO;IAEnB,uCAAuC;IACvC,MAAM,kBAAkB,YAAY,CAAC,YAAY;IACjD,IAAI,CAAC,iBAAiB,OAAO;IAE7B,gDAAgD;IAChD,OAAO,QACL,GAAG,iBAAiB,WAAW,MAAM,UAAU,CAAC,OAAO,MAAM,SAAS,CAAC,KAAK,OAAO,GACnF;AACJ", "debugId": null}}, {"offset": {"line": 620, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/IMPORTANT/work/export/front/src/utils/countryData.ts"], "sourcesContent": ["// Country data for dropdowns and phone inputs\n\nexport interface CountryCode {\n  code: string;\n  name: string;\n  dialCode: string;\n  flag: string;\n}\n\n// List of country codes (Arab and European countries first, then others)\nexport const countryCodes: CountryCode[] = [\n  // Arab Countries\n  { code: \"EG\", name: \"Egypt\", dialCode: \"+20\", flag: \"🇪🇬\" },\n  { code: \"SA\", name: \"Saudi Arabia\", dialCode: \"+966\", flag: \"🇸🇦\" },\n  { code: \"AE\", name: \"United Arab Emirates\", dialCode: \"+971\", flag: \"🇦🇪\" },\n  { code: \"KW\", name: \"Kuwait\", dialCode: \"+965\", flag: \"🇰🇼\" },\n  { code: \"QA\", name: \"Qatar\", dialCode: \"+974\", flag: \"🇶🇦\" },\n  { code: \"OM\", name: \"Oman\", dialCode: \"+968\", flag: \"🇴🇲\" },\n  { code: \"BH\", name: \"Bahrain\", dialCode: \"+973\", flag: \"🇧🇭\" },\n  { code: \"<PERSON><PERSON>\", name: \"Jordan\", dialCode: \"+962\", flag: \"🇯🇴\" },\n  { code: \"LB\", name: \"Lebanon\", dialCode: \"+961\", flag: \"🇱🇧\" },\n  { code: \"SY\", name: \"Syria\", dialCode: \"+963\", flag: \"🇸🇾\" },\n  { code: \"IQ\", name: \"Iraq\", dialCode: \"+964\", flag: \"🇮🇶\" },\n  { code: \"PS\", name: \"Palestine\", dialCode: \"+970\", flag: \"🇵🇸\" },\n  { code: \"YE\", name: \"Yemen\", dialCode: \"+967\", flag: \"🇾🇪\" },\n  { code: \"DZ\", name: \"Algeria\", dialCode: \"+213\", flag: \"🇩🇿\" },\n  { code: \"MA\", name: \"Morocco\", dialCode: \"+212\", flag: \"🇲🇦\" },\n  { code: \"TN\", name: \"Tunisia\", dialCode: \"+216\", flag: \"🇹🇳\" },\n  { code: \"LY\", name: \"Libya\", dialCode: \"+218\", flag: \"🇱🇾\" },\n  { code: \"SD\", name: \"Sudan\", dialCode: \"+249\", flag: \"🇸🇩\" },\n  { code: \"MR\", name: \"Mauritania\", dialCode: \"+222\", flag: \"🇲🇷\" },\n  { code: \"SO\", name: \"Somalia\", dialCode: \"+252\", flag: \"🇸🇴\" },\n  { code: \"DJ\", name: \"Djibouti\", dialCode: \"+253\", flag: \"🇩🇯\" },\n  { code: \"KM\", name: \"Comoros\", dialCode: \"+269\", flag: \"🇰🇲\" },\n\n  // European Countries\n  { code: \"GB\", name: \"United Kingdom\", dialCode: \"+44\", flag: \"🇬🇧\" },\n  { code: \"DE\", name: \"Germany\", dialCode: \"+49\", flag: \"🇩🇪\" },\n  { code: \"FR\", name: \"France\", dialCode: \"+33\", flag: \"🇫🇷\" },\n  { code: \"IT\", name: \"Italy\", dialCode: \"+39\", flag: \"🇮🇹\" },\n  { code: \"ES\", name: \"Spain\", dialCode: \"+34\", flag: \"🇪🇸\" },\n  { code: \"RU\", name: \"Russia\", dialCode: \"+7\", flag: \"🇷🇺\" },\n  { code: \"UA\", name: \"Ukraine\", dialCode: \"+380\", flag: \"🇺🇦\" },\n  { code: \"PL\", name: \"Poland\", dialCode: \"+48\", flag: \"🇵🇱\" },\n  { code: \"RO\", name: \"Romania\", dialCode: \"+40\", flag: \"🇷🇴\" },\n  { code: \"NL\", name: \"Netherlands\", dialCode: \"+31\", flag: \"🇳🇱\" },\n  { code: \"BE\", name: \"Belgium\", dialCode: \"+32\", flag: \"🇧🇪\" },\n  { code: \"SE\", name: \"Sweden\", dialCode: \"+46\", flag: \"🇸🇪\" },\n  { code: \"NO\", name: \"Norway\", dialCode: \"+47\", flag: \"🇳🇴\" },\n  { code: \"DK\", name: \"Denmark\", dialCode: \"+45\", flag: \"🇩🇰\" },\n  { code: \"FI\", name: \"Finland\", dialCode: \"+358\", flag: \"🇫🇮\" },\n  { code: \"CH\", name: \"Switzerland\", dialCode: \"+41\", flag: \"🇨🇭\" },\n  { code: \"AT\", name: \"Austria\", dialCode: \"+43\", flag: \"🇦🇹\" },\n  { code: \"PT\", name: \"Portugal\", dialCode: \"+351\", flag: \"🇵🇹\" },\n  { code: \"GR\", name: \"Greece\", dialCode: \"+30\", flag: \"🇬🇷\" },\n  { code: \"TR\", name: \"Turkey\", dialCode: \"+90\", flag: \"🇹🇷\" },\n  { code: \"HU\", name: \"Hungary\", dialCode: \"+36\", flag: \"🇭🇺\" },\n  { code: \"CZ\", name: \"Czech Republic\", dialCode: \"+420\", flag: \"🇨🇿\" },\n  { code: \"IE\", name: \"Ireland\", dialCode: \"+353\", flag: \"🇮🇪\" },\n  { code: \"BG\", name: \"Bulgaria\", dialCode: \"+359\", flag: \"🇧🇬\" },\n  { code: \"HR\", name: \"Croatia\", dialCode: \"+385\", flag: \"🇭🇷\" },\n  { code: \"LT\", name: \"Lithuania\", dialCode: \"+370\", flag: \"🇱🇹\" },\n  { code: \"SK\", name: \"Slovakia\", dialCode: \"+421\", flag: \"🇸🇰\" },\n  { code: \"LV\", name: \"Latvia\", dialCode: \"+371\", flag: \"🇱🇻\" },\n  { code: \"SI\", name: \"Slovenia\", dialCode: \"+386\", flag: \"🇸🇮\" },\n  { code: \"EE\", name: \"Estonia\", dialCode: \"+372\", flag: \"🇪🇪\" },\n  { code: \"CY\", name: \"Cyprus\", dialCode: \"+357\", flag: \"🇨🇾\" },\n  { code: \"LU\", name: \"Luxembourg\", dialCode: \"+352\", flag: \"🇱🇺\" },\n  { code: \"MT\", name: \"Malta\", dialCode: \"+356\", flag: \"🇲🇹\" },\n  { code: \"IS\", name: \"Iceland\", dialCode: \"+354\", flag: \"🇮🇸\" },\n  { code: \"AL\", name: \"Albania\", dialCode: \"+355\", flag: \"🇦🇱\" },\n  { code: \"MD\", name: \"Moldova\", dialCode: \"+373\", flag: \"🇲🇩\" },\n  { code: \"MK\", name: \"North Macedonia\", dialCode: \"+389\", flag: \"🇲🇰\" },\n  { code: \"ME\", name: \"Montenegro\", dialCode: \"+382\", flag: \"🇲🇪\" },\n  { code: \"RS\", name: \"Serbia\", dialCode: \"+381\", flag: \"🇷🇸\" },\n  { code: \"BA\", name: \"Bosnia and Herzegovina\", dialCode: \"+387\", flag: \"🇧🇦\" },\n  { code: \"MC\", name: \"Monaco\", dialCode: \"+377\", flag: \"🇲🇨\" },\n  { code: \"LI\", name: \"Liechtenstein\", dialCode: \"+423\", flag: \"🇱🇮\" },\n  { code: \"SM\", name: \"San Marino\", dialCode: \"+378\", flag: \"🇸🇲\" },\n  { code: \"VA\", name: \"Vatican City\", dialCode: \"+379\", flag: \"🇻🇦\" },\n  { code: \"AD\", name: \"Andorra\", dialCode: \"+376\", flag: \"🇦🇩\" },\n\n  // Other major countries\n  { code: \"US\", name: \"United States\", dialCode: \"+1\", flag: \"🇺🇸\" },\n  { code: \"CA\", name: \"Canada\", dialCode: \"+1\", flag: \"🇨🇦\" },\n  { code: \"AU\", name: \"Australia\", dialCode: \"+61\", flag: \"🇦🇺\" },\n  { code: \"IN\", name: \"India\", dialCode: \"+91\", flag: \"🇮🇳\" },\n  { code: \"CN\", name: \"China\", dialCode: \"+86\", flag: \"🇨🇳\" },\n  { code: \"JP\", name: \"Japan\", dialCode: \"+81\", flag: \"🇯🇵\" },\n  { code: \"BR\", name: \"Brazil\", dialCode: \"+55\", flag: \"🇧🇷\" },\n];\n\n// List of Egyptian governorates\nexport const egyptianGovernorates = [\n  \"Cairo\",\n  \"Alexandria\",\n  \"Giza\",\n  \"Qalyubia\",\n  \"Sharqia\",\n  \"Gharbia\",\n  \"Menoufia\",\n  \"Beheira\",\n  \"Kafr El Sheikh\",\n  \"Damietta\",\n  \"Port Said\",\n  \"Ismailia\",\n  \"Suez\",\n  \"North Sinai\",\n  \"South Sinai\",\n  \"Fayoum\",\n  \"Beni Suef\",\n  \"Minya\",\n  \"Assiut\",\n  \"Sohag\",\n  \"Qena\",\n  \"Luxor\",\n  \"Aswan\",\n  \"Red Sea\",\n  \"New Valley\",\n  \"Matrouh\",\n];\n\n// List of Egyptian cities for the city dropdown\nexport const egyptianCities = [\n  \"Cairo\",\n  \"Alexandria\",\n  \"Giza\",\n  \"Shubra El Kheima\",\n  \"Port Said\",\n  \"Suez\",\n  \"Luxor\",\n  \"Aswan\",\n  \"Asyut\",\n  \"Ismailia\",\n  \"Faiyum\",\n  \"Zagazig\",\n  \"Damietta\",\n  \"Mansoura\",\n  \"Tanta\",\n  \"Hurghada\",\n  \"Sohag\",\n  \"Shibin El Kom\",\n  \"Beni Suef\",\n  \"Qena\",\n  \"Minya\",\n  \"Damanhur\",\n  \"El Mahalla El Kubra\",\n  \"Kafr El Sheikh\",\n  \"Arish\",\n  \"Mallawi\",\n  \"Banha\",\n  \"Belbeis\",\n  \"Marsa Matruh\",\n  \"Idfu\",\n  \"Mit Ghamr\",\n  \"Al-Hamidiyya\",\n  \"Desouk\",\n  \"Qalyub\",\n  \"Abu Kabir\",\n  \"Kafr El Dawwar\",\n  \"Girga\",\n  \"Akhmim\",\n  \"El Tor\",\n  \"Dahab\",\n  \"Sharm El Sheikh\",\n  \"New Cairo\",\n  \"6th of October City\",\n  \"10th of Ramadan City\",\n  \"El Obour\",\n  \"El Shorouk\",\n  \"New Borg El Arab\",\n];\n"], "names": [], "mappings": "AAAA,8CAA8C;;;;;;AAUvC,MAAM,eAA8B;IACzC,iBAAiB;IACjB;QAAE,MAAM;QAAM,MAAM;QAAS,UAAU;QAAO,MAAM;IAAO;IAC3D;QAAE,MAAM;QAAM,MAAM;QAAgB,UAAU;QAAQ,MAAM;IAAO;IACnE;QAAE,MAAM;QAAM,MAAM;QAAwB,UAAU;QAAQ,MAAM;IAAO;IAC3E;QAAE,MAAM;QAAM,MAAM;QAAU,UAAU;QAAQ,MAAM;IAAO;IAC7D;QAAE,MAAM;QAAM,MAAM;QAAS,UAAU;QAAQ,MAAM;IAAO;IAC5D;QAAE,MAAM;QAAM,MAAM;QAAQ,UAAU;QAAQ,MAAM;IAAO;IAC3D;QAAE,MAAM;QAAM,MAAM;QAAW,UAAU;QAAQ,MAAM;IAAO;IAC9D;QAAE,MAAM;QAAM,MAAM;QAAU,UAAU;QAAQ,MAAM;IAAO;IAC7D;QAAE,MAAM;QAAM,MAAM;QAAW,UAAU;QAAQ,MAAM;IAAO;IAC9D;QAAE,MAAM;QAAM,MAAM;QAAS,UAAU;QAAQ,MAAM;IAAO;IAC5D;QAAE,MAAM;QAAM,MAAM;QAAQ,UAAU;QAAQ,MAAM;IAAO;IAC3D;QAAE,MAAM;QAAM,MAAM;QAAa,UAAU;QAAQ,MAAM;IAAO;IAChE;QAAE,MAAM;QAAM,MAAM;QAAS,UAAU;QAAQ,MAAM;IAAO;IAC5D;QAAE,MAAM;QAAM,MAAM;QAAW,UAAU;QAAQ,MAAM;IAAO;IAC9D;QAAE,MAAM;QAAM,MAAM;QAAW,UAAU;QAAQ,MAAM;IAAO;IAC9D;QAAE,MAAM;QAAM,MAAM;QAAW,UAAU;QAAQ,MAAM;IAAO;IAC9D;QAAE,MAAM;QAAM,MAAM;QAAS,UAAU;QAAQ,MAAM;IAAO;IAC5D;QAAE,MAAM;QAAM,MAAM;QAAS,UAAU;QAAQ,MAAM;IAAO;IAC5D;QAAE,MAAM;QAAM,MAAM;QAAc,UAAU;QAAQ,MAAM;IAAO;IACjE;QAAE,MAAM;QAAM,MAAM;QAAW,UAAU;QAAQ,MAAM;IAAO;IAC9D;QAAE,MAAM;QAAM,MAAM;QAAY,UAAU;QAAQ,MAAM;IAAO;IAC/D;QAAE,MAAM;QAAM,MAAM;QAAW,UAAU;QAAQ,MAAM;IAAO;IAE9D,qBAAqB;IACrB;QAAE,MAAM;QAAM,MAAM;QAAkB,UAAU;QAAO,MAAM;IAAO;IACpE;QAAE,MAAM;QAAM,MAAM;QAAW,UAAU;QAAO,MAAM;IAAO;IAC7D;QAAE,MAAM;QAAM,MAAM;QAAU,UAAU;QAAO,MAAM;IAAO;IAC5D;QAAE,MAAM;QAAM,MAAM;QAAS,UAAU;QAAO,MAAM;IAAO;IAC3D;QAAE,MAAM;QAAM,MAAM;QAAS,UAAU;QAAO,MAAM;IAAO;IAC3D;QAAE,MAAM;QAAM,MAAM;QAAU,UAAU;QAAM,MAAM;IAAO;IAC3D;QAAE,MAAM;QAAM,MAAM;QAAW,UAAU;QAAQ,MAAM;IAAO;IAC9D;QAAE,MAAM;QAAM,MAAM;QAAU,UAAU;QAAO,MAAM;IAAO;IAC5D;QAAE,MAAM;QAAM,MAAM;QAAW,UAAU;QAAO,MAAM;IAAO;IAC7D;QAAE,MAAM;QAAM,MAAM;QAAe,UAAU;QAAO,MAAM;IAAO;IACjE;QAAE,MAAM;QAAM,MAAM;QAAW,UAAU;QAAO,MAAM;IAAO;IAC7D;QAAE,MAAM;QAAM,MAAM;QAAU,UAAU;QAAO,MAAM;IAAO;IAC5D;QAAE,MAAM;QAAM,MAAM;QAAU,UAAU;QAAO,MAAM;IAAO;IAC5D;QAAE,MAAM;QAAM,MAAM;QAAW,UAAU;QAAO,MAAM;IAAO;IAC7D;QAAE,MAAM;QAAM,MAAM;QAAW,UAAU;QAAQ,MAAM;IAAO;IAC9D;QAAE,MAAM;QAAM,MAAM;QAAe,UAAU;QAAO,MAAM;IAAO;IACjE;QAAE,MAAM;QAAM,MAAM;QAAW,UAAU;QAAO,MAAM;IAAO;IAC7D;QAAE,MAAM;QAAM,MAAM;QAAY,UAAU;QAAQ,MAAM;IAAO;IAC/D;QAAE,MAAM;QAAM,MAAM;QAAU,UAAU;QAAO,MAAM;IAAO;IAC5D;QAAE,MAAM;QAAM,MAAM;QAAU,UAAU;QAAO,MAAM;IAAO;IAC5D;QAAE,MAAM;QAAM,MAAM;QAAW,UAAU;QAAO,MAAM;IAAO;IAC7D;QAAE,MAAM;QAAM,MAAM;QAAkB,UAAU;QAAQ,MAAM;IAAO;IACrE;QAAE,MAAM;QAAM,MAAM;QAAW,UAAU;QAAQ,MAAM;IAAO;IAC9D;QAAE,MAAM;QAAM,MAAM;QAAY,UAAU;QAAQ,MAAM;IAAO;IAC/D;QAAE,MAAM;QAAM,MAAM;QAAW,UAAU;QAAQ,MAAM;IAAO;IAC9D;QAAE,MAAM;QAAM,MAAM;QAAa,UAAU;QAAQ,MAAM;IAAO;IAChE;QAAE,MAAM;QAAM,MAAM;QAAY,UAAU;QAAQ,MAAM;IAAO;IAC/D;QAAE,MAAM;QAAM,MAAM;QAAU,UAAU;QAAQ,MAAM;IAAO;IAC7D;QAAE,MAAM;QAAM,MAAM;QAAY,UAAU;QAAQ,MAAM;IAAO;IAC/D;QAAE,MAAM;QAAM,MAAM;QAAW,UAAU;QAAQ,MAAM;IAAO;IAC9D;QAAE,MAAM;QAAM,MAAM;QAAU,UAAU;QAAQ,MAAM;IAAO;IAC7D;QAAE,MAAM;QAAM,MAAM;QAAc,UAAU;QAAQ,MAAM;IAAO;IACjE;QAAE,MAAM;QAAM,MAAM;QAAS,UAAU;QAAQ,MAAM;IAAO;IAC5D;QAAE,MAAM;QAAM,MAAM;QAAW,UAAU;QAAQ,MAAM;IAAO;IAC9D;QAAE,MAAM;QAAM,MAAM;QAAW,UAAU;QAAQ,MAAM;IAAO;IAC9D;QAAE,MAAM;QAAM,MAAM;QAAW,UAAU;QAAQ,MAAM;IAAO;IAC9D;QAAE,MAAM;QAAM,MAAM;QAAmB,UAAU;QAAQ,MAAM;IAAO;IACtE;QAAE,MAAM;QAAM,MAAM;QAAc,UAAU;QAAQ,MAAM;IAAO;IACjE;QAAE,MAAM;QAAM,MAAM;QAAU,UAAU;QAAQ,MAAM;IAAO;IAC7D;QAAE,MAAM;QAAM,MAAM;QAA0B,UAAU;QAAQ,MAAM;IAAO;IAC7E;QAAE,MAAM;QAAM,MAAM;QAAU,UAAU;QAAQ,MAAM;IAAO;IAC7D;QAAE,MAAM;QAAM,MAAM;QAAiB,UAAU;QAAQ,MAAM;IAAO;IACpE;QAAE,MAAM;QAAM,MAAM;QAAc,UAAU;QAAQ,MAAM;IAAO;IACjE;QAAE,MAAM;QAAM,MAAM;QAAgB,UAAU;QAAQ,MAAM;IAAO;IACnE;QAAE,MAAM;QAAM,MAAM;QAAW,UAAU;QAAQ,MAAM;IAAO;IAE9D,wBAAwB;IACxB;QAAE,MAAM;QAAM,MAAM;QAAiB,UAAU;QAAM,MAAM;IAAO;IAClE;QAAE,MAAM;QAAM,MAAM;QAAU,UAAU;QAAM,MAAM;IAAO;IAC3D;QAAE,MAAM;QAAM,MAAM;QAAa,UAAU;QAAO,MAAM;IAAO;IAC/D;QAAE,MAAM;QAAM,MAAM;QAAS,UAAU;QAAO,MAAM;IAAO;IAC3D;QAAE,MAAM;QAAM,MAAM;QAAS,UAAU;QAAO,MAAM;IAAO;IAC3D;QAAE,MAAM;QAAM,MAAM;QAAS,UAAU;QAAO,MAAM;IAAO;IAC3D;QAAE,MAAM;QAAM,MAAM;QAAU,UAAU;QAAO,MAAM;IAAO;CAC7D;AAGM,MAAM,uBAAuB;IAClC;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;CACD;AAGM,MAAM,iBAAiB;IAC5B;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;CACD", "debugId": null}}, {"offset": {"line": 1158, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/IMPORTANT/work/export/front/src/utils/accountUtils.ts"], "sourcesContent": ["import { FormErrors, PasswordStrength, checkPasswordStrength } from \"./formUtils\";\nimport { countryCodes } from \"./countryData\";\nimport { ProfileFormData, User, ExtendedUserType } from \"../types/account\";\n\nexport const extractPhoneNumber = (\n  phoneWithCode: string\n): { phoneNumber: string; countryCode: string } => {\n  let phoneNumber = \"\";\n  let countryCode = \"EG\";\n\n  if (!phoneWithCode) return { phoneNumber, countryCode };\n\n  console.log(\"Extracting from phone:\", phoneWithCode);\n\n  // Try to find the country code from the phone number\n  for (const country of countryCodes) {\n    if (phoneWithCode.startsWith(country.dialCode)) {\n      countryCode = country.code;\n      phoneNumber = phoneWithCode.substring(country.dialCode.length);\n      console.log(`Found country ${country.name} (${country.code}) with dial code ${country.dialCode}, remaining phone: ${phoneNumber}`);\n      return { phoneNumber, countryCode };\n    }\n  }\n\n  // If we couldn't find a matching country code, just return the whole number\n  console.log(\"No matching country code found, using default:\", countryCode);\n  return { phoneNumber: phoneWithCode, countryCode };\n};\n\nexport const getCountryCodeFromName = (countryName: string): string => {\n  if (!countryName) return \"EG\";\n\n  const country = countryCodes.find(\n    (c) => c.name.toLowerCase() === countryName.toLowerCase()\n  );\n\n  return country ? country.code : \"EG\";\n};\n\nexport const validateProfileForm = (\n  formData: ProfileFormData,\n  validateOptionalFields: boolean = false\n): { [key: string]: string } => {\n  const newErrors: { [key: string]: string } = {};\n\n  if (!formData.email) {\n    newErrors.email = \"البريد الإلكتروني مطلوب\";\n  } else if (!/^[^\\s@]+@[^\\s@]+\\.[^\\s@]+$/.test(formData.email)) {\n    newErrors.email = \"يرجى إدخال بريد إلكتروني صحيح\";\n  }\n\n  if (!formData.firstName) {\n    newErrors.firstName = \"الاسم الأول مطلوب\";\n  } else if (formData.firstName.length < 2) {\n    newErrors.firstName = \"الاسم الأول قصير جدًا\";\n  }\n\n  if (!formData.lastName) {\n    newErrors.lastName = \"اسم العائلة مطلوب\";\n  } else if (formData.lastName.length < 2) {\n    newErrors.lastName = \"اسم العائلة قصير جدًا\";\n  }\n\n  // التحقق من رقم الهاتف (مطلوب)\n  if (!formData.phone) {\n    newErrors.phone = \"رقم الهاتف مطلوب\";\n  } else {\n    const phoneDigits = formData.phone.replace(/\\D/g, \"\");\n\n    if (phoneDigits.length < 8 || phoneDigits.length > 15) {\n      newErrors.phone = \"يرجى إدخال رقم هاتف صحيح (8-15 رقم)\";\n    } else if (!/^[0-9]+$/.test(phoneDigits)) {\n      newErrors.phone = \"رقم الهاتف يجب أن يحتوي على أرقام فقط\";\n    }\n\n    if (formData.phoneCountry === \"EG\" && phoneDigits.length !== 10 && phoneDigits.length !== 11) {\n      newErrors.phone = \"أرقام الهاتف المصرية يجب أن تكون 10-11 رقم\";\n    }\n  }\n\n  // التحقق من العلاقة\n  if (!formData.relation) {\n    newErrors.relation = \"يرجى اختيار صلتك بالطفل\";\n  } else if (formData.relation === \"other\" && (!formData.otherRelation || formData.otherRelation.trim() === \"\")) {\n    newErrors.otherRelation = \"يرجى تحديد صلتك بالطفل\";\n  }\n\n  // التحقق من صورة الهوية\n  if (!formData.idImage) {\n    newErrors.idImage = \"صورة الهوية مطلوبة\";\n  }\n\n  // التحقق من بيانات الأطفال إذا وجدت\n  if (formData.children && formData.children.length > 0) {\n    formData.children.forEach((child) => {\n      if (!child.name || child.name.trim() === \"\") {\n        newErrors[`child_${child.id}_name`] = \"اسم الطفل مطلوب\";\n      }\n\n      if (!child.class || child.class.trim() === \"\") {\n        newErrors[`child_${child.id}_class`] = \"الصف مطلوب\";\n      }\n\n      if (!child.stage || child.stage.trim() === \"\") {\n        newErrors[`child_${child.id}_stage`] = \"المرحلة الدراسية مطلوبة\";\n      }\n    });\n  } else {\n    newErrors.children = \"مطلوب إدخال طفل واحد على الأقل\";\n  }\n\n  if (validateOptionalFields) {\n    if (formData.address && formData.address.length < 5) {\n      newErrors.address = \"العنوان قصير جدًا\";\n    }\n\n    if (formData.address && !formData.country) {\n      newErrors.country = \"الدولة مطلوبة عند إدخال العنوان\";\n    }\n\n    if (formData.address && formData.country === \"Egypt\" && !formData.governorate) {\n      newErrors.governorate = \"المحافظة مطلوبة عند إدخال العنوان\";\n    }\n\n    if (formData.address && !formData.city) {\n      newErrors.city = \"المدينة مطلوبة عند إدخال العنوان\";\n    }\n  }\n\n  return newErrors;\n};\n\nexport const validatePasswordForm = (\n  currentPassword: string,\n  newPassword: string,\n  confirmPassword: string\n): FormErrors => {\n  const newErrors: FormErrors = {};\n\n  if (!currentPassword) {\n    newErrors.password = \"كلمة المرور الحالية مطلوبة\";\n  }\n\n  if (!newPassword) {\n    newErrors.password = \"كلمة المرور الجديدة مطلوبة\";\n  } else if (newPassword.length < 6) {\n    newErrors.password = \"يجب أن تكون كلمة المرور 6 أحرف على الأقل\";\n  } else {\n    const strength = checkPasswordStrength(newPassword);\n    if (strength === \"weak\") {\n      newErrors.password = \"كلمة المرور ضعيفة جدًا. يجب أن تحتوي على حروف كبيرة وصغيرة وأرقام\";\n    }\n  }\n\n  if (!confirmPassword) {\n    newErrors.password = \"يرجى تأكيد كلمة المرور\";\n  } else if (newPassword !== confirmPassword) {\n    newErrors.password = \"كلمتا المرور الجديدتان غير متطابقتين\";\n  }\n\n  return newErrors;\n};\n\nexport const hasProfileFormChanged = (\n  profileForm: ProfileFormData,\n  user: ExtendedUserType | null,\n  showOptionalFields: boolean\n): boolean => {\n  if (!user) return false;\n\n  if (\n    profileForm.firstName !== user.first_name ||\n    profileForm.lastName !== user.last_name ||\n    profileForm.email !== user.email\n  ) {\n    return true;\n  }\n\n  if (showOptionalFields) {\n    // تنسيق رقم الهاتف مع رمز الدولة\n    const selectedCountry = countryCodes.find(c => c.code === profileForm.phoneCountry);\n    const dialCode = selectedCountry?.dialCode || \"\";\n    const phoneNumber = profileForm.phone || \"\";\n    const formattedPhone = phoneNumber\n      ? `${dialCode}${phoneNumber.startsWith('0') ? phoneNumber.substring(1) : phoneNumber}`\n      : \"\";\n\n    const userPhone = (user as ExtendedUserType).phone_number || user.phone || \"\";\n\n    if (\n      formattedPhone !== userPhone ||\n      profileForm.address !== (user.address || \"\") ||\n      profileForm.country !== (user.country || \"Egypt\") ||\n      profileForm.city !== (user.city || \"\") ||\n      profileForm.governorate !== (user.governorate || \"\") ||\n      profileForm.countryCode !==\n        (user.country_code || getCountryCodeFromName(user.country || \"Egypt\"))\n    ) {\n      return true;\n    }\n  }\n\n  return false;\n};\n"], "names": [], "mappings": ";;;;;;;AAAA;AACA;;;AAGO,MAAM,qBAAqB,CAChC;IAEA,IAAI,cAAc;IAClB,IAAI,cAAc;IAElB,IAAI,CAAC,eAAe,OAAO;QAAE;QAAa;IAAY;IAEtD,QAAQ,GAAG,CAAC,0BAA0B;IAEtC,qDAAqD;IACrD,KAAK,MAAM,WAAW,2HAAA,CAAA,eAAY,CAAE;QAClC,IAAI,cAAc,UAAU,CAAC,QAAQ,QAAQ,GAAG;YAC9C,cAAc,QAAQ,IAAI;YAC1B,cAAc,cAAc,SAAS,CAAC,QAAQ,QAAQ,CAAC,MAAM;YAC7D,QAAQ,GAAG,CAAC,CAAC,cAAc,EAAE,QAAQ,IAAI,CAAC,EAAE,EAAE,QAAQ,IAAI,CAAC,iBAAiB,EAAE,QAAQ,QAAQ,CAAC,mBAAmB,EAAE,aAAa;YACjI,OAAO;gBAAE;gBAAa;YAAY;QACpC;IACF;IAEA,4EAA4E;IAC5E,QAAQ,GAAG,CAAC,kDAAkD;IAC9D,OAAO;QAAE,aAAa;QAAe;IAAY;AACnD;AAEO,MAAM,yBAAyB,CAAC;IACrC,IAAI,CAAC,aAAa,OAAO;IAEzB,MAAM,UAAU,2HAAA,CAAA,eAAY,CAAC,IAAI,CAC/B,CAAC,IAAM,EAAE,IAAI,CAAC,WAAW,OAAO,YAAY,WAAW;IAGzD,OAAO,UAAU,QAAQ,IAAI,GAAG;AAClC;AAEO,MAAM,sBAAsB,CACjC,UACA,yBAAkC,KAAK;IAEvC,MAAM,YAAuC,CAAC;IAE9C,IAAI,CAAC,SAAS,KAAK,EAAE;QACnB,UAAU,KAAK,GAAG;IACpB,OAAO,IAAI,CAAC,6BAA6B,IAAI,CAAC,SAAS,KAAK,GAAG;QAC7D,UAAU,KAAK,GAAG;IACpB;IAEA,IAAI,CAAC,SAAS,SAAS,EAAE;QACvB,UAAU,SAAS,GAAG;IACxB,OAAO,IAAI,SAAS,SAAS,CAAC,MAAM,GAAG,GAAG;QACxC,UAAU,SAAS,GAAG;IACxB;IAEA,IAAI,CAAC,SAAS,QAAQ,EAAE;QACtB,UAAU,QAAQ,GAAG;IACvB,OAAO,IAAI,SAAS,QAAQ,CAAC,MAAM,GAAG,GAAG;QACvC,UAAU,QAAQ,GAAG;IACvB;IAEA,+BAA+B;IAC/B,IAAI,CAAC,SAAS,KAAK,EAAE;QACnB,UAAU,KAAK,GAAG;IACpB,OAAO;QACL,MAAM,cAAc,SAAS,KAAK,CAAC,OAAO,CAAC,OAAO;QAElD,IAAI,YAAY,MAAM,GAAG,KAAK,YAAY,MAAM,GAAG,IAAI;YACrD,UAAU,KAAK,GAAG;QACpB,OAAO,IAAI,CAAC,WAAW,IAAI,CAAC,cAAc;YACxC,UAAU,KAAK,GAAG;QACpB;QAEA,IAAI,SAAS,YAAY,KAAK,QAAQ,YAAY,MAAM,KAAK,MAAM,YAAY,MAAM,KAAK,IAAI;YAC5F,UAAU,KAAK,GAAG;QACpB;IACF;IAEA,oBAAoB;IACpB,IAAI,CAAC,SAAS,QAAQ,EAAE;QACtB,UAAU,QAAQ,GAAG;IACvB,OAAO,IAAI,SAAS,QAAQ,KAAK,WAAW,CAAC,CAAC,SAAS,aAAa,IAAI,SAAS,aAAa,CAAC,IAAI,OAAO,EAAE,GAAG;QAC7G,UAAU,aAAa,GAAG;IAC5B;IAEA,wBAAwB;IACxB,IAAI,CAAC,SAAS,OAAO,EAAE;QACrB,UAAU,OAAO,GAAG;IACtB;IAEA,oCAAoC;IACpC,IAAI,SAAS,QAAQ,IAAI,SAAS,QAAQ,CAAC,MAAM,GAAG,GAAG;QACrD,SAAS,QAAQ,CAAC,OAAO,CAAC,CAAC;YACzB,IAAI,CAAC,MAAM,IAAI,IAAI,MAAM,IAAI,CAAC,IAAI,OAAO,IAAI;gBAC3C,SAAS,CAAC,CAAC,MAAM,EAAE,MAAM,EAAE,CAAC,KAAK,CAAC,CAAC,GAAG;YACxC;YAEA,IAAI,CAAC,MAAM,KAAK,IAAI,MAAM,KAAK,CAAC,IAAI,OAAO,IAAI;gBAC7C,SAAS,CAAC,CAAC,MAAM,EAAE,MAAM,EAAE,CAAC,MAAM,CAAC,CAAC,GAAG;YACzC;YAEA,IAAI,CAAC,MAAM,KAAK,IAAI,MAAM,KAAK,CAAC,IAAI,OAAO,IAAI;gBAC7C,SAAS,CAAC,CAAC,MAAM,EAAE,MAAM,EAAE,CAAC,MAAM,CAAC,CAAC,GAAG;YACzC;QACF;IACF,OAAO;QACL,UAAU,QAAQ,GAAG;IACvB;IAEA,IAAI,wBAAwB;QAC1B,IAAI,SAAS,OAAO,IAAI,SAAS,OAAO,CAAC,MAAM,GAAG,GAAG;YACnD,UAAU,OAAO,GAAG;QACtB;QAEA,IAAI,SAAS,OAAO,IAAI,CAAC,SAAS,OAAO,EAAE;YACzC,UAAU,OAAO,GAAG;QACtB;QAEA,IAAI,SAAS,OAAO,IAAI,SAAS,OAAO,KAAK,WAAW,CAAC,SAAS,WAAW,EAAE;YAC7E,UAAU,WAAW,GAAG;QAC1B;QAEA,IAAI,SAAS,OAAO,IAAI,CAAC,SAAS,IAAI,EAAE;YACtC,UAAU,IAAI,GAAG;QACnB;IACF;IAEA,OAAO;AACT;AAEO,MAAM,uBAAuB,CAClC,iBACA,aACA;IAEA,MAAM,YAAwB,CAAC;IAE/B,IAAI,CAAC,iBAAiB;QACpB,UAAU,QAAQ,GAAG;IACvB;IAEA,IAAI,CAAC,aAAa;QAChB,UAAU,QAAQ,GAAG;IACvB,OAAO,IAAI,YAAY,MAAM,GAAG,GAAG;QACjC,UAAU,QAAQ,GAAG;IACvB,OAAO;QACL,MAAM,WAAW,CAAA,GAAA,yHAAA,CAAA,wBAAqB,AAAD,EAAE;QACvC,IAAI,aAAa,QAAQ;YACvB,UAAU,QAAQ,GAAG;QACvB;IACF;IAEA,IAAI,CAAC,iBAAiB;QACpB,UAAU,QAAQ,GAAG;IACvB,OAAO,IAAI,gBAAgB,iBAAiB;QAC1C,UAAU,QAAQ,GAAG;IACvB;IAEA,OAAO;AACT;AAEO,MAAM,wBAAwB,CACnC,aACA,MACA;IAEA,IAAI,CAAC,MAAM,OAAO;IAElB,IACE,YAAY,SAAS,KAAK,KAAK,UAAU,IACzC,YAAY,QAAQ,KAAK,KAAK,SAAS,IACvC,YAAY,KAAK,KAAK,KAAK,KAAK,EAChC;QACA,OAAO;IACT;IAEA,IAAI,oBAAoB;QACtB,iCAAiC;QACjC,MAAM,kBAAkB,2HAAA,CAAA,eAAY,CAAC,IAAI,CAAC,CAAA,IAAK,EAAE,IAAI,KAAK,YAAY,YAAY;QAClF,MAAM,WAAW,iBAAiB,YAAY;QAC9C,MAAM,cAAc,YAAY,KAAK,IAAI;QACzC,MAAM,iBAAiB,cACnB,GAAG,WAAW,YAAY,UAAU,CAAC,OAAO,YAAY,SAAS,CAAC,KAAK,aAAa,GACpF;QAEJ,MAAM,YAAY,AAAC,KAA0B,YAAY,IAAI,KAAK,KAAK,IAAI;QAE3E,IACE,mBAAmB,aACnB,YAAY,OAAO,KAAK,CAAC,KAAK,OAAO,IAAI,EAAE,KAC3C,YAAY,OAAO,KAAK,CAAC,KAAK,OAAO,IAAI,OAAO,KAChD,YAAY,IAAI,KAAK,CAAC,KAAK,IAAI,IAAI,EAAE,KACrC,YAAY,WAAW,KAAK,CAAC,KAAK,WAAW,IAAI,EAAE,KACnD,YAAY,WAAW,KACrB,CAAC,KAAK,YAAY,IAAI,uBAAuB,KAAK,OAAO,IAAI,QAAQ,GACvE;YACA,OAAO;QACT;IACF;IAEA,OAAO;AACT", "debugId": null}}, {"offset": {"line": 1320, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/IMPORTANT/work/export/front/src/config.ts"], "sourcesContent": ["// import { useSelector } from \"react-redux\";\r\n\r\n// export const BACKEND_URL = \"https://api.uud.io\";\r\nexport const BACKEND_URL: string = \"http://127.0.0.1:8000\";\r\n// const lang = useSelector((state) => state.lang);\r\n// structure layout\r\n"], "names": [], "mappings": "AAAA,6CAA6C;AAE7C,mDAAmD;;;;AAC5C,MAAM,cAAsB,yBACnC,mDAAmD;CACnD,mBAAmB", "debugId": null}}, {"offset": {"line": 1333, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/IMPORTANT/work/export/front/src/services/authService.ts"], "sourcesContent": ["// Authentication Service\n// This service handles all authentication-related API calls\n\nimport { BACKEND_URL } from \"../config\";\n\n// API Error response interface\nexport interface ApiErrorResponse {\n  [key: string]: string[];\n}\n\n// Custom error interface with response data\nexport interface ApiError extends Error {\n  response?: {\n    data?: ApiErrorResponse | {\n      detail?: string;\n      email?: string;\n      email_verified?: string;\n      email_not_verified?: string;\n      code?: string | string[];\n      [key: string]: any;\n    };\n  };\n}\n\n// Child interface\nexport interface Child {\n  id: number;\n  name: string;\n  class: string;\n  stage: string;\n}\n\n// User interface\nexport interface User {\n  id: number;\n  username: string;\n  email: string;\n  first_name: string;\n  last_name: string;\n  phone?: string;\n  relation?: string;\n  id_image?: string;\n  children?: Child[];\n  address?: string;\n  country?: string;\n  city?: string;\n  country_code?: string;\n  profile_image?: string;\n  date_joined?: string;\n  last_login?: string;\n  is_active?: boolean;\n}\n\n// Token response interface\nexport interface TokenResponse {\n  access: string;\n  refresh: string;\n}\n\n// Base API URL\nconst API_URL = '/api';\n\n// Get tokens from localStorage\nexport const getTokens = (): { access: string | null; refresh: string | null } => {\n  return {\n    access: localStorage.getItem('access_token'),\n    refresh: localStorage.getItem('refresh_token')\n  };\n};\n\n// Set tokens in localStorage\nexport const setTokens = (tokens: TokenResponse): void => {\n  localStorage.setItem('access_token', tokens.access);\n  localStorage.setItem('refresh_token', tokens.refresh);\n\n  // Dispatch custom event to notify other components about authentication change\n  const authChangeEvent = new Event('authChange');\n  window.dispatchEvent(authChangeEvent);\n\n  // Dispatch custom event to notify about cart updates\n  const cartUpdateEvent = new Event('cartUpdated');\n  window.dispatchEvent(cartUpdateEvent);\n};\n\n// Remove tokens from localStorage (logout)\nexport const removeTokens = (): void => {\n  localStorage.removeItem('access_token');\n  localStorage.removeItem('refresh_token');\n};\n\n// For backward compatibility\nexport const getAuthToken = (): string | null => {\n  return getTokens().access;\n};\n\nexport const setAuthToken = (token: string): void => {\n  localStorage.setItem('access_token', token);\n};\n\nexport const removeAuthToken = (): void => {\n  removeTokens();\n};\n\n// Check if the user is authenticated\nexport const isAuthenticated = (): boolean => {\n  const tokens = getTokens();\n  return tokens.access !== null && tokens.access !== '';\n};\n\n// Login user\nexport const login = async (email: string, password: string): Promise<TokenResponse> => {\n  const response = await fetch(`${BACKEND_URL}${API_URL}/token/`, {\n    method: 'POST',\n    headers: {\n      'Content-Type': 'application/json'\n    },\n    body: JSON.stringify({ email, password })\n  });\n\n  if (!response.ok) {\n    const errorData = await response.json();\n    console.log('Login error:', errorData);\n\n    // Create a custom error object with the response data\n    const error = new Error('Login failed') as ApiError;\n    // Add the response data to the error object\n    error.response = { data: errorData };\n    throw error;\n  }\n\n  const data = await response.json();\n  // Not saving tokens in localStorage for security reasons\n  // The calling component should decide how to handle the tokens\n\n  return data;\n};\n\n// Registration response interface\nexport interface RegistrationResponse {\n  user: User;\n  message: string;\n  email_verified: boolean;\n}\n\n// Register user with children data\nexport const register = async (formData: FormData): Promise<RegistrationResponse> => {\n  const response = await fetch(`${BACKEND_URL}${API_URL}/register/`, {\n    method: 'POST',\n    body: formData\n  });\n\n  if (!response.ok) {\n    const errorData = await response.json();\n    console.log('Registration error:', errorData);\n\n    // Create a custom error object with the response data\n    const error = new Error('Registration failed') as ApiError;\n    // Add the response data to the error object\n    error.response = { data: errorData };\n    throw error;\n  }\n\n  return response.json();\n};\n\n// Verify token\nexport const verifyToken = async (token: string): Promise<boolean> => {\n  try {\n    const response = await fetch(`${BACKEND_URL}${API_URL}/token/verify/`, {\n      method: 'POST',\n      headers: {\n        'Content-Type': 'application/json'\n      },\n      body: JSON.stringify({ token })\n    });\n\n    return response.ok;\n  } catch (error) {\n      console.error(\"Authentication error:\", error);\n\n    return false;\n  }\n};\n\n// Refresh token\nexport const refreshToken = async (refresh: string): Promise<TokenResponse> => {\n  const response = await fetch(`${BACKEND_URL}${API_URL}/token/refresh/`, {\n    method: 'POST',\n    headers: {\n      'Content-Type': 'application/json'\n    },\n    body: JSON.stringify({ refresh })\n  });\n\n  if (!response.ok) {\n    throw new Error('Token refresh failed');\n  }\n\n  const data = await response.json();\n\n  // Update only the access token in localStorage\n  localStorage.setItem('access_token', data.access);\n\n  return data;\n};\n\n// Get authenticated user profile\nexport const getUserProfile = async (): Promise<User> => {\n  const tokens = getTokens();\n\n  if (!tokens.access) {\n    throw new Error('No access token found');\n  }\n\n  const response = await fetch(`${BACKEND_URL}${API_URL}/auth/user-data/`, {\n    headers: {\n      'Authorization': `Bearer ${tokens.access}`\n    }\n  });\n\n  if (!response.ok) {\n    if (response.status === 401 && tokens.refresh) {\n      // Try to refresh the token\n      try {\n        const newTokens = await refreshToken(tokens.refresh);\n\n        // Retry the request with the new token\n        const retryResponse = await fetch(`${BACKEND_URL}${API_URL}/users/me/`, {\n          headers: {\n            'Authorization': `Bearer ${newTokens.access}`\n          }\n        });\n\n        if (retryResponse.ok) {\n          return await retryResponse.json();\n        }\n\n        throw new Error('Failed to get user profile after token refresh');\n      } catch (error) {\n        // If refresh fails, logout the user\n        console.error(\"Authentication error:\", error);\n\n        throw new Error('Session expired. Please login again.');\n      }\n    }\n\n    throw new Error('Failed to get user profile');\n  }\n\n  return await response.json();\n};\n\n// Get user data\nexport const getUserData = async (): Promise<User> => {\n  const tokens = getTokens();\n\n  if (!tokens.access) {\n    throw new Error('No access token found');\n  }\n\n  const response = await fetch(`${BACKEND_URL}${API_URL}/auth/user-data/`, {\n    method: 'GET',\n    headers: {\n      'Authorization': `Bearer ${tokens.access}`\n    }\n  });\n\n  if (!response.ok) {\n    if (response.status === 401 && tokens.refresh) {\n      // Try to refresh the token\n      try {\n        const newTokens = await refreshToken(tokens.refresh);\n\n        // Retry the request with the new token\n        const retryResponse = await fetch(`${BACKEND_URL}${API_URL}/auth/user-data/`, {\n          method: 'GET',\n          headers: {\n            'Authorization': `Bearer ${newTokens.access}`\n          }\n        });\n\n        if (retryResponse.ok) {\n          return await retryResponse.json();\n        }\n\n        throw new Error('Failed to get user profile after token refresh');\n      } catch (error) {\n        // If refresh fails, logout the user\n        console.error(\"Authentication error:\", error);\n\n        removeTokens();\n        throw new Error('Session expired. Please login again.');\n      }\n    }\n\n    throw new Error('Failed to get user profile');\n  }\n\n  return await response.json();\n};\n\n// Update user profile\nexport const updateUserProfile = async (formData: FormData): Promise<User> => {\n  const tokens = getTokens();\n\n  if (!tokens.access) {\n    throw new Error('No access token found');\n  }\n\n  const response = await fetch(`${BACKEND_URL}${API_URL}/auth/update/`, {\n    method: 'PUT',\n    headers: {\n      'Authorization': `Bearer ${tokens.access}`,\n    },\n    body: formData\n  });\n\n  if (!response.ok) {\n    if (response.status === 401 && tokens.refresh) {\n      // Try to refresh the token\n      try {\n        const newTokens = await refreshToken(tokens.refresh);\n\n        // Retry the request with the new token\n        const retryResponse = await fetch(`${BACKEND_URL}${API_URL}/auth/update/`, {\n          method: 'PUT',\n          headers: {\n            'Authorization': `Bearer ${newTokens.access}`,\n          },\n          body: formData\n        });\n\n        if (retryResponse.ok) {\n          return await retryResponse.json();\n        }\n\n        throw new Error('Failed to update user profile after token refresh');\n      } catch (error) {\n        // If refresh fails, logout the user\n        console.error(\"Authentication error:\", error);\n        removeTokens();\n        throw new Error('Session expired. Please login again.');\n      }\n    }\n\n    const errorData = await response.json();\n    const error = new Error('Failed to update user profile') as ApiError;\n    error.response = { data: errorData };\n    throw error;\n\n  }\n\n  return await response.json();\n};\n\n// Change password\nexport const changePassword = async (currentPassword: string, newPassword: string): Promise<void> => {\n  const tokens = getTokens();\n\n  if (!tokens.access) {\n    throw new Error('No access token found');\n  }\n\n  const response = await fetch(`${BACKEND_URL}${API_URL}/users/change_password/`, {\n    method: 'POST',\n    headers: {\n      'Authorization': `Bearer ${tokens.access}`,\n      'Content-Type': 'application/json'\n    },\n    body: JSON.stringify({\n      current_password: currentPassword,\n      new_password: newPassword\n    })\n  });\n\n  if (!response.ok) {\n    if (response.status === 401 && tokens.refresh) {\n      // Try to refresh the token\n      try {\n        const newTokens = await refreshToken(tokens.refresh);\n\n        // Retry the request with the new token\n        const retryResponse = await fetch(`${BACKEND_URL}${API_URL}/users/change_password/`, {\n          method: 'POST',\n          headers: {\n            'Authorization': `Bearer ${newTokens.access}`,\n            'Content-Type': 'application/json'\n          },\n          body: JSON.stringify({\n            current_password: currentPassword,\n            new_password: newPassword\n          })\n        });\n\n        if (retryResponse.ok) {\n          return;\n        }\n\n        const errorData = await retryResponse.json();\n        throw new Error(errorData.detail || 'Failed to change password');\n      } catch (error) {\n        // If refresh fails, logout the user\n        removeTokens();\n        throw error;\n      }\n    }\n\n    const errorData = await response.json();\n    console.log('Registration error:', errorData);\n\n    // Create a custom error object with the response data\n    const error = new Error('Registration failed') as ApiError;\n    // Add the response data to the error object\n    error.response = { data: errorData };\n    throw error;\n  }\n};\n\n// Delete account\nexport const deleteAccount = async (password: string): Promise<void> => {\n  const tokens = getTokens();\n\n  if (!tokens.access) {\n    throw new Error('No access token found');\n  }\n\n  const response = await fetch(`${BACKEND_URL}${API_URL}/delete-account/`, {\n    method: 'POST',\n    headers: {\n      'Authorization': `Bearer ${tokens.access}`,\n      'Content-Type': 'application/json'\n    },\n    body: JSON.stringify({ password })\n  });\n\n  if (!response.ok) {\n    if (response.status === 401 && tokens.refresh) {\n      // Try to refresh the token\n      try {\n        const newTokens = await refreshToken(tokens.refresh);\n\n        // Retry the request with the new token\n        const retryResponse = await fetch(`${BACKEND_URL}${API_URL}/delete-account/`, {\n          method: 'POST',\n          headers: {\n            'Authorization': `Bearer ${newTokens.access}`,\n            'Content-Type': 'application/json'\n          },\n          body: JSON.stringify({ password })\n        });\n\n        if (retryResponse.ok) {\n          removeTokens();\n          return;\n        }\n\n        const errorData = await retryResponse.json();\n        throw new Error(errorData.detail || 'Failed to delete account');\n      } catch (error) {\n        // If refresh fails, logout the user\n        removeTokens();\n        throw error;\n      }\n    }\n\n    const errorData = await response.json();\n    throw new Error(errorData.detail || 'Failed to delete account');\n  }\n\n  // Remove tokens after successful account deletion\n  removeTokens();\n};\n\n// Request password reset code (Step 1)\nexport const requestPasswordResetCode = async (email: string): Promise<{\n  message?: string;\n  remaining_resets?: number;\n  note?: string;\n}> => {\n  const response = await fetch(`${BACKEND_URL}/api/auth/request-reset-code/`, {\n    method: 'POST',\n    headers: {\n      'Content-Type': 'application/json'\n    },\n    body: JSON.stringify({ email })\n  });\n\n  const responseData = await response.json();\n\n  if (!response.ok) {\n    console.log('Password reset code request error:', responseData);\n\n    // Create a custom error object with the response data\n    const error = new Error('Failed to request password reset code') as ApiError;\n    // Add the response data to the error object\n    error.response = { data: responseData };\n    throw error;\n  }\n\n  return responseData;\n};\n\n// Verify code and reset password (Step 2)\nexport const resetPasswordWithCode = async (email: string, code: string, newPassword: string): Promise<void> => {\n  const response = await fetch(`${BACKEND_URL}/api/auth/verify-reset-code/`, {\n    method: 'POST',\n    headers: {\n      'Content-Type': 'application/json'\n    },\n    body: JSON.stringify({\n      email,\n      code,\n      new_password: newPassword\n    })\n  });\n\n  if (!response.ok) {\n    const errorData = await response.json();\n    console.log('Password reset error:', errorData);\n\n    // Create a custom error object with the response data\n    const error = new Error('Failed to reset password') as ApiError;\n    // Add the response data to the error object\n    error.response = { data: errorData };\n    throw error;\n  }\n};\n\n// Legacy password reset function (keeping for backward compatibility)\nexport const requestPasswordReset = async (email: string): Promise<{\n  message?: string;\n  remaining_resets?: number;\n  note?: string;\n}> => {\n  return requestPasswordResetCode(email);\n};\n\n// Request account activation code\nexport const requestActivationCode = async (email: string): Promise<void> => {\n  const response = await fetch(`${BACKEND_URL}/api/auth/request-activation-code/`, {\n    method: 'POST',\n    headers: {\n      'Content-Type': 'application/json'\n    },\n    body: JSON.stringify({ email })\n  });\n\n  if (!response.ok) {\n    const errorData = await response.json();\n    console.log('Activation code request error:', errorData);\n\n    // Create a custom error object with the response data\n    const error = new Error('Failed to request activation code') as ApiError;\n    // Add the response data to the error object\n    error.response = { data: errorData };\n    throw error;\n  }\n};\n\n// Resend account activation code\nexport const resendActivationCode = async (email: string): Promise<void> => {\n  const response = await fetch(`${BACKEND_URL}/api/auth/resend-activation/`, {\n    method: 'POST',\n    headers: {\n      'Content-Type': 'application/json'\n    },\n    body: JSON.stringify({ email })\n  });\n\n  if (!response.ok) {\n    const errorData = await response.json();\n    console.log('Resend activation code error:', errorData);\n\n    // Create a custom error object with the response data\n    const error = new Error('Failed to resend activation code') as ApiError;\n    // Add the response data to the error object\n    error.response = { data: errorData };\n    throw error;\n  }\n};\n\n// Activate account with code\nexport const activateAccountWithCode = async (email: string, code: string): Promise<{ message: string; user: User; refresh: string; access: string }> => {\n  const response = await fetch(`${BACKEND_URL}/api/auth/activate/`, {\n    method: 'POST',\n    headers: {\n      'Content-Type': 'application/json'\n    },\n    body: JSON.stringify({ email, code })\n  });\n\n  if (!response.ok) {\n    const errorData = await response.json();\n    console.log('Account activation error:', errorData);\n\n    // Create a custom error object with the response data\n    const error = new Error('Failed to activate account') as ApiError;\n    // Add the response data to the error object\n    error.response = { data: errorData };\n    throw error;\n  }\n\n  return await response.json();\n};\n\n// Legacy account activation function (keeping for backward compatibility)\nexport const activateAccount = async (token: string): Promise<{ message: string; user: User; refresh: string; access: string }> => {\n  const response = await fetch(`${BACKEND_URL}/api/auth/activate/`, {\n    method: 'POST',\n    headers: {\n      'Content-Type': 'application/json'\n    },\n    body: JSON.stringify({ token })\n  });\n\n  if (!response.ok) {\n    const errorData = await response.json();\n    console.log('Account activation error:', errorData);\n\n    // Create a custom error object with the response data\n    const error = new Error('Failed to activate account') as ApiError;\n    // Add the response data to the error object\n    error.response = { data: errorData };\n    throw error;\n  }\n\n  return await response.json();\n};\n\n// Check email verification status\nexport interface EmailVerificationStatus {\n  email_verified: boolean;\n  email: string;\n  message: string;\n}\n\nexport const checkEmailVerification = async (): Promise<EmailVerificationStatus> => {\n  const tokens = getTokens();\n\n  if (!tokens.access) {\n    throw new Error('No access token found');\n  }\n\n  const response = await fetch(`${BACKEND_URL}/api/auth/check-email-verification/`, {\n    method: 'GET',\n    headers: {\n      'Authorization': `Bearer ${tokens.access}`\n    }\n  });\n\n  if (!response.ok) {\n    if (response.status === 401 && tokens.refresh) {\n      // Try to refresh the token\n      try {\n        const newTokens = await refreshToken(tokens.refresh);\n\n        // Retry the request with the new token\n        const retryResponse = await fetch(`${BACKEND_URL}/api/auth/check-email-verification/`, {\n          method: 'GET',\n          headers: {\n            'Authorization': `Bearer ${newTokens.access}`\n          }\n        });\n\n        if (retryResponse.ok) {\n          return await retryResponse.json();\n        }\n\n        throw new Error('Failed to check email verification status after token refresh');\n      } catch (error) {\n        // If refresh fails, logout the user\n      console.error(\"Authentication error:\", error);\n\n        removeTokens();\n        throw new Error('Session expired. Please login again.');\n      }\n    }\n\n    throw new Error('Failed to check email verification status');\n  }\n\n  return await response.json();\n};\n\n// Permission-related functions\nexport const getPermissions = async (): Promise<any[]> => {\n  const tokens = getTokens();\n  if (!tokens.access) {\n    throw new Error(\"No access token found\");\n  }\n\n  const response = await fetch(`${BACKEND_URL}${API_URL}/permissions/`, {\n    method: \"GET\",\n    headers: {\n      Authorization: `Bearer ${tokens.access}`,\n    },\n  });\n\n  if (!response.ok) {\n    if (response.status === 401 && tokens.refresh) {\n      try {\n        const newTokens = await refreshToken(tokens.refresh);\n        const retryResponse = await fetch(`${BACKEND_URL}${API_URL}/permissions/`, {\n          method: \"GET\",\n          headers: {\n            Authorization: `Bearer ${newTokens.access}`,\n          },\n        });\n\n        if (retryResponse.ok) {\n          return await retryResponse.json();\n        }\n\n        throw new Error(\"Failed to get permissions after token refresh\");\n      } catch (error) {\n        removeTokens();\n        throw new Error(\"Session expired. Please login again.\");\n      }\n    }\n\n    const errorData = await response.json();\n    throw new Error(errorData.message || \"Failed to get permissions\");\n  }\n\n  return await response.json();\n};\n\nexport const createPermission = async (formData: FormData): Promise<any> => {\n  const tokens = getTokens();\n  if (!tokens.access) {\n    throw new Error(\"No access token found\");\n  }\n\n  const response = await fetch(`${BACKEND_URL}${API_URL}/permissions/`, {\n    method: \"POST\",\n    headers: {\n      Authorization: `Bearer ${tokens.access}`,\n    },\n    body: formData,\n  });\n\n  if (!response.ok) {\n    if (response.status === 401 && tokens.refresh) {\n      try {\n        const newTokens = await refreshToken(tokens.refresh);\n        const retryResponse = await fetch(`${BACKEND_URL}${API_URL}/permissions/`, {\n          method: \"POST\",\n          headers: {\n            Authorization: `Bearer ${newTokens.access}`,\n          },\n          body: formData,\n        });\n\n        if (retryResponse.ok) {\n          return await retryResponse.json();\n        }\n\n        throw new Error(\"Failed to create permission after token refresh\");\n      } catch (error) {\n        removeTokens();\n        throw new Error(\"Session expired. Please login again.\");\n      }\n    }\n\n    const errorData = await response.json();\n    throw new Error(errorData.message || \"Failed to create permission\");\n  }\n\n  return await response.json();\n};\n\n// Logout\nexport const logout = (): void => {\n  removeTokens();\n\n  // Dispatch custom event to notify other components about authentication change\n  const authChangeEvent = new Event('authChange');\n  window.dispatchEvent(authChangeEvent);\n\n  // Dispatch custom event to notify about cart updates\n  const cartUpdateEvent = new Event('cartUpdated');\n  window.dispatchEvent(cartUpdateEvent);\n};\n"], "names": [], "mappings": "AAAA,yBAAyB;AACzB,4DAA4D;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAE5D;;AAwDA,eAAe;AACf,MAAM,UAAU;AAGT,MAAM,YAAY;IACvB,OAAO;QACL,QAAQ,aAAa,OAAO,CAAC;QAC7B,SAAS,aAAa,OAAO,CAAC;IAChC;AACF;AAGO,MAAM,YAAY,CAAC;IACxB,aAAa,OAAO,CAAC,gBAAgB,OAAO,MAAM;IAClD,aAAa,OAAO,CAAC,iBAAiB,OAAO,OAAO;IAEpD,+EAA+E;IAC/E,MAAM,kBAAkB,IAAI,MAAM;IAClC,OAAO,aAAa,CAAC;IAErB,qDAAqD;IACrD,MAAM,kBAAkB,IAAI,MAAM;IAClC,OAAO,aAAa,CAAC;AACvB;AAGO,MAAM,eAAe;IAC1B,aAAa,UAAU,CAAC;IACxB,aAAa,UAAU,CAAC;AAC1B;AAGO,MAAM,eAAe;IAC1B,OAAO,YAAY,MAAM;AAC3B;AAEO,MAAM,eAAe,CAAC;IAC3B,aAAa,OAAO,CAAC,gBAAgB;AACvC;AAEO,MAAM,kBAAkB;IAC7B;AACF;AAGO,MAAM,kBAAkB;IAC7B,MAAM,SAAS;IACf,OAAO,OAAO,MAAM,KAAK,QAAQ,OAAO,MAAM,KAAK;AACrD;AAGO,MAAM,QAAQ,OAAO,OAAe;IACzC,MAAM,WAAW,MAAM,MAAM,GAAG,6GAAA,CAAA,cAAW,GAAG,QAAQ,OAAO,CAAC,EAAE;QAC9D,QAAQ;QACR,SAAS;YACP,gBAAgB;QAClB;QACA,MAAM,KAAK,SAAS,CAAC;YAAE;YAAO;QAAS;IACzC;IAEA,IAAI,CAAC,SAAS,EAAE,EAAE;QAChB,MAAM,YAAY,MAAM,SAAS,IAAI;QACrC,QAAQ,GAAG,CAAC,gBAAgB;QAE5B,sDAAsD;QACtD,MAAM,QAAQ,IAAI,MAAM;QACxB,4CAA4C;QAC5C,MAAM,QAAQ,GAAG;YAAE,MAAM;QAAU;QACnC,MAAM;IACR;IAEA,MAAM,OAAO,MAAM,SAAS,IAAI;IAChC,yDAAyD;IACzD,+DAA+D;IAE/D,OAAO;AACT;AAUO,MAAM,WAAW,OAAO;IAC7B,MAAM,WAAW,MAAM,MAAM,GAAG,6GAAA,CAAA,cAAW,GAAG,QAAQ,UAAU,CAAC,EAAE;QACjE,QAAQ;QACR,MAAM;IACR;IAEA,IAAI,CAAC,SAAS,EAAE,EAAE;QAChB,MAAM,YAAY,MAAM,SAAS,IAAI;QACrC,QAAQ,GAAG,CAAC,uBAAuB;QAEnC,sDAAsD;QACtD,MAAM,QAAQ,IAAI,MAAM;QACxB,4CAA4C;QAC5C,MAAM,QAAQ,GAAG;YAAE,MAAM;QAAU;QACnC,MAAM;IACR;IAEA,OAAO,SAAS,IAAI;AACtB;AAGO,MAAM,cAAc,OAAO;IAChC,IAAI;QACF,MAAM,WAAW,MAAM,MAAM,GAAG,6GAAA,CAAA,cAAW,GAAG,QAAQ,cAAc,CAAC,EAAE;YACrE,QAAQ;YACR,SAAS;gBACP,gBAAgB;YAClB;YACA,MAAM,KAAK,SAAS,CAAC;gBAAE;YAAM;QAC/B;QAEA,OAAO,SAAS,EAAE;IACpB,EAAE,OAAO,OAAO;QACZ,QAAQ,KAAK,CAAC,yBAAyB;QAEzC,OAAO;IACT;AACF;AAGO,MAAM,eAAe,OAAO;IACjC,MAAM,WAAW,MAAM,MAAM,GAAG,6GAAA,CAAA,cAAW,GAAG,QAAQ,eAAe,CAAC,EAAE;QACtE,QAAQ;QACR,SAAS;YACP,gBAAgB;QAClB;QACA,MAAM,KAAK,SAAS,CAAC;YAAE;QAAQ;IACjC;IAEA,IAAI,CAAC,SAAS,EAAE,EAAE;QAChB,MAAM,IAAI,MAAM;IAClB;IAEA,MAAM,OAAO,MAAM,SAAS,IAAI;IAEhC,+CAA+C;IAC/C,aAAa,OAAO,CAAC,gBAAgB,KAAK,MAAM;IAEhD,OAAO;AACT;AAGO,MAAM,iBAAiB;IAC5B,MAAM,SAAS;IAEf,IAAI,CAAC,OAAO,MAAM,EAAE;QAClB,MAAM,IAAI,MAAM;IAClB;IAEA,MAAM,WAAW,MAAM,MAAM,GAAG,6GAAA,CAAA,cAAW,GAAG,QAAQ,gBAAgB,CAAC,EAAE;QACvE,SAAS;YACP,iBAAiB,CAAC,OAAO,EAAE,OAAO,MAAM,EAAE;QAC5C;IACF;IAEA,IAAI,CAAC,SAAS,EAAE,EAAE;QAChB,IAAI,SAAS,MAAM,KAAK,OAAO,OAAO,OAAO,EAAE;YAC7C,2BAA2B;YAC3B,IAAI;gBACF,MAAM,YAAY,MAAM,aAAa,OAAO,OAAO;gBAEnD,uCAAuC;gBACvC,MAAM,gBAAgB,MAAM,MAAM,GAAG,6GAAA,CAAA,cAAW,GAAG,QAAQ,UAAU,CAAC,EAAE;oBACtE,SAAS;wBACP,iBAAiB,CAAC,OAAO,EAAE,UAAU,MAAM,EAAE;oBAC/C;gBACF;gBAEA,IAAI,cAAc,EAAE,EAAE;oBACpB,OAAO,MAAM,cAAc,IAAI;gBACjC;gBAEA,MAAM,IAAI,MAAM;YAClB,EAAE,OAAO,OAAO;gBACd,oCAAoC;gBACpC,QAAQ,KAAK,CAAC,yBAAyB;gBAEvC,MAAM,IAAI,MAAM;YAClB;QACF;QAEA,MAAM,IAAI,MAAM;IAClB;IAEA,OAAO,MAAM,SAAS,IAAI;AAC5B;AAGO,MAAM,cAAc;IACzB,MAAM,SAAS;IAEf,IAAI,CAAC,OAAO,MAAM,EAAE;QAClB,MAAM,IAAI,MAAM;IAClB;IAEA,MAAM,WAAW,MAAM,MAAM,GAAG,6GAAA,CAAA,cAAW,GAAG,QAAQ,gBAAgB,CAAC,EAAE;QACvE,QAAQ;QACR,SAAS;YACP,iBAAiB,CAAC,OAAO,EAAE,OAAO,MAAM,EAAE;QAC5C;IACF;IAEA,IAAI,CAAC,SAAS,EAAE,EAAE;QAChB,IAAI,SAAS,MAAM,KAAK,OAAO,OAAO,OAAO,EAAE;YAC7C,2BAA2B;YAC3B,IAAI;gBACF,MAAM,YAAY,MAAM,aAAa,OAAO,OAAO;gBAEnD,uCAAuC;gBACvC,MAAM,gBAAgB,MAAM,MAAM,GAAG,6GAAA,CAAA,cAAW,GAAG,QAAQ,gBAAgB,CAAC,EAAE;oBAC5E,QAAQ;oBACR,SAAS;wBACP,iBAAiB,CAAC,OAAO,EAAE,UAAU,MAAM,EAAE;oBAC/C;gBACF;gBAEA,IAAI,cAAc,EAAE,EAAE;oBACpB,OAAO,MAAM,cAAc,IAAI;gBACjC;gBAEA,MAAM,IAAI,MAAM;YAClB,EAAE,OAAO,OAAO;gBACd,oCAAoC;gBACpC,QAAQ,KAAK,CAAC,yBAAyB;gBAEvC;gBACA,MAAM,IAAI,MAAM;YAClB;QACF;QAEA,MAAM,IAAI,MAAM;IAClB;IAEA,OAAO,MAAM,SAAS,IAAI;AAC5B;AAGO,MAAM,oBAAoB,OAAO;IACtC,MAAM,SAAS;IAEf,IAAI,CAAC,OAAO,MAAM,EAAE;QAClB,MAAM,IAAI,MAAM;IAClB;IAEA,MAAM,WAAW,MAAM,MAAM,GAAG,6GAAA,CAAA,cAAW,GAAG,QAAQ,aAAa,CAAC,EAAE;QACpE,QAAQ;QACR,SAAS;YACP,iBAAiB,CAAC,OAAO,EAAE,OAAO,MAAM,EAAE;QAC5C;QACA,MAAM;IACR;IAEA,IAAI,CAAC,SAAS,EAAE,EAAE;QAChB,IAAI,SAAS,MAAM,KAAK,OAAO,OAAO,OAAO,EAAE;YAC7C,2BAA2B;YAC3B,IAAI;gBACF,MAAM,YAAY,MAAM,aAAa,OAAO,OAAO;gBAEnD,uCAAuC;gBACvC,MAAM,gBAAgB,MAAM,MAAM,GAAG,6GAAA,CAAA,cAAW,GAAG,QAAQ,aAAa,CAAC,EAAE;oBACzE,QAAQ;oBACR,SAAS;wBACP,iBAAiB,CAAC,OAAO,EAAE,UAAU,MAAM,EAAE;oBAC/C;oBACA,MAAM;gBACR;gBAEA,IAAI,cAAc,EAAE,EAAE;oBACpB,OAAO,MAAM,cAAc,IAAI;gBACjC;gBAEA,MAAM,IAAI,MAAM;YAClB,EAAE,OAAO,OAAO;gBACd,oCAAoC;gBACpC,QAAQ,KAAK,CAAC,yBAAyB;gBACvC;gBACA,MAAM,IAAI,MAAM;YAClB;QACF;QAEA,MAAM,YAAY,MAAM,SAAS,IAAI;QACrC,MAAM,QAAQ,IAAI,MAAM;QACxB,MAAM,QAAQ,GAAG;YAAE,MAAM;QAAU;QACnC,MAAM;IAER;IAEA,OAAO,MAAM,SAAS,IAAI;AAC5B;AAGO,MAAM,iBAAiB,OAAO,iBAAyB;IAC5D,MAAM,SAAS;IAEf,IAAI,CAAC,OAAO,MAAM,EAAE;QAClB,MAAM,IAAI,MAAM;IAClB;IAEA,MAAM,WAAW,MAAM,MAAM,GAAG,6GAAA,CAAA,cAAW,GAAG,QAAQ,uBAAuB,CAAC,EAAE;QAC9E,QAAQ;QACR,SAAS;YACP,iBAAiB,CAAC,OAAO,EAAE,OAAO,MAAM,EAAE;YAC1C,gBAAgB;QAClB;QACA,MAAM,KAAK,SAAS,CAAC;YACnB,kBAAkB;YAClB,cAAc;QAChB;IACF;IAEA,IAAI,CAAC,SAAS,EAAE,EAAE;QAChB,IAAI,SAAS,MAAM,KAAK,OAAO,OAAO,OAAO,EAAE;YAC7C,2BAA2B;YAC3B,IAAI;gBACF,MAAM,YAAY,MAAM,aAAa,OAAO,OAAO;gBAEnD,uCAAuC;gBACvC,MAAM,gBAAgB,MAAM,MAAM,GAAG,6GAAA,CAAA,cAAW,GAAG,QAAQ,uBAAuB,CAAC,EAAE;oBACnF,QAAQ;oBACR,SAAS;wBACP,iBAAiB,CAAC,OAAO,EAAE,UAAU,MAAM,EAAE;wBAC7C,gBAAgB;oBAClB;oBACA,MAAM,KAAK,SAAS,CAAC;wBACnB,kBAAkB;wBAClB,cAAc;oBAChB;gBACF;gBAEA,IAAI,cAAc,EAAE,EAAE;oBACpB;gBACF;gBAEA,MAAM,YAAY,MAAM,cAAc,IAAI;gBAC1C,MAAM,IAAI,MAAM,UAAU,MAAM,IAAI;YACtC,EAAE,OAAO,OAAO;gBACd,oCAAoC;gBACpC;gBACA,MAAM;YACR;QACF;QAEA,MAAM,YAAY,MAAM,SAAS,IAAI;QACrC,QAAQ,GAAG,CAAC,uBAAuB;QAEnC,sDAAsD;QACtD,MAAM,QAAQ,IAAI,MAAM;QACxB,4CAA4C;QAC5C,MAAM,QAAQ,GAAG;YAAE,MAAM;QAAU;QACnC,MAAM;IACR;AACF;AAGO,MAAM,gBAAgB,OAAO;IAClC,MAAM,SAAS;IAEf,IAAI,CAAC,OAAO,MAAM,EAAE;QAClB,MAAM,IAAI,MAAM;IAClB;IAEA,MAAM,WAAW,MAAM,MAAM,GAAG,6GAAA,CAAA,cAAW,GAAG,QAAQ,gBAAgB,CAAC,EAAE;QACvE,QAAQ;QACR,SAAS;YACP,iBAAiB,CAAC,OAAO,EAAE,OAAO,MAAM,EAAE;YAC1C,gBAAgB;QAClB;QACA,MAAM,KAAK,SAAS,CAAC;YAAE;QAAS;IAClC;IAEA,IAAI,CAAC,SAAS,EAAE,EAAE;QAChB,IAAI,SAAS,MAAM,KAAK,OAAO,OAAO,OAAO,EAAE;YAC7C,2BAA2B;YAC3B,IAAI;gBACF,MAAM,YAAY,MAAM,aAAa,OAAO,OAAO;gBAEnD,uCAAuC;gBACvC,MAAM,gBAAgB,MAAM,MAAM,GAAG,6GAAA,CAAA,cAAW,GAAG,QAAQ,gBAAgB,CAAC,EAAE;oBAC5E,QAAQ;oBACR,SAAS;wBACP,iBAAiB,CAAC,OAAO,EAAE,UAAU,MAAM,EAAE;wBAC7C,gBAAgB;oBAClB;oBACA,MAAM,KAAK,SAAS,CAAC;wBAAE;oBAAS;gBAClC;gBAEA,IAAI,cAAc,EAAE,EAAE;oBACpB;oBACA;gBACF;gBAEA,MAAM,YAAY,MAAM,cAAc,IAAI;gBAC1C,MAAM,IAAI,MAAM,UAAU,MAAM,IAAI;YACtC,EAAE,OAAO,OAAO;gBACd,oCAAoC;gBACpC;gBACA,MAAM;YACR;QACF;QAEA,MAAM,YAAY,MAAM,SAAS,IAAI;QACrC,MAAM,IAAI,MAAM,UAAU,MAAM,IAAI;IACtC;IAEA,kDAAkD;IAClD;AACF;AAGO,MAAM,2BAA2B,OAAO;IAK7C,MAAM,WAAW,MAAM,MAAM,GAAG,6GAAA,CAAA,cAAW,CAAC,6BAA6B,CAAC,EAAE;QAC1E,QAAQ;QACR,SAAS;YACP,gBAAgB;QAClB;QACA,MAAM,KAAK,SAAS,CAAC;YAAE;QAAM;IAC/B;IAEA,MAAM,eAAe,MAAM,SAAS,IAAI;IAExC,IAAI,CAAC,SAAS,EAAE,EAAE;QAChB,QAAQ,GAAG,CAAC,sCAAsC;QAElD,sDAAsD;QACtD,MAAM,QAAQ,IAAI,MAAM;QACxB,4CAA4C;QAC5C,MAAM,QAAQ,GAAG;YAAE,MAAM;QAAa;QACtC,MAAM;IACR;IAEA,OAAO;AACT;AAGO,MAAM,wBAAwB,OAAO,OAAe,MAAc;IACvE,MAAM,WAAW,MAAM,MAAM,GAAG,6GAAA,CAAA,cAAW,CAAC,4BAA4B,CAAC,EAAE;QACzE,QAAQ;QACR,SAAS;YACP,gBAAgB;QAClB;QACA,MAAM,KAAK,SAAS,CAAC;YACnB;YACA;YACA,cAAc;QAChB;IACF;IAEA,IAAI,CAAC,SAAS,EAAE,EAAE;QAChB,MAAM,YAAY,MAAM,SAAS,IAAI;QACrC,QAAQ,GAAG,CAAC,yBAAyB;QAErC,sDAAsD;QACtD,MAAM,QAAQ,IAAI,MAAM;QACxB,4CAA4C;QAC5C,MAAM,QAAQ,GAAG;YAAE,MAAM;QAAU;QACnC,MAAM;IACR;AACF;AAGO,MAAM,uBAAuB,OAAO;IAKzC,OAAO,yBAAyB;AAClC;AAGO,MAAM,wBAAwB,OAAO;IAC1C,MAAM,WAAW,MAAM,MAAM,GAAG,6GAAA,CAAA,cAAW,CAAC,kCAAkC,CAAC,EAAE;QAC/E,QAAQ;QACR,SAAS;YACP,gBAAgB;QAClB;QACA,MAAM,KAAK,SAAS,CAAC;YAAE;QAAM;IAC/B;IAEA,IAAI,CAAC,SAAS,EAAE,EAAE;QAChB,MAAM,YAAY,MAAM,SAAS,IAAI;QACrC,QAAQ,GAAG,CAAC,kCAAkC;QAE9C,sDAAsD;QACtD,MAAM,QAAQ,IAAI,MAAM;QACxB,4CAA4C;QAC5C,MAAM,QAAQ,GAAG;YAAE,MAAM;QAAU;QACnC,MAAM;IACR;AACF;AAGO,MAAM,uBAAuB,OAAO;IACzC,MAAM,WAAW,MAAM,MAAM,GAAG,6GAAA,CAAA,cAAW,CAAC,4BAA4B,CAAC,EAAE;QACzE,QAAQ;QACR,SAAS;YACP,gBAAgB;QAClB;QACA,MAAM,KAAK,SAAS,CAAC;YAAE;QAAM;IAC/B;IAEA,IAAI,CAAC,SAAS,EAAE,EAAE;QAChB,MAAM,YAAY,MAAM,SAAS,IAAI;QACrC,QAAQ,GAAG,CAAC,iCAAiC;QAE7C,sDAAsD;QACtD,MAAM,QAAQ,IAAI,MAAM;QACxB,4CAA4C;QAC5C,MAAM,QAAQ,GAAG;YAAE,MAAM;QAAU;QACnC,MAAM;IACR;AACF;AAGO,MAAM,0BAA0B,OAAO,OAAe;IAC3D,MAAM,WAAW,MAAM,MAAM,GAAG,6GAAA,CAAA,cAAW,CAAC,mBAAmB,CAAC,EAAE;QAChE,QAAQ;QACR,SAAS;YACP,gBAAgB;QAClB;QACA,MAAM,KAAK,SAAS,CAAC;YAAE;YAAO;QAAK;IACrC;IAEA,IAAI,CAAC,SAAS,EAAE,EAAE;QAChB,MAAM,YAAY,MAAM,SAAS,IAAI;QACrC,QAAQ,GAAG,CAAC,6BAA6B;QAEzC,sDAAsD;QACtD,MAAM,QAAQ,IAAI,MAAM;QACxB,4CAA4C;QAC5C,MAAM,QAAQ,GAAG;YAAE,MAAM;QAAU;QACnC,MAAM;IACR;IAEA,OAAO,MAAM,SAAS,IAAI;AAC5B;AAGO,MAAM,kBAAkB,OAAO;IACpC,MAAM,WAAW,MAAM,MAAM,GAAG,6GAAA,CAAA,cAAW,CAAC,mBAAmB,CAAC,EAAE;QAChE,QAAQ;QACR,SAAS;YACP,gBAAgB;QAClB;QACA,MAAM,KAAK,SAAS,CAAC;YAAE;QAAM;IAC/B;IAEA,IAAI,CAAC,SAAS,EAAE,EAAE;QAChB,MAAM,YAAY,MAAM,SAAS,IAAI;QACrC,QAAQ,GAAG,CAAC,6BAA6B;QAEzC,sDAAsD;QACtD,MAAM,QAAQ,IAAI,MAAM;QACxB,4CAA4C;QAC5C,MAAM,QAAQ,GAAG;YAAE,MAAM;QAAU;QACnC,MAAM;IACR;IAEA,OAAO,MAAM,SAAS,IAAI;AAC5B;AASO,MAAM,yBAAyB;IACpC,MAAM,SAAS;IAEf,IAAI,CAAC,OAAO,MAAM,EAAE;QAClB,MAAM,IAAI,MAAM;IAClB;IAEA,MAAM,WAAW,MAAM,MAAM,GAAG,6GAAA,CAAA,cAAW,CAAC,mCAAmC,CAAC,EAAE;QAChF,QAAQ;QACR,SAAS;YACP,iBAAiB,CAAC,OAAO,EAAE,OAAO,MAAM,EAAE;QAC5C;IACF;IAEA,IAAI,CAAC,SAAS,EAAE,EAAE;QAChB,IAAI,SAAS,MAAM,KAAK,OAAO,OAAO,OAAO,EAAE;YAC7C,2BAA2B;YAC3B,IAAI;gBACF,MAAM,YAAY,MAAM,aAAa,OAAO,OAAO;gBAEnD,uCAAuC;gBACvC,MAAM,gBAAgB,MAAM,MAAM,GAAG,6GAAA,CAAA,cAAW,CAAC,mCAAmC,CAAC,EAAE;oBACrF,QAAQ;oBACR,SAAS;wBACP,iBAAiB,CAAC,OAAO,EAAE,UAAU,MAAM,EAAE;oBAC/C;gBACF;gBAEA,IAAI,cAAc,EAAE,EAAE;oBACpB,OAAO,MAAM,cAAc,IAAI;gBACjC;gBAEA,MAAM,IAAI,MAAM;YAClB,EAAE,OAAO,OAAO;gBACd,oCAAoC;gBACtC,QAAQ,KAAK,CAAC,yBAAyB;gBAErC;gBACA,MAAM,IAAI,MAAM;YAClB;QACF;QAEA,MAAM,IAAI,MAAM;IAClB;IAEA,OAAO,MAAM,SAAS,IAAI;AAC5B;AAGO,MAAM,iBAAiB;IAC5B,MAAM,SAAS;IACf,IAAI,CAAC,OAAO,MAAM,EAAE;QAClB,MAAM,IAAI,MAAM;IAClB;IAEA,MAAM,WAAW,MAAM,MAAM,GAAG,6GAAA,CAAA,cAAW,GAAG,QAAQ,aAAa,CAAC,EAAE;QACpE,QAAQ;QACR,SAAS;YACP,eAAe,CAAC,OAAO,EAAE,OAAO,MAAM,EAAE;QAC1C;IACF;IAEA,IAAI,CAAC,SAAS,EAAE,EAAE;QAChB,IAAI,SAAS,MAAM,KAAK,OAAO,OAAO,OAAO,EAAE;YAC7C,IAAI;gBACF,MAAM,YAAY,MAAM,aAAa,OAAO,OAAO;gBACnD,MAAM,gBAAgB,MAAM,MAAM,GAAG,6GAAA,CAAA,cAAW,GAAG,QAAQ,aAAa,CAAC,EAAE;oBACzE,QAAQ;oBACR,SAAS;wBACP,eAAe,CAAC,OAAO,EAAE,UAAU,MAAM,EAAE;oBAC7C;gBACF;gBAEA,IAAI,cAAc,EAAE,EAAE;oBACpB,OAAO,MAAM,cAAc,IAAI;gBACjC;gBAEA,MAAM,IAAI,MAAM;YAClB,EAAE,OAAO,OAAO;gBACd;gBACA,MAAM,IAAI,MAAM;YAClB;QACF;QAEA,MAAM,YAAY,MAAM,SAAS,IAAI;QACrC,MAAM,IAAI,MAAM,UAAU,OAAO,IAAI;IACvC;IAEA,OAAO,MAAM,SAAS,IAAI;AAC5B;AAEO,MAAM,mBAAmB,OAAO;IACrC,MAAM,SAAS;IACf,IAAI,CAAC,OAAO,MAAM,EAAE;QAClB,MAAM,IAAI,MAAM;IAClB;IAEA,MAAM,WAAW,MAAM,MAAM,GAAG,6GAAA,CAAA,cAAW,GAAG,QAAQ,aAAa,CAAC,EAAE;QACpE,QAAQ;QACR,SAAS;YACP,eAAe,CAAC,OAAO,EAAE,OAAO,MAAM,EAAE;QAC1C;QACA,MAAM;IACR;IAEA,IAAI,CAAC,SAAS,EAAE,EAAE;QAChB,IAAI,SAAS,MAAM,KAAK,OAAO,OAAO,OAAO,EAAE;YAC7C,IAAI;gBACF,MAAM,YAAY,MAAM,aAAa,OAAO,OAAO;gBACnD,MAAM,gBAAgB,MAAM,MAAM,GAAG,6GAAA,CAAA,cAAW,GAAG,QAAQ,aAAa,CAAC,EAAE;oBACzE,QAAQ;oBACR,SAAS;wBACP,eAAe,CAAC,OAAO,EAAE,UAAU,MAAM,EAAE;oBAC7C;oBACA,MAAM;gBACR;gBAEA,IAAI,cAAc,EAAE,EAAE;oBACpB,OAAO,MAAM,cAAc,IAAI;gBACjC;gBAEA,MAAM,IAAI,MAAM;YAClB,EAAE,OAAO,OAAO;gBACd;gBACA,MAAM,IAAI,MAAM;YAClB;QACF;QAEA,MAAM,YAAY,MAAM,SAAS,IAAI;QACrC,MAAM,IAAI,MAAM,UAAU,OAAO,IAAI;IACvC;IAEA,OAAO,MAAM,SAAS,IAAI;AAC5B;AAGO,MAAM,SAAS;IACpB;IAEA,+EAA+E;IAC/E,MAAM,kBAAkB,IAAI,MAAM;IAClC,OAAO,aAAa,CAAC;IAErB,qDAAqD;IACrD,MAAM,kBAAkB,IAAI,MAAM;IAClC,OAAO,aAAa,CAAC;AACvB", "debugId": null}}, {"offset": {"line": 1969, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/IMPORTANT/work/export/front/src/utils/navigationUtils.ts"], "sourcesContent": ["/**\n * Navigation utilities for handling redirects and storing temporary data\n */\n\n/**\n * Redirects the user to the email verification page and stores their email\n * @param email The user's email address\n * @param message Optional message to display on the verification page\n */\nexport const redirectToEmailVerification = (email: string, message?: string) => {\n  // Store the email in localStorage so it can be accessed on the verification page\n  if (email) {\n    localStorage.setItem('pendingActivationEmail', email);\n  }\n\n  // Store the message if provided\n  if (message) {\n    localStorage.setItem('emailVerificationMessage', message);\n  }\n\n  // Navigate to the activation page\n  window.location.href = '/activate-account';\n};\n\n/**\n * Triggers an email verification error popup\n * @param error The error object containing email verification data\n */\nexport const triggerEmailVerificationPopup = (error: any) => {\n  // Dispatch a custom event that the EmailVerificationHandler will listen for\n  const event = new CustomEvent('emailVerificationError', {\n    detail: error\n  });\n  window.dispatchEvent(event);\n};\n\n/**\n * Checks if the current API error is related to email verification\n * @param error The API error object\n * @returns True if the error is related to email verification\n */\nexport const isEmailVerificationError = (error: any): boolean => {\n  if (!error ) {\n    return false;\n  }\n\n  const data = error;\n\n  return (\n    data.email_not_verified !== undefined ||\n    (data.email_verified !== undefined && data.email_verified === \"False\") ||\n    (data.code === \"email_not_verified\") ||\n    (data.detail && data.detail.includes(\"email\") && data.detail.includes(\"verified\"))\n  );\n};\n\n/**\n * Extracts the email and message from an email verification error\n * @param error The API error object\n * @returns An object containing the email and message\n */\nexport const extractEmailVerificationData = (error: any): { email?: string; message?: string } => {\n  if (!error ) {\n    return {};\n  }\n\n  const data = error;\n\n  return {\n    email: data.email || '',\n    message: data.message || data.detail || data.email_not_verified || 'Your email address has not been verified. Please verify your email to continue.'\n  };\n};\n"], "names": [], "mappings": "AAAA;;CAEC,GAED;;;;CAIC;;;;;;AACM,MAAM,8BAA8B,CAAC,OAAe;IACzD,iFAAiF;IACjF,IAAI,OAAO;QACT,aAAa,OAAO,CAAC,0BAA0B;IACjD;IAEA,gCAAgC;IAChC,IAAI,SAAS;QACX,aAAa,OAAO,CAAC,4BAA4B;IACnD;IAEA,kCAAkC;IAClC,OAAO,QAAQ,CAAC,IAAI,GAAG;AACzB;AAMO,MAAM,gCAAgC,CAAC;IAC5C,4EAA4E;IAC5E,MAAM,QAAQ,IAAI,YAAY,0BAA0B;QACtD,QAAQ;IACV;IACA,OAAO,aAAa,CAAC;AACvB;AAOO,MAAM,2BAA2B,CAAC;IACvC,IAAI,CAAC,OAAQ;QACX,OAAO;IACT;IAEA,MAAM,OAAO;IAEb,OACE,KAAK,kBAAkB,KAAK,aAC3B,KAAK,cAAc,KAAK,aAAa,KAAK,cAAc,KAAK,WAC7D,KAAK,IAAI,KAAK,wBACd,KAAK,MAAM,IAAI,KAAK,MAAM,CAAC,QAAQ,CAAC,YAAY,KAAK,MAAM,CAAC,QAAQ,CAAC;AAE1E;AAOO,MAAM,+BAA+B,CAAC;IAC3C,IAAI,CAAC,OAAQ;QACX,OAAO,CAAC;IACV;IAEA,MAAM,OAAO;IAEb,OAAO;QACL,OAAO,KAAK,KAAK,IAAI;QACrB,SAAS,KAAK,OAAO,IAAI,KAAK,MAAM,IAAI,KAAK,kBAAkB,IAAI;IACrE;AACF", "debugId": null}}, {"offset": {"line": 2023, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/IMPORTANT/work/export/front/src/app/account/components/ProfileForm.tsx"], "sourcesContent": ["\"use client\";\n\nimport React, { useState, useEffect } from \"react\";\nimport { toast } from \"react-toastify\";\nimport { Loader2, X, Image as ImageIcon } from \"lucide-react\";\nimport Image from \"next/image\";\nimport Input from \"@/components/common/Input\";\nimport PhoneInput from \"@/components/PhoneInput\";\nimport RelationSection from \"@/components/RelationSection\";\nimport { ProfileFormData, FormErrors, User } from \"@/types/account\";\nimport { ChildData } from \"@/types/auth\";\nimport { validateProfileForm } from \"@/utils/accountUtils\";\nimport { countryCodes } from \"@/utils/countryData\";\nimport { useDropzone } from \"react-dropzone\";\nimport { updateUserProfile } from \"@/services/authService\";\nimport {\n  isEmailVerificationError,\n  triggerEmailVerificationPopup,\n  redirectToEmailVerification,\n} from \"@/utils/navigationUtils\";\n\n// Extended User interface to handle both phone and phone_number fields\ninterface ExtendedUser extends Omit<User, \"children\"> {\n  phone: string;\n  phone_number?: string;\n  children: ChildData[];\n  governorate?: string;\n  city?: string;\n  other_relation?: string;\n}\n\ninterface ProfileFormProps {\n  initialFormData: ProfileFormData;\n  user: ExtendedUser | null;\n  onUserUpdate: (user: ExtendedUser) => void;\n}\n\nconst ProfileForm: React.FC<ProfileFormProps> = ({\n  initialFormData,\n  user,\n  onUserUpdate,\n}) => {\n  const [localFormData, setLocalFormData] =\n    useState<ProfileFormData>(initialFormData);\n  const [errors, setErrors] = useState<FormErrors>({});\n  const [previewUrl, setPreviewUrl] = useState<string>(\"\");\n  const [isImageModified, setIsImageModified] = useState(false);\n  const [updateLoading, setUpdateLoading] = useState(false);\n\n  // تحديث بيانات النموذج المحلية عند تغيير الخصائص (props)\n\n  // دوال إدارة الأطفال\n  const addChild = () => {\n    // إنشاء طفل جديد\n    const newChild = {\n      id: Date.now().toString(),\n      name: \"\",\n      class: \"\",\n      stage: \"\",\n    };\n\n    // تحديث بيانات النموذج بإضافة الطفل الجديد مع الحفاظ على باقي الحقول\n    setLocalFormData((prevFormData) => ({\n      ...prevFormData,\n      children: [...prevFormData.children, newChild],\n    }));\n\n    console.log(\"تم إضافة طفل جديد:\", newChild);\n  };\n\n  const removeChild = (childId: string) => {\n    // تحديث بيانات النموذج بحذف الطفل المحدد مع الحفاظ على باقي الحقول\n    setLocalFormData((prevFormData) => ({\n      ...prevFormData,\n      children: prevFormData.children.filter((child) => child.id !== childId),\n    }));\n\n    console.log(\"تم حذف الطفل بالمعرف:\", childId);\n  };\n\n  const updateChild = (\n    id: string,\n    field: \"name\" | \"class\" | \"stage\",\n    value: string\n  ) => {\n    // تحديث بيانات النموذج باستخدام الصيغة الدالية لضمان الحصول على أحدث حالة\n    setLocalFormData((prevFormData) => ({\n      ...prevFormData,\n      children: prevFormData.children.map((child) =>\n        child.id === id ? { ...child, [field]: value } : child\n      ),\n    }));\n\n    console.log(`تم تحديث الطفل ${id}, الحقل ${field} إلى ${value}`);\n  };\n\n  const { getRootProps, getInputProps } = useDropzone({\n    onDrop: (acceptedFiles) => {\n      const file = acceptedFiles[0];\n      setLocalFormData({ ...localFormData, idImage: file });\n      setIsImageModified(true);\n\n      // إنشاء رابط معاينة للصورة\n      const url = URL.createObjectURL(file);\n      setPreviewUrl(url);\n    },\n    accept: {\n      \"image/*\": [\".jpeg\", \".jpg\", \".png\"],\n    },\n  });\n\n  // ضبط رابط المعاينة من صورة الهوية الموجودة\n  useEffect(() => {\n    if (initialFormData.idImage) {\n      if (typeof initialFormData.idImage === \"string\") {\n        setPreviewUrl(initialFormData.idImage);\n      } else if (initialFormData.idImage instanceof File) {\n        const url = URL.createObjectURL(initialFormData.idImage);\n        setPreviewUrl(url);\n      }\n    } else {\n      setPreviewUrl(\"\");\n    }\n  }, [initialFormData.idImage]);\n\n  // تنظيف رابط المعاينة عند إلغاء تحميل المكون\n  useEffect(() => {\n    return () => {\n      if (previewUrl && !previewUrl.startsWith(\"http\")) {\n        URL.revokeObjectURL(previewUrl);\n      }\n    };\n  }, [previewUrl]);\n\n  const handlePhoneChange = (value: string) => {\n    console.log(\"تم تغيير رقم الهاتف:\", value);\n    setLocalFormData((prev) => {\n      const updated = { ...prev, phone: value };\n      console.log(\"تم تحديث بيانات النموذج (الهاتف):\", updated);\n      return updated;\n    });\n  };\n\n  const handlePhoneCountryChange = (code: string) => {\n    console.log(\"تم تغيير رمز الدولة للهاتف:\", code);\n    setLocalFormData((prev) => {\n      const updated = { ...prev, phoneCountry: code };\n      console.log(\"تم تحديث بيانات النموذج (رمز الدولة):\", updated);\n      return updated;\n    });\n  };\n\n  // التحقق من صحة بيانات الأطفال\n  const validateChildren = (\n    children: { id: string; name: string; class: string; stage: string }[]\n  ): { valid: boolean; errors: FormErrors } => {\n    const childErrors: FormErrors = {};\n    let valid = true;\n\n    children.forEach((child) => {\n      if (!child.name || child.name.trim() === \"\") {\n        childErrors[`child_${child.id}_name`] = \"اسم الطفل مطلوب\";\n        valid = false;\n      }\n\n      if (!child.class || child.class.trim() === \"\") {\n        childErrors[`child_${child.id}_class`] = \"الفصل مطلوب\";\n        valid = false;\n      }\n\n      if (!child.stage || child.stage.trim() === \"\") {\n        childErrors[`child_${child.id}_stage`] = \"المرحلة مطلوبة\";\n        valid = false;\n      }\n    });\n\n    return { valid, errors: childErrors };\n  };\n\n  const handleSubmit = async (e: React.FormEvent) => {\n    e.preventDefault();\n    if (localFormData == initialFormData) {\n      toast.error(\"يرجى تغيير أي حقل\");\n      return;\n    }\n    // التحقق من صحة بيانات النموذج\n    const formErrors = validateProfileForm(localFormData, true);\n\n    // التحقق من صحة بيانات الأطفال\n    const { errors: childrenErrors } = validateChildren(localFormData.children);\n\n    // دمج الأخطاء\n    const newErrors = { ...formErrors, ...childrenErrors };\n    setErrors(newErrors);\n\n    if (Object.keys(newErrors).length > 0) {\n      toast.error(\"يرجى تصحيح الأخطاء في النموذج\");\n      return;\n    }\n\n    setUpdateLoading(true);\n\n    // التحقق مما إذا كان البريد الإلكتروني قد تغير\n    const newEmail = localFormData.email;\n    const emailChanged = user && newEmail && newEmail !== user.email;\n\n    try {\n      // إنشاء نسخة جديدة من FormData\n      const formDataToSubmit = new FormData();\n\n      // إضافة الحقول الأساسية\n      formDataToSubmit.append(\"first_name\", localFormData.firstName);\n      formDataToSubmit.append(\"last_name\", localFormData.lastName);\n      formDataToSubmit.append(\"email\", localFormData.email);\n\n      // تنسيق رقم الهاتف مع رمز الدولة\n      const selectedCountry = countryCodes.find(\n        (c) => c.code === localFormData.phoneCountry\n      );\n      const dialCode = selectedCountry?.dialCode || \"\";\n      const phoneNumber = localFormData.phone || \"\";\n      const formattedPhone = phoneNumber\n        ? `${dialCode}${\n            phoneNumber.startsWith(\"0\") ? phoneNumber.substring(1) : phoneNumber\n          }`\n        : \"\";\n\n      // تسجيل رقم الهاتف للتصحيح\n      console.log(\"تنسيق رقم الهاتف:\", {\n        countryCode: localFormData.phoneCountry,\n        dialCode,\n        phoneNumber,\n        formattedPhone,\n      });\n\n      // استخدام كلا الحقلين phone و phone_number لضمان التوافق\n      formDataToSubmit.append(\"phone_number\", formattedPhone);\n      formDataToSubmit.append(\"phone\", formattedPhone);\n\n      // إضافة حقل العلاقة - إذا تم اختيار \"أخرى\"، استخدم قيمة otherRelation\n      if (localFormData.relation === \"other\" && localFormData.otherRelation) {\n        formDataToSubmit.append(\"relation\", localFormData.otherRelation);\n      } else {\n        formDataToSubmit.append(\"relation\", localFormData.relation);\n      }\n      formDataToSubmit.append(\"governorate\", localFormData.governorate || \"\");\n      formDataToSubmit.append(\"city\", localFormData.city || \"\");\n\n      // معالجة صورة الهوية\n      if (isImageModified) {\n        if (localFormData.idImage instanceof File) {\n          formDataToSubmit.append(\"id_image\", localFormData.idImage);\n        } else if (localFormData.idImage === undefined) {\n          // إذا تم إزالة الصورة ولكن نحتاج صورة\n          newErrors.idImage = \"صورة الهوية مطلوبة\";\n          setErrors(newErrors);\n          toast.error(\"صورة الهوية مطلوبة\");\n          setUpdateLoading(false);\n          return;\n        }\n      } else if (\n        typeof localFormData.idImage === \"string\" &&\n        localFormData.idImage\n      ) {\n        // إذا كانت هناك صورة موجودة كرابط URL، لا داعي لفعل شيء\n        console.log(\"استخدام الصورة الموجودة:\", localFormData.idImage);\n      } else if (!localFormData.idImage) {\n        // لا توجد صورة على الإطلاق\n        newErrors.idImage = \"صورة الهوية مطلوبة\";\n        setErrors(newErrors);\n        toast.error(\"صورة الهوية مطلوبة\");\n        setUpdateLoading(false);\n        return;\n      }\n\n      // إضافة بيانات الأطفال - تأكد من إرسال الحالة الحالية\n      formDataToSubmit.append(\n        \"children\",\n        JSON.stringify(localFormData.children)\n      );\n\n      // استدعاء دالة تحديث بيانات المستخدم\n      const response = await updateUserProfile(formDataToSubmit);\n\n      if (!user) {\n        setUpdateLoading(false);\n        return;\n      }\n\n      const updatedUser = {\n        ...user,\n        first_name: localFormData.firstName || user.first_name,\n        last_name: localFormData.lastName || user.last_name,\n        email: localFormData.email || user.email,\n        phone: formattedPhone || user.phone,\n        governorate: localFormData.governorate || \"\",\n        city: localFormData.city || \"\",\n        relation:\n          localFormData.relation === \"other\" && localFormData.otherRelation\n            ? localFormData.otherRelation\n            : localFormData.relation || user.relation,\n        other_relation: localFormData.otherRelation || \"\",\n        children: localFormData.children,\n      };\n\n      onUserUpdate(updatedUser);\n      setUpdateLoading(false);\n\n      // التحقق إذا كان البريد الإلكتروني تغير ويحتاج إلى تفعيل\n      if (\n        emailChanged &&\n        response &&\n        \"email_verified\" in response &&\n        !response.email_verified\n      ) {\n        // تم تغيير البريد الإلكتروني ويحتاج تفعيل - التوجيه لصفحة التفعيل\n        setTimeout(() => {\n          redirectToEmailVerification(\n            newEmail,\n            \"تم تحديث بريدك الإلكتروني ويجب التحقق منه. يرجى التحقق من صندوق الوارد الخاص بك لرمز التفعيل.\"\n          );\n        }, 1000);\n        return;\n      }\n\n      // عرض رسالة نجاح للتحديثات العادية\n      toast.success(\"تم تحديث الملف الشخصي بنجاح\", {\n        autoClose: 1500, // الإغلاق بعد 1.5 ثانية\n        hideProgressBar: true, // إزالة شريط التوقيت\n        closeOnClick: true,\n        pauseOnHover: false,\n        draggable: true,\n      });\n\n      // إعادة تحميل الصفحة بعد 1.5 ثانية للتحديثات العادية الناجحة\n      setTimeout(() => {\n        window.location.reload();\n      }, 1500);\n    } catch (error: unknown) {\n      setUpdateLoading(false);\n      console.error(\"حدث خطأ أثناء تحديث الملف الشخصي:\", error);\n\n      // التحقق إذا كان الخطأ متعلق بتفعيل البريد الإلكتروني\n      if (isEmailVerificationError(error)) {\n        triggerEmailVerificationPopup(error);\n        return;\n      }\n\n      toast.error(\"فشل تحديث الملف الشخصي\");\n    }\n  };\n\n  return (\n    <div className=\"bg-white rounded-2xl shadow-xl border border-gray-100 overflow-hidden\">\n      {/* Header */}\n      <div className=\"bg-pink-500 px-6 py-4\">\n        <h3 className=\"text-xl font-bold text-white\">معلومات الملف الشخصي</h3>\n        <p className=\"text-sm text-pink-100 mt-1\">تحديث بياناتك الشخصية</p>\n      </div>\n\n      <form\n        id=\"profileForm\"\n        onSubmit={handleSubmit}\n        className=\"p-6 space-y-8\"\n        noValidate\n      >\n        {/* قسم المعلومات الأساسية */}\n        <div className=\"space-y-4\">\n          <div className=\"border-b border-gray-200 pb-4\">\n            <h4 className=\"text-lg font-semibold text-gray-900 mb-4\">\n              المعلومات الأساسية\n            </h4>\n\n            <div className=\"space-y-4\">\n              {/* حقول الاسم */}\n              <div className=\"grid grid-cols-1 md:grid-cols-2 gap-4\">\n                <Input\n                  id=\"firstName\"\n                  label=\"الاسم الأول\"\n                  name=\"firstName\"\n                  value={localFormData.firstName}\n                  onChange={(e) =>\n                    setLocalFormData({\n                      ...localFormData,\n                      firstName: e.target.value,\n                    })\n                  }\n                  error={!!errors.firstName}\n                  errorMessage={errors.firstName}\n                  required\n                />\n                <Input\n                  id=\"lastName\"\n                  label=\"اسم العائلة\"\n                  name=\"lastName\"\n                  value={localFormData.lastName}\n                  onChange={(e) =>\n                    setLocalFormData({\n                      ...localFormData,\n                      lastName: e.target.value,\n                    })\n                  }\n                  error={!!errors.lastName}\n                  errorMessage={errors.lastName}\n                  required\n                />\n              </div>\n\n              {/* البريد الإلكتروني */}\n              <Input\n                id=\"email\"\n                label=\"البريد الإلكتروني\"\n                type=\"email\"\n                name=\"email\"\n                value={localFormData.email}\n                onChange={(e) =>\n                  setLocalFormData({ ...localFormData, email: e.target.value })\n                }\n                error={!!errors.email}\n                errorMessage={errors.email}\n                required\n              />\n\n              {/* رقم الهاتف */}\n              <div className=\"space-y-2\">\n                <label className=\"block text-sm font-medium text-gray-700\">\n                  رقم الهاتف\n                  <span className=\"text-red-500\">*</span>\n                </label>\n                <PhoneInput\n                  value={localFormData.phone || \"\"}\n                  countryCode={localFormData.phoneCountry || \"EG\"}\n                  onChange={handlePhoneChange}\n                  onCountryChange={handlePhoneCountryChange}\n                  error={!!errors.phone}\n                  errorMessage={errors.phone}\n                  countryCodes={countryCodes}\n                />\n                <p className=\"text-xs text-gray-500\">\n                  التنسيق الحالي: {localFormData.phoneCountry} (\n                  {countryCodes.find(\n                    (c) => c.code === localFormData.phoneCountry\n                  )?.dialCode || \"\"}\n                  ) {localFormData.phone}\n                </p>\n              </div>\n            </div>\n          </div>\n        </div>\n\n        {/* قسم تحميل صورة الهوية */}\n        <div className=\"space-y-4\">\n          <div className=\"border-b border-gray-200 pb-4\">\n            <h4 className=\"text-lg font-semibold text-gray-900 mb-4\">\n              التحقق من الهوية\n            </h4>\n\n            <div className=\"space-y-2\">\n              <label className=\"block text-sm font-medium text-gray-700\">\n                صورة الهوية\n                <span className=\"text-red-500\">*</span>\n              </label>\n              <div\n                {...getRootProps()}\n                className={`relative border-2 border-dashed ${\n                  errors.idImage ? \"border-red-500\" : \"border-gray-300\"\n                } p-6 rounded-xl text-center cursor-pointer hover:border-slate-400 focus:outline-none focus:border-slate-600 transition-all duration-200 bg-gradient-to-br from-gray-50 to-slate-50`}\n              >\n                <input {...getInputProps()} />\n                {previewUrl ? (\n                  <div className=\"relative inline-block\">\n                    <Image\n                      src={previewUrl}\n                      alt=\"ID Preview\"\n                      width={160}\n                      height={160}\n                      className=\"max-h-40 rounded-lg shadow-lg object-contain\"\n                    />\n                    <button\n                      type=\"button\"\n                      onClick={(e) => {\n                        e.stopPropagation();\n                        setLocalFormData({\n                          ...localFormData,\n                          idImage: undefined,\n                        });\n                        setPreviewUrl(\"\");\n                        setIsImageModified(true);\n                      }}\n                      className=\"absolute -top-2 -right-2 bg-red-500 text-white rounded-full p-1.5 hover:bg-red-600 transition-colors duration-200 shadow-lg\"\n                      aria-label=\"حذف الصورة\"\n                    >\n                      <X className=\"h-4 w-4\" />\n                    </button>\n                  </div>\n                ) : (\n                  <div className=\"flex flex-col items-center space-y-3\">\n                    <ImageIcon className=\"h-12 w-12 text-gray-400\" />\n                    <div>\n                      <p className=\"text-gray-600 font-medium\">\n                        قم بسحب وإفلات صورة الهوية هنا\n                      </p>\n                      <p className=\"text-sm text-gray-500 mt-1\">\n                        أو اضغط لاختيار صورة من جهازك\n                      </p>\n                    </div>\n                  </div>\n                )}\n              </div>\n              {errors.idImage && (\n                <p className=\"text-sm text-red-500 mt-1\">{errors.idImage}</p>\n              )}\n            </div>\n          </div>\n        </div>\n\n        {/* قسم صلة القرابة */}\n        <div className=\"space-y-4\">\n          <div className=\"border-b border-gray-200 pb-4\">\n            <h4 className=\"text-lg font-semibold text-gray-900 mb-4\">\n              صلة القرابة\n            </h4>\n            <RelationSection\n              relation={\n                localFormData.relation === \"other\" &&\n                localFormData.otherRelation\n                  ? localFormData.otherRelation\n                  : localFormData.relation\n              }\n              errors={errors}\n              onRelationChange={(relation) => {\n                if ([\"father\", \"mother\", \"other\"].includes(relation)) {\n                  setLocalFormData({\n                    ...localFormData,\n                    relation: relation,\n                    otherRelation:\n                      relation === \"other\" ? localFormData.otherRelation : \"\",\n                  });\n                } else {\n                  setLocalFormData({\n                    ...localFormData,\n                    relation: \"other\",\n                    otherRelation: relation,\n                  });\n                }\n              }}\n            />\n          </div>\n        </div>\n\n        {/* قسم بيانات الأطفال */}\n        <div className=\"space-y-4\">\n          <div className=\"border border-gray-200 rounded-xl overflow-hidden\">\n            <div className=\"bg-gradient-to-r from-slate-50 to-blue-50 px-6 py-4\">\n              <div className=\"flex justify-between items-center\">\n                <div>\n                  <h4 className=\"text-lg font-semibold text-gray-900\">\n                    بيانات الأطفال\n                  </h4>\n                  <p className=\"text-sm text-gray-600\">\n                    أضف معلومات حول أطفالك\n                  </p>\n                </div>\n                <button\n                  type=\"button\"\n                  onClick={addChild}\n                  className=\"px-4 py-2 text-sm font-semibold text-white bg-pink-500 rounded-lg hover:bg-slate-800 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-slate-500 transition-all duration-200 shadow-md\"\n                >\n                  إضافة طفل\n                </button>\n              </div>\n            </div>\n\n            <div className=\"p-6\">\n              {localFormData.children.length === 0 && (\n                <div className=\"text-center py-8\">\n                  <p className=\"text-gray-500 text-sm\">\n                    لم يتم إضافة أطفال بعد. اضغط على \"إضافة طفل\" لإضافة معلومات.\n                  </p>\n                </div>\n              )}\n\n              {localFormData.children.map((child, index) => (\n                <div\n                  key={child.id}\n                  className=\"p-4 border border-gray-200 rounded-xl bg-gradient-to-br from-white to-gray-50 space-y-4 mb-4 last:mb-0\"\n                >\n                  <div className=\"flex justify-between items-center\">\n                    <h5 className=\"font-semibold text-gray-900\">\n                      الطفل {index + 1}\n                    </h5>\n                    <button\n                      type=\"button\"\n                      onClick={() => removeChild(child.id)}\n                      className=\"text-red-500 hover:text-red-700 font-medium text-sm transition-colors duration-200\"\n                    >\n                      حذف\n                    </button>\n                  </div>\n\n                  <div className=\"grid grid-cols-1 md:grid-cols-3 gap-4\">\n                    <Input\n                      id={`child-name-${child.id}`}\n                      label=\"الاسم\"\n                      value={child.name}\n                      onChange={(e) =>\n                        updateChild(child.id, \"name\", e.target.value)\n                      }\n                      error={!!errors[`child_${child.id}_name`]}\n                      errorMessage={errors[`child_${child.id}_name`]}\n                      required\n                    />\n                    <Input\n                      id={`child-class-${child.id}`}\n                      label=\"الصف\"\n                      value={child.class}\n                      onChange={(e) =>\n                        updateChild(child.id, \"class\", e.target.value)\n                      }\n                      error={!!errors[`child_${child.id}_class`]}\n                      errorMessage={errors[`child_${child.id}_class`]}\n                      required\n                    />\n                    <Input\n                      id={`child-stage-${child.id}`}\n                      label=\"المرحلة\"\n                      value={child.stage}\n                      onChange={(e) =>\n                        updateChild(child.id, \"stage\", e.target.value)\n                      }\n                      error={!!errors[`child_${child.id}_stage`]}\n                      errorMessage={errors[`child_${child.id}_stage`]}\n                      required\n                    />\n                  </div>\n                </div>\n              ))}\n\n              {errors.children && (\n                <div className=\"bg-red-50 border border-red-200 rounded-lg p-3 mt-4\">\n                  <p className=\"text-sm text-red-700\">{errors.children}</p>\n                </div>\n              )}\n            </div>\n          </div>\n        </div>\n\n        {/* زر الحفظ */}\n        <div className=\"pt-6\">\n          <button\n            type=\"submit\"\n            disabled={updateLoading}\n            className=\"w-full flex justify-center items-center py-3 px-6 border border-transparent text-base font-semibold rounded-xl text-white bg-gradient-to-r bg-pink-500 hover:to-slate-950 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-slate-500 disabled:opacity-50 disabled:cursor-not-allowed transition-all duration-200 shadow-lg hover:shadow-xl\"\n          >\n            {updateLoading ? (\n              <>\n                <Loader2 className=\"w-5 h-5 animate-spin mr-2\" />\n                جاري تحديث الملف الشخصي...\n              </>\n            ) : (\n              \"تحديث الملف الشخصي\"\n            )}\n          </button>\n        </div>\n      </form>\n    </div>\n  );\n};\n\nexport default ProfileForm;\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAAA;AAAA;AACA;AACA;AACA;AACA;AAGA;AACA;AACA;AAAA;AACA;AACA;AAfA;;;;;;;;;;;;;;AAqCA,MAAM,cAA0C,CAAC,EAC/C,eAAe,EACf,IAAI,EACJ,YAAY,EACb;IACC,MAAM,CAAC,eAAe,iBAAiB,GACrC,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAmB;IAC5B,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAc,CAAC;IAClD,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAU;IACrD,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACvD,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAEnD,yDAAyD;IAEzD,qBAAqB;IACrB,MAAM,WAAW;QACf,iBAAiB;QACjB,MAAM,WAAW;YACf,IAAI,KAAK,GAAG,GAAG,QAAQ;YACvB,MAAM;YACN,OAAO;YACP,OAAO;QACT;QAEA,qEAAqE;QACrE,iBAAiB,CAAC,eAAiB,CAAC;gBAClC,GAAG,YAAY;gBACf,UAAU;uBAAI,aAAa,QAAQ;oBAAE;iBAAS;YAChD,CAAC;QAED,QAAQ,GAAG,CAAC,sBAAsB;IACpC;IAEA,MAAM,cAAc,CAAC;QACnB,mEAAmE;QACnE,iBAAiB,CAAC,eAAiB,CAAC;gBAClC,GAAG,YAAY;gBACf,UAAU,aAAa,QAAQ,CAAC,MAAM,CAAC,CAAC,QAAU,MAAM,EAAE,KAAK;YACjE,CAAC;QAED,QAAQ,GAAG,CAAC,yBAAyB;IACvC;IAEA,MAAM,cAAc,CAClB,IACA,OACA;QAEA,0EAA0E;QAC1E,iBAAiB,CAAC,eAAiB,CAAC;gBAClC,GAAG,YAAY;gBACf,UAAU,aAAa,QAAQ,CAAC,GAAG,CAAC,CAAC,QACnC,MAAM,EAAE,KAAK,KAAK;wBAAE,GAAG,KAAK;wBAAE,CAAC,MAAM,EAAE;oBAAM,IAAI;YAErD,CAAC;QAED,QAAQ,GAAG,CAAC,CAAC,eAAe,EAAE,GAAG,QAAQ,EAAE,MAAM,KAAK,EAAE,OAAO;IACjE;IAEA,MAAM,EAAE,YAAY,EAAE,aAAa,EAAE,GAAG,CAAA,GAAA,wKAAA,CAAA,cAAW,AAAD,EAAE;QAClD,QAAQ,CAAC;YACP,MAAM,OAAO,aAAa,CAAC,EAAE;YAC7B,iBAAiB;gBAAE,GAAG,aAAa;gBAAE,SAAS;YAAK;YACnD,mBAAmB;YAEnB,2BAA2B;YAC3B,MAAM,MAAM,IAAI,eAAe,CAAC;YAChC,cAAc;QAChB;QACA,QAAQ;YACN,WAAW;gBAAC;gBAAS;gBAAQ;aAAO;QACtC;IACF;IAEA,4CAA4C;IAC5C,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,gBAAgB,OAAO,EAAE;YAC3B,IAAI,OAAO,gBAAgB,OAAO,KAAK,UAAU;gBAC/C,cAAc,gBAAgB,OAAO;YACvC,OAAO,IAAI,gBAAgB,OAAO,YAAY,MAAM;gBAClD,MAAM,MAAM,IAAI,eAAe,CAAC,gBAAgB,OAAO;gBACvD,cAAc;YAChB;QACF,OAAO;YACL,cAAc;QAChB;IACF,GAAG;QAAC,gBAAgB,OAAO;KAAC;IAE5B,6CAA6C;IAC7C,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,OAAO;YACL,IAAI,cAAc,CAAC,WAAW,UAAU,CAAC,SAAS;gBAChD,IAAI,eAAe,CAAC;YACtB;QACF;IACF,GAAG;QAAC;KAAW;IAEf,MAAM,oBAAoB,CAAC;QACzB,QAAQ,GAAG,CAAC,wBAAwB;QACpC,iBAAiB,CAAC;YAChB,MAAM,UAAU;gBAAE,GAAG,IAAI;gBAAE,OAAO;YAAM;YACxC,QAAQ,GAAG,CAAC,qCAAqC;YACjD,OAAO;QACT;IACF;IAEA,MAAM,2BAA2B,CAAC;QAChC,QAAQ,GAAG,CAAC,+BAA+B;QAC3C,iBAAiB,CAAC;YAChB,MAAM,UAAU;gBAAE,GAAG,IAAI;gBAAE,cAAc;YAAK;YAC9C,QAAQ,GAAG,CAAC,yCAAyC;YACrD,OAAO;QACT;IACF;IAEA,+BAA+B;IAC/B,MAAM,mBAAmB,CACvB;QAEA,MAAM,cAA0B,CAAC;QACjC,IAAI,QAAQ;QAEZ,SAAS,OAAO,CAAC,CAAC;YAChB,IAAI,CAAC,MAAM,IAAI,IAAI,MAAM,IAAI,CAAC,IAAI,OAAO,IAAI;gBAC3C,WAAW,CAAC,CAAC,MAAM,EAAE,MAAM,EAAE,CAAC,KAAK,CAAC,CAAC,GAAG;gBACxC,QAAQ;YACV;YAEA,IAAI,CAAC,MAAM,KAAK,IAAI,MAAM,KAAK,CAAC,IAAI,OAAO,IAAI;gBAC7C,WAAW,CAAC,CAAC,MAAM,EAAE,MAAM,EAAE,CAAC,MAAM,CAAC,CAAC,GAAG;gBACzC,QAAQ;YACV;YAEA,IAAI,CAAC,MAAM,KAAK,IAAI,MAAM,KAAK,CAAC,IAAI,OAAO,IAAI;gBAC7C,WAAW,CAAC,CAAC,MAAM,EAAE,MAAM,EAAE,CAAC,MAAM,CAAC,CAAC,GAAG;gBACzC,QAAQ;YACV;QACF;QAEA,OAAO;YAAE;YAAO,QAAQ;QAAY;IACtC;IAEA,MAAM,eAAe,OAAO;QAC1B,EAAE,cAAc;QAChB,IAAI,iBAAiB,iBAAiB;YACpC,mJAAA,CAAA,QAAK,CAAC,KAAK,CAAC;YACZ;QACF;QACA,+BAA+B;QAC/B,MAAM,aAAa,CAAA,GAAA,4HAAA,CAAA,sBAAmB,AAAD,EAAE,eAAe;QAEtD,+BAA+B;QAC/B,MAAM,EAAE,QAAQ,cAAc,EAAE,GAAG,iBAAiB,cAAc,QAAQ;QAE1E,cAAc;QACd,MAAM,YAAY;YAAE,GAAG,UAAU;YAAE,GAAG,cAAc;QAAC;QACrD,UAAU;QAEV,IAAI,OAAO,IAAI,CAAC,WAAW,MAAM,GAAG,GAAG;YACrC,mJAAA,CAAA,QAAK,CAAC,KAAK,CAAC;YACZ;QACF;QAEA,iBAAiB;QAEjB,+CAA+C;QAC/C,MAAM,WAAW,cAAc,KAAK;QACpC,MAAM,eAAe,QAAQ,YAAY,aAAa,KAAK,KAAK;QAEhE,IAAI;YACF,+BAA+B;YAC/B,MAAM,mBAAmB,IAAI;YAE7B,wBAAwB;YACxB,iBAAiB,MAAM,CAAC,cAAc,cAAc,SAAS;YAC7D,iBAAiB,MAAM,CAAC,aAAa,cAAc,QAAQ;YAC3D,iBAAiB,MAAM,CAAC,SAAS,cAAc,KAAK;YAEpD,iCAAiC;YACjC,MAAM,kBAAkB,2HAAA,CAAA,eAAY,CAAC,IAAI,CACvC,CAAC,IAAM,EAAE,IAAI,KAAK,cAAc,YAAY;YAE9C,MAAM,WAAW,iBAAiB,YAAY;YAC9C,MAAM,cAAc,cAAc,KAAK,IAAI;YAC3C,MAAM,iBAAiB,cACnB,GAAG,WACD,YAAY,UAAU,CAAC,OAAO,YAAY,SAAS,CAAC,KAAK,aACzD,GACF;YAEJ,2BAA2B;YAC3B,QAAQ,GAAG,CAAC,qBAAqB;gBAC/B,aAAa,cAAc,YAAY;gBACvC;gBACA;gBACA;YACF;YAEA,yDAAyD;YACzD,iBAAiB,MAAM,CAAC,gBAAgB;YACxC,iBAAiB,MAAM,CAAC,SAAS;YAEjC,sEAAsE;YACtE,IAAI,cAAc,QAAQ,KAAK,WAAW,cAAc,aAAa,EAAE;gBACrE,iBAAiB,MAAM,CAAC,YAAY,cAAc,aAAa;YACjE,OAAO;gBACL,iBAAiB,MAAM,CAAC,YAAY,cAAc,QAAQ;YAC5D;YACA,iBAAiB,MAAM,CAAC,eAAe,cAAc,WAAW,IAAI;YACpE,iBAAiB,MAAM,CAAC,QAAQ,cAAc,IAAI,IAAI;YAEtD,qBAAqB;YACrB,IAAI,iBAAiB;gBACnB,IAAI,cAAc,OAAO,YAAY,MAAM;oBACzC,iBAAiB,MAAM,CAAC,YAAY,cAAc,OAAO;gBAC3D,OAAO,IAAI,cAAc,OAAO,KAAK,WAAW;oBAC9C,sCAAsC;oBACtC,UAAU,OAAO,GAAG;oBACpB,UAAU;oBACV,mJAAA,CAAA,QAAK,CAAC,KAAK,CAAC;oBACZ,iBAAiB;oBACjB;gBACF;YACF,OAAO,IACL,OAAO,cAAc,OAAO,KAAK,YACjC,cAAc,OAAO,EACrB;gBACA,wDAAwD;gBACxD,QAAQ,GAAG,CAAC,4BAA4B,cAAc,OAAO;YAC/D,OAAO,IAAI,CAAC,cAAc,OAAO,EAAE;gBACjC,2BAA2B;gBAC3B,UAAU,OAAO,GAAG;gBACpB,UAAU;gBACV,mJAAA,CAAA,QAAK,CAAC,KAAK,CAAC;gBACZ,iBAAiB;gBACjB;YACF;YAEA,sDAAsD;YACtD,iBAAiB,MAAM,CACrB,YACA,KAAK,SAAS,CAAC,cAAc,QAAQ;YAGvC,qCAAqC;YACrC,MAAM,WAAW,MAAM,CAAA,GAAA,8HAAA,CAAA,oBAAiB,AAAD,EAAE;YAEzC,IAAI,CAAC,MAAM;gBACT,iBAAiB;gBACjB;YACF;YAEA,MAAM,cAAc;gBAClB,GAAG,IAAI;gBACP,YAAY,cAAc,SAAS,IAAI,KAAK,UAAU;gBACtD,WAAW,cAAc,QAAQ,IAAI,KAAK,SAAS;gBACnD,OAAO,cAAc,KAAK,IAAI,KAAK,KAAK;gBACxC,OAAO,kBAAkB,KAAK,KAAK;gBACnC,aAAa,cAAc,WAAW,IAAI;gBAC1C,MAAM,cAAc,IAAI,IAAI;gBAC5B,UACE,cAAc,QAAQ,KAAK,WAAW,cAAc,aAAa,GAC7D,cAAc,aAAa,GAC3B,cAAc,QAAQ,IAAI,KAAK,QAAQ;gBAC7C,gBAAgB,cAAc,aAAa,IAAI;gBAC/C,UAAU,cAAc,QAAQ;YAClC;YAEA,aAAa;YACb,iBAAiB;YAEjB,yDAAyD;YACzD,IACE,gBACA,YACA,oBAAoB,YACpB,CAAC,SAAS,cAAc,EACxB;gBACA,kEAAkE;gBAClE,WAAW;oBACT,CAAA,GAAA,+HAAA,CAAA,8BAA2B,AAAD,EACxB,UACA;gBAEJ,GAAG;gBACH;YACF;YAEA,mCAAmC;YACnC,mJAAA,CAAA,QAAK,CAAC,OAAO,CAAC,+BAA+B;gBAC3C,WAAW;gBACX,iBAAiB;gBACjB,cAAc;gBACd,cAAc;gBACd,WAAW;YACb;YAEA,6DAA6D;YAC7D,WAAW;gBACT,OAAO,QAAQ,CAAC,MAAM;YACxB,GAAG;QACL,EAAE,OAAO,OAAgB;YACvB,iBAAiB;YACjB,QAAQ,KAAK,CAAC,qCAAqC;YAEnD,sDAAsD;YACtD,IAAI,CAAA,GAAA,+HAAA,CAAA,2BAAwB,AAAD,EAAE,QAAQ;gBACnC,CAAA,GAAA,+HAAA,CAAA,gCAA6B,AAAD,EAAE;gBAC9B;YACF;YAEA,mJAAA,CAAA,QAAK,CAAC,KAAK,CAAC;QACd;IACF;IAEA,qBACE,8OAAC;QAAI,WAAU;;0BAEb,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAG,WAAU;kCAA+B;;;;;;kCAC7C,8OAAC;wBAAE,WAAU;kCAA6B;;;;;;;;;;;;0BAG5C,8OAAC;gBACC,IAAG;gBACH,UAAU;gBACV,WAAU;gBACV,UAAU;;kCAGV,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAG,WAAU;8CAA2C;;;;;;8CAIzD,8OAAC;oCAAI,WAAU;;sDAEb,8OAAC;4CAAI,WAAU;;8DACb,8OAAC,qIAAA,CAAA,UAAK;oDACJ,IAAG;oDACH,OAAM;oDACN,MAAK;oDACL,OAAO,cAAc,SAAS;oDAC9B,UAAU,CAAC,IACT,iBAAiB;4DACf,GAAG,aAAa;4DAChB,WAAW,EAAE,MAAM,CAAC,KAAK;wDAC3B;oDAEF,OAAO,CAAC,CAAC,OAAO,SAAS;oDACzB,cAAc,OAAO,SAAS;oDAC9B,QAAQ;;;;;;8DAEV,8OAAC,qIAAA,CAAA,UAAK;oDACJ,IAAG;oDACH,OAAM;oDACN,MAAK;oDACL,OAAO,cAAc,QAAQ;oDAC7B,UAAU,CAAC,IACT,iBAAiB;4DACf,GAAG,aAAa;4DAChB,UAAU,EAAE,MAAM,CAAC,KAAK;wDAC1B;oDAEF,OAAO,CAAC,CAAC,OAAO,QAAQ;oDACxB,cAAc,OAAO,QAAQ;oDAC7B,QAAQ;;;;;;;;;;;;sDAKZ,8OAAC,qIAAA,CAAA,UAAK;4CACJ,IAAG;4CACH,OAAM;4CACN,MAAK;4CACL,MAAK;4CACL,OAAO,cAAc,KAAK;4CAC1B,UAAU,CAAC,IACT,iBAAiB;oDAAE,GAAG,aAAa;oDAAE,OAAO,EAAE,MAAM,CAAC,KAAK;gDAAC;4CAE7D,OAAO,CAAC,CAAC,OAAO,KAAK;4CACrB,cAAc,OAAO,KAAK;4CAC1B,QAAQ;;;;;;sDAIV,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAM,WAAU;;wDAA0C;sEAEzD,8OAAC;4DAAK,WAAU;sEAAe;;;;;;;;;;;;8DAEjC,8OAAC,gIAAA,CAAA,UAAU;oDACT,OAAO,cAAc,KAAK,IAAI;oDAC9B,aAAa,cAAc,YAAY,IAAI;oDAC3C,UAAU;oDACV,iBAAiB;oDACjB,OAAO,CAAC,CAAC,OAAO,KAAK;oDACrB,cAAc,OAAO,KAAK;oDAC1B,cAAc,2HAAA,CAAA,eAAY;;;;;;8DAE5B,8OAAC;oDAAE,WAAU;;wDAAwB;wDAClB,cAAc,YAAY;wDAAC;wDAC3C,2HAAA,CAAA,eAAY,CAAC,IAAI,CAChB,CAAC,IAAM,EAAE,IAAI,KAAK,cAAc,YAAY,GAC3C,YAAY;wDAAG;wDACf,cAAc,KAAK;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;kCAQhC,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAG,WAAU;8CAA2C;;;;;;8CAIzD,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAM,WAAU;;gDAA0C;8DAEzD,8OAAC;oDAAK,WAAU;8DAAe;;;;;;;;;;;;sDAEjC,8OAAC;4CACE,GAAG,cAAc;4CAClB,WAAW,CAAC,gCAAgC,EAC1C,OAAO,OAAO,GAAG,mBAAmB,kBACrC,kLAAkL,CAAC;;8DAEpL,8OAAC;oDAAO,GAAG,eAAe;;;;;;gDACzB,2BACC,8OAAC;oDAAI,WAAU;;sEACb,8OAAC,6HAAA,CAAA,UAAK;4DACJ,KAAK;4DACL,KAAI;4DACJ,OAAO;4DACP,QAAQ;4DACR,WAAU;;;;;;sEAEZ,8OAAC;4DACC,MAAK;4DACL,SAAS,CAAC;gEACR,EAAE,eAAe;gEACjB,iBAAiB;oEACf,GAAG,aAAa;oEAChB,SAAS;gEACX;gEACA,cAAc;gEACd,mBAAmB;4DACrB;4DACA,WAAU;4DACV,cAAW;sEAEX,cAAA,8OAAC,4LAAA,CAAA,IAAC;gEAAC,WAAU;;;;;;;;;;;;;;;;yEAIjB,8OAAC;oDAAI,WAAU;;sEACb,8OAAC,oMAAA,CAAA,QAAS;4DAAC,WAAU;;;;;;sEACrB,8OAAC;;8EACC,8OAAC;oEAAE,WAAU;8EAA4B;;;;;;8EAGzC,8OAAC;oEAAE,WAAU;8EAA6B;;;;;;;;;;;;;;;;;;;;;;;;wCAOjD,OAAO,OAAO,kBACb,8OAAC;4CAAE,WAAU;sDAA6B,OAAO,OAAO;;;;;;;;;;;;;;;;;;;;;;;kCAOhE,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAG,WAAU;8CAA2C;;;;;;8CAGzD,8OAAC,qIAAA,CAAA,UAAe;oCACd,UACE,cAAc,QAAQ,KAAK,WAC3B,cAAc,aAAa,GACvB,cAAc,aAAa,GAC3B,cAAc,QAAQ;oCAE5B,QAAQ;oCACR,kBAAkB,CAAC;wCACjB,IAAI;4CAAC;4CAAU;4CAAU;yCAAQ,CAAC,QAAQ,CAAC,WAAW;4CACpD,iBAAiB;gDACf,GAAG,aAAa;gDAChB,UAAU;gDACV,eACE,aAAa,UAAU,cAAc,aAAa,GAAG;4CACzD;wCACF,OAAO;4CACL,iBAAiB;gDACf,GAAG,aAAa;gDAChB,UAAU;gDACV,eAAe;4CACjB;wCACF;oCACF;;;;;;;;;;;;;;;;;kCAMN,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAU;8CACb,cAAA,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;;kEACC,8OAAC;wDAAG,WAAU;kEAAsC;;;;;;kEAGpD,8OAAC;wDAAE,WAAU;kEAAwB;;;;;;;;;;;;0DAIvC,8OAAC;gDACC,MAAK;gDACL,SAAS;gDACT,WAAU;0DACX;;;;;;;;;;;;;;;;;8CAML,8OAAC;oCAAI,WAAU;;wCACZ,cAAc,QAAQ,CAAC,MAAM,KAAK,mBACjC,8OAAC;4CAAI,WAAU;sDACb,cAAA,8OAAC;gDAAE,WAAU;0DAAwB;;;;;;;;;;;wCAMxC,cAAc,QAAQ,CAAC,GAAG,CAAC,CAAC,OAAO,sBAClC,8OAAC;gDAEC,WAAU;;kEAEV,8OAAC;wDAAI,WAAU;;0EACb,8OAAC;gEAAG,WAAU;;oEAA8B;oEACnC,QAAQ;;;;;;;0EAEjB,8OAAC;gEACC,MAAK;gEACL,SAAS,IAAM,YAAY,MAAM,EAAE;gEACnC,WAAU;0EACX;;;;;;;;;;;;kEAKH,8OAAC;wDAAI,WAAU;;0EACb,8OAAC,qIAAA,CAAA,UAAK;gEACJ,IAAI,CAAC,WAAW,EAAE,MAAM,EAAE,EAAE;gEAC5B,OAAM;gEACN,OAAO,MAAM,IAAI;gEACjB,UAAU,CAAC,IACT,YAAY,MAAM,EAAE,EAAE,QAAQ,EAAE,MAAM,CAAC,KAAK;gEAE9C,OAAO,CAAC,CAAC,MAAM,CAAC,CAAC,MAAM,EAAE,MAAM,EAAE,CAAC,KAAK,CAAC,CAAC;gEACzC,cAAc,MAAM,CAAC,CAAC,MAAM,EAAE,MAAM,EAAE,CAAC,KAAK,CAAC,CAAC;gEAC9C,QAAQ;;;;;;0EAEV,8OAAC,qIAAA,CAAA,UAAK;gEACJ,IAAI,CAAC,YAAY,EAAE,MAAM,EAAE,EAAE;gEAC7B,OAAM;gEACN,OAAO,MAAM,KAAK;gEAClB,UAAU,CAAC,IACT,YAAY,MAAM,EAAE,EAAE,SAAS,EAAE,MAAM,CAAC,KAAK;gEAE/C,OAAO,CAAC,CAAC,MAAM,CAAC,CAAC,MAAM,EAAE,MAAM,EAAE,CAAC,MAAM,CAAC,CAAC;gEAC1C,cAAc,MAAM,CAAC,CAAC,MAAM,EAAE,MAAM,EAAE,CAAC,MAAM,CAAC,CAAC;gEAC/C,QAAQ;;;;;;0EAEV,8OAAC,qIAAA,CAAA,UAAK;gEACJ,IAAI,CAAC,YAAY,EAAE,MAAM,EAAE,EAAE;gEAC7B,OAAM;gEACN,OAAO,MAAM,KAAK;gEAClB,UAAU,CAAC,IACT,YAAY,MAAM,EAAE,EAAE,SAAS,EAAE,MAAM,CAAC,KAAK;gEAE/C,OAAO,CAAC,CAAC,MAAM,CAAC,CAAC,MAAM,EAAE,MAAM,EAAE,CAAC,MAAM,CAAC,CAAC;gEAC1C,cAAc,MAAM,CAAC,CAAC,MAAM,EAAE,MAAM,EAAE,CAAC,MAAM,CAAC,CAAC;gEAC/C,QAAQ;;;;;;;;;;;;;+CAhDP,MAAM,EAAE;;;;;wCAsDhB,OAAO,QAAQ,kBACd,8OAAC;4CAAI,WAAU;sDACb,cAAA,8OAAC;gDAAE,WAAU;0DAAwB,OAAO,QAAQ;;;;;;;;;;;;;;;;;;;;;;;;;;;;kCAQ9D,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BACC,MAAK;4BACL,UAAU;4BACV,WAAU;sCAET,8BACC;;kDACE,8OAAC,iNAAA,CAAA,UAAO;wCAAC,WAAU;;;;;;oCAA8B;;+CAInD;;;;;;;;;;;;;;;;;;;;;;;AAOd;uCAEe", "debugId": null}}, {"offset": {"line": 2955, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/IMPORTANT/work/export/front/src/components/common/PasswordInput.tsx"], "sourcesContent": ["import React, { useState, InputHTMLAttributes } from \"react\";\nimport { <PERSON>, Eye, EyeOff, AlertCircle } from \"lucide-react\";\n\ninterface PasswordInputProps extends InputHTMLAttributes<HTMLInputElement> {\n  id: string;\n  label?: string;\n  error?: boolean;\n  errorMessage?: string;\n  value: string;\n  onChange: (e: React.ChangeEvent<HTMLInputElement>) => void;\n}\n\nconst PasswordInput: React.FC<PasswordInputProps> = ({\n  id,\n  label,\n  error,\n  value,\n  onChange,\n  className = \"\",\n  ...props\n}) => {\n  const [showPassword, setShowPassword] = useState(false);\n\n  const togglePasswordVisibility = () => {\n    setShowPassword(!showPassword);\n  };\n\n  return (\n    <div className=\"w-full\">\n      {label && (\n        <label\n          htmlFor={id}\n          className=\"block text-sm font-medium text-gray-700 mb-1\"\n        >\n          {label}\n        </label>\n      )}\n      <div className=\"relative\">\n        <input\n          id={id}\n          type={showPassword ? \"text\" : \"password\"}\n          className={`appearance-none rounded-lg relative block w-full px-3 py-3 pl-10 pr-10 border ${\n            error ? \"border-red-500\" : \"border-gray-300\"\n          } placeholder-gray-500 text-gray-900 focus:outline-none focus:ring-2 focus:ring-black focus:border-transparent ${className}`}\n          value={value}\n          onChange={onChange}\n          {...props}\n        />\n        <div className=\"absolute inset-y-0 right-0 pr-3 flex items-center pointer-events-none\">\n          <Lock className=\"h-5 w-5 text-gray-400\" />\n        </div>\n        <button\n          type=\"button\"\n          className=\"absolute inset-y-0 left-0 pl-3 flex items-center text-gray-400 hover:text-gray-600 focus:outline-none\"\n          onClick={togglePasswordVisibility}\n        >\n          {showPassword ? (\n            <EyeOff className=\"h-5 w-5\" />\n          ) : (\n            <Eye className=\"h-5 w-5\" />\n          )}\n        </button>\n        {error && (\n          <div className=\"absolute left-10 top-1/2 transform -translate-y-1/2 text-red-500\">\n            <AlertCircle className=\"h-5 w-5\" />\n          </div>\n        )}\n      </div>\n    </div>\n  );\n};\n\nexport default PasswordInput;\n"], "names": [], "mappings": ";;;;AAAA;AACA;AAAA;AAAA;AAAA;;;;AAWA,MAAM,gBAA8C,CAAC,EACnD,EAAE,EACF,KAAK,EACL,KAAK,EACL,KAAK,EACL,QAAQ,EACR,YAAY,EAAE,EACd,GAAG,OACJ;IACC,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAEjD,MAAM,2BAA2B;QAC/B,gBAAgB,CAAC;IACnB;IAEA,qBACE,8OAAC;QAAI,WAAU;;YACZ,uBACC,8OAAC;gBACC,SAAS;gBACT,WAAU;0BAET;;;;;;0BAGL,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBACC,IAAI;wBACJ,MAAM,eAAe,SAAS;wBAC9B,WAAW,CAAC,8EAA8E,EACxF,QAAQ,mBAAmB,kBAC5B,8GAA8G,EAAE,WAAW;wBAC5H,OAAO;wBACP,UAAU;wBACT,GAAG,KAAK;;;;;;kCAEX,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC,kMAAA,CAAA,OAAI;4BAAC,WAAU;;;;;;;;;;;kCAElB,8OAAC;wBACC,MAAK;wBACL,WAAU;wBACV,SAAS;kCAER,6BACC,8OAAC,0MAAA,CAAA,SAAM;4BAAC,WAAU;;;;;iDAElB,8OAAC,gMAAA,CAAA,MAAG;4BAAC,WAAU;;;;;;;;;;;oBAGlB,uBACC,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC,oNAAA,CAAA,cAAW;4BAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;AAMnC;uCAEe", "debugId": null}}, {"offset": {"line": 3069, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/IMPORTANT/work/export/front/src/components/PasswordStrengthIndicator.tsx"], "sourcesContent": ["import React from \"react\";\nimport { PasswordStrength } from \"../utils/formUtils\";\nimport { Check, X } from \"lucide-react\";\n\ninterface PasswordStrengthIndicatorProps {\n  strength: PasswordStrength;\n  password?: string;\n}\n\nconst PasswordStrengthIndicator: React.FC<PasswordStrengthIndicatorProps> = ({\n  strength,\n  password = \"\",\n}) => {\n  if (!strength) return null;\n\n  const getColorClass = () => {\n    switch (strength) {\n      case \"weak\":\n        return \"bg-red-500\";\n      case \"medium\":\n        return \"bg-yellow-500\";\n      case \"strong\":\n        return \"bg-green-500\";\n      default:\n        return \"\";\n    }\n  };\n\n  const getWidthClass = () => {\n    switch (strength) {\n      case \"weak\":\n        return \"w-1/3\";\n      case \"medium\":\n        return \"w-2/3\";\n      case \"strong\":\n        return \"w-full\";\n      default:\n        return \"\";\n    }\n  };\n\n  // تحقق من شروط كلمة السر\n  const hasMinLength = password.length >= 6;\n  const hasUppercase = /[A-Z]/.test(password);\n  const hasLowercase = /[a-z]/.test(password);\n  const hasNumbers = /[0-9]/.test(password);\n  const hasSpecialChars = /[!@#$%^&*(),.?\":{}|<>]/.test(password);\n\n  const renderCriteriaItem = (label: string, isMet: boolean) => (\n    <div className=\"flex items-center space-x-2\">\n      {isMet ? (\n        <Check className=\"h-3 w-3 text-green-500\" />\n      ) : (\n        <X className=\"h-3 w-3 text-red-500\" />\n      )}\n      <span className={`text-xs ${isMet ? \"text-green-600\" : \"text-gray-500\"}`}>\n        {label}\n      </span>\n    </div>\n  );\n\n  return (\n    <div className=\"!mt-4 space-y-2\">\n      <div className=\"h-1 w-full bg-gray-200 rounded-full overflow-hidden\">\n        <div\n          className={`h-full ${getColorClass()} ${getWidthClass()} transition-all duration-300`}\n        ></div>\n      </div>\n      <p\n        className={`text-xs ${\n          strength === \"weak\"\n            ? \"text-red-500\"\n            : strength === \"medium\"\n            ? \"text-yellow-500\"\n            : \"text-green-500\"\n        }`}\n      >\n        قوة كلمة المرور:{\" \"}\n        {strength === \"weak\"\n          ? \"ضعيفة\"\n          : strength === \"medium\"\n          ? \"متوسطة\"\n          : \"قوية\"}\n      </p>\n\n      <div className=\"bg-gray-50 p-2 rounded-md border border-gray-200 mt-2\">\n        <p className=\"text-xs font-medium text-gray-700 mb-1\">\n          متطلبات كلمة المرور:\n        </p>\n        <div className=\"grid grid-cols-2 gap-1\">\n          {renderCriteriaItem(\"6 أحرف على الأقل\", hasMinLength)}\n          {renderCriteriaItem(\"حرف كبير (A-Z)\", hasUppercase)}\n          {renderCriteriaItem(\"حرف صغير (a-z)\", hasLowercase)}\n          {renderCriteriaItem(\"رقم (0-9)\", hasNumbers)}\n          {renderCriteriaItem(\"رمز خاص (!@#$)\", hasSpecialChars)}\n        </div>\n      </div>\n    </div>\n  );\n};\n\nexport default PasswordStrengthIndicator;\n"], "names": [], "mappings": ";;;;AAEA;AAAA;;;AAOA,MAAM,4BAAsE,CAAC,EAC3E,QAAQ,EACR,WAAW,EAAE,EACd;IACC,IAAI,CAAC,UAAU,OAAO;IAEtB,MAAM,gBAAgB;QACpB,OAAQ;YACN,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT;gBACE,OAAO;QACX;IACF;IAEA,MAAM,gBAAgB;QACpB,OAAQ;YACN,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT;gBACE,OAAO;QACX;IACF;IAEA,yBAAyB;IACzB,MAAM,eAAe,SAAS,MAAM,IAAI;IACxC,MAAM,eAAe,QAAQ,IAAI,CAAC;IAClC,MAAM,eAAe,QAAQ,IAAI,CAAC;IAClC,MAAM,aAAa,QAAQ,IAAI,CAAC;IAChC,MAAM,kBAAkB,yBAAyB,IAAI,CAAC;IAEtD,MAAM,qBAAqB,CAAC,OAAe,sBACzC,8OAAC;YAAI,WAAU;;gBACZ,sBACC,8OAAC,oMAAA,CAAA,QAAK;oBAAC,WAAU;;;;;yCAEjB,8OAAC,4LAAA,CAAA,IAAC;oBAAC,WAAU;;;;;;8BAEf,8OAAC;oBAAK,WAAW,CAAC,QAAQ,EAAE,QAAQ,mBAAmB,iBAAiB;8BACrE;;;;;;;;;;;;IAKP,qBACE,8OAAC;QAAI,WAAU;;0BACb,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBACC,WAAW,CAAC,OAAO,EAAE,gBAAgB,CAAC,EAAE,gBAAgB,4BAA4B,CAAC;;;;;;;;;;;0BAGzF,8OAAC;gBACC,WAAW,CAAC,QAAQ,EAClB,aAAa,SACT,iBACA,aAAa,WACb,oBACA,kBACJ;;oBACH;oBACkB;oBAChB,aAAa,SACV,UACA,aAAa,WACb,WACA;;;;;;;0BAGN,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAE,WAAU;kCAAyC;;;;;;kCAGtD,8OAAC;wBAAI,WAAU;;4BACZ,mBAAmB,oBAAoB;4BACvC,mBAAmB,kBAAkB;4BACrC,mBAAmB,kBAAkB;4BACrC,mBAAmB,aAAa;4BAChC,mBAAmB,kBAAkB;;;;;;;;;;;;;;;;;;;AAKhD;uCAEe", "debugId": null}}, {"offset": {"line": 3213, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/IMPORTANT/work/export/front/src/app/account/components/PasswordForm.tsx"], "sourcesContent": ["\"use client\";\n\nimport React, { useState, useEffect } from \"react\";\nimport { toast } from \"react-toastify\";\nimport { Lock, Loader2 } from \"lucide-react\";\nimport PasswordInput from \"@/components/common/PasswordInput\";\nimport PasswordStrengthIndicator from \"@/components/PasswordStrengthIndicator\";\n\nimport {\n  FormErrors,\n  PasswordStrength,\n  checkPasswordStrength,\n} from \"@/utils/formUtils\";\n\nexport interface PasswordFormData {\n  currentPassword: string;\n  newPassword: string;\n  confirmPassword: string;\n}\n\ninterface PasswordFormProps {\n  onSubmit: (data: {\n    currentPassword: string;\n    newPassword: string;\n  }) => Promise<void>;\n  loading: boolean;\n}\n\nconst PasswordForm: React.FC<PasswordFormProps> = ({\n  onSubmit,\n  loading: externalLoading,\n}) => {\n  const [formData, setFormData] = useState<PasswordFormData>({\n    currentPassword: \"\",\n    newPassword: \"\",\n    confirmPassword: \"\",\n  });\n  const [internalLoading, setInternalLoading] = useState(false);\n  const loading = externalLoading || internalLoading;\n  const [errors, setErrors] = useState<FormErrors>({});\n  const [passwordStrength, setPasswordStrength] =\n    useState<PasswordStrength>(\"\");\n\n  useEffect(() => {\n    const strength = formData.newPassword\n      ? checkPasswordStrength(formData.newPassword)\n      : \"\";\n    setPasswordStrength(strength);\n  }, [formData.newPassword]);\n\n  const validateForm = (data: PasswordFormData): FormErrors => {\n    const errors: FormErrors = {};\n\n    if (!data.currentPassword) {\n      errors.currentPassword = \"كلمة المرور الحالية مطلوبة\";\n    }\n\n    if (!data.newPassword) {\n      errors.newPassword = \"كلمة المرور الجديدة مطلوبة\";\n    } else if (data.newPassword.length < 6) {\n      errors.newPassword = \"يجب أن تتكون كلمة المرور من 6 أحرف على الأقل\";\n    } else {\n      const strength = checkPasswordStrength(data.newPassword);\n      if (strength === \"weak\") {\n        errors.newPassword =\n          \"كلمة المرور ضعيفة جدًا. يجب أن تحتوي على أحرف كبيرة وصغيرة وأرقام\";\n      }\n    }\n\n    if (!data.confirmPassword) {\n      errors.confirmPassword = \"يرجى تأكيد كلمة المرور الجديدة\";\n    } else if (data.newPassword !== data.confirmPassword) {\n      errors.confirmPassword = \"كلمات المرور غير متطابقة\";\n    }\n\n    return errors;\n  };\n\n  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {\n    const { name, value } = e.target;\n    setFormData((prev) => ({\n      ...prev,\n      [name]: value,\n    }));\n  };\n\n  const handleSubmit = async (e: React.FormEvent) => {\n    e.preventDefault();\n\n    const newErrors = validateForm(formData);\n    setErrors(newErrors);\n\n    if (Object.keys(newErrors).length > 0) {\n      if (Object.keys(newErrors).length === 1) {\n        const firstErrorKey = Object.keys(newErrors)[0];\n        const firstErrorMessage = newErrors[firstErrorKey as keyof FormErrors];\n        if (firstErrorMessage) {\n          toast.error(firstErrorMessage, {\n            autoClose: false,\n            hideProgressBar: true,\n            closeOnClick: true,\n            pauseOnHover: false,\n            draggable: true,\n          });\n        }\n      } else {\n        toast.error(\"يرجى تصحيح الأخطاء في النموذج\", {\n          autoClose: false,\n          hideProgressBar: true,\n          closeOnClick: true,\n          pauseOnHover: false,\n          draggable: true,\n        });\n      }\n      return;\n    }\n\n    const loadingToastId = toast.loading(\"جاري تغيير كلمة المرور...\", {\n      hideProgressBar: true,\n      closeOnClick: false,\n      pauseOnHover: false,\n      draggable: false,\n    });\n    setInternalLoading(true);\n\n    try {\n      await onSubmit({\n        currentPassword: formData.currentPassword,\n        newPassword: formData.newPassword,\n      });\n\n      setFormData({\n        currentPassword: \"\",\n        newPassword: \"\",\n        confirmPassword: \"\",\n      });\n\n      toast.dismiss(loadingToastId);\n    } catch (error) {\n      console.error(\"خطأ في تغيير كلمة المرور:\", error);\n      toast.dismiss(loadingToastId);\n      toast.error(\"فشل تغيير كلمة المرور\", {\n        autoClose: false,\n        hideProgressBar: true,\n        closeOnClick: true,\n        pauseOnHover: false,\n        draggable: true,\n      });\n    } finally {\n      setInternalLoading(false);\n    }\n  };\n\n  return (\n    <form onSubmit={handleSubmit} className=\"space-y-6\">\n      <div className=\"space-y-4\">\n        <PasswordInput\n          id=\"currentPassword\"\n          name=\"currentPassword\"\n          placeholder=\"كلمة المرور الحالية\"\n          value={formData.currentPassword}\n          onChange={handleInputChange}\n          error={!!errors.currentPassword}\n          autoComplete=\"current-password\"\n        />\n\n        <div className=\"space-y-1\">\n          <PasswordInput\n            id=\"newPassword\"\n            name=\"newPassword\"\n            placeholder=\"كلمة المرور الجديدة\"\n            value={formData.newPassword}\n            onChange={handleInputChange}\n            error={!!errors.newPassword}\n            autoComplete=\"new-password\"\n          />\n\n          {formData.newPassword && (\n            <PasswordStrengthIndicator\n              strength={passwordStrength}\n              password={formData.newPassword}\n            />\n          )}\n        </div>\n\n        <PasswordInput\n          id=\"confirmPassword\"\n          name=\"confirmPassword\"\n          placeholder=\"تأكيد كلمة المرور الجديدة\"\n          value={formData.confirmPassword}\n          onChange={handleInputChange}\n          error={!!errors.confirmPassword}\n          autoComplete=\"new-password\"\n        />\n      </div>\n\n      <button\n        type=\"submit\"\n        disabled={loading}\n        className=\"w-full flex justify-center items-center py-3 px-6 border border-transparent text-base font-semibold rounded-xl text-white  bg-pink-500 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-pink-500 disabled:opacity-50 disabled:cursor-not-allowed transition-all duration-200 shadow-lg hover:shadow-xl\"\n      >\n        {loading ? (\n          <>\n            <Loader2 className=\"w-5 h-5 mr-2 animate-spin\" />\n            جاري تغيير كلمة المرور...\n          </>\n        ) : (\n          <>\n            <Lock className=\"w-5 h-5 mr-2\" />\n            تغيير كلمة المرور\n          </>\n        )}\n      </button>\n    </form>\n  );\n};\n\nexport default PasswordForm;\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAAA;AACA;AACA;AAEA;AARA;;;;;;;;AA4BA,MAAM,eAA4C,CAAC,EACjD,QAAQ,EACR,SAAS,eAAe,EACzB;IACC,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAoB;QACzD,iBAAiB;QACjB,aAAa;QACb,iBAAiB;IACnB;IACA,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACvD,MAAM,UAAU,mBAAmB;IACnC,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAc,CAAC;IAClD,MAAM,CAAC,kBAAkB,oBAAoB,GAC3C,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAoB;IAE7B,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,MAAM,WAAW,SAAS,WAAW,GACjC,CAAA,GAAA,yHAAA,CAAA,wBAAqB,AAAD,EAAE,SAAS,WAAW,IAC1C;QACJ,oBAAoB;IACtB,GAAG;QAAC,SAAS,WAAW;KAAC;IAEzB,MAAM,eAAe,CAAC;QACpB,MAAM,SAAqB,CAAC;QAE5B,IAAI,CAAC,KAAK,eAAe,EAAE;YACzB,OAAO,eAAe,GAAG;QAC3B;QAEA,IAAI,CAAC,KAAK,WAAW,EAAE;YACrB,OAAO,WAAW,GAAG;QACvB,OAAO,IAAI,KAAK,WAAW,CAAC,MAAM,GAAG,GAAG;YACtC,OAAO,WAAW,GAAG;QACvB,OAAO;YACL,MAAM,WAAW,CAAA,GAAA,yHAAA,CAAA,wBAAqB,AAAD,EAAE,KAAK,WAAW;YACvD,IAAI,aAAa,QAAQ;gBACvB,OAAO,WAAW,GAChB;YACJ;QACF;QAEA,IAAI,CAAC,KAAK,eAAe,EAAE;YACzB,OAAO,eAAe,GAAG;QAC3B,OAAO,IAAI,KAAK,WAAW,KAAK,KAAK,eAAe,EAAE;YACpD,OAAO,eAAe,GAAG;QAC3B;QAEA,OAAO;IACT;IAEA,MAAM,oBAAoB,CAAC;QACzB,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,EAAE,MAAM;QAChC,YAAY,CAAC,OAAS,CAAC;gBACrB,GAAG,IAAI;gBACP,CAAC,KAAK,EAAE;YACV,CAAC;IACH;IAEA,MAAM,eAAe,OAAO;QAC1B,EAAE,cAAc;QAEhB,MAAM,YAAY,aAAa;QAC/B,UAAU;QAEV,IAAI,OAAO,IAAI,CAAC,WAAW,MAAM,GAAG,GAAG;YACrC,IAAI,OAAO,IAAI,CAAC,WAAW,MAAM,KAAK,GAAG;gBACvC,MAAM,gBAAgB,OAAO,IAAI,CAAC,UAAU,CAAC,EAAE;gBAC/C,MAAM,oBAAoB,SAAS,CAAC,cAAkC;gBACtE,IAAI,mBAAmB;oBACrB,mJAAA,CAAA,QAAK,CAAC,KAAK,CAAC,mBAAmB;wBAC7B,WAAW;wBACX,iBAAiB;wBACjB,cAAc;wBACd,cAAc;wBACd,WAAW;oBACb;gBACF;YACF,OAAO;gBACL,mJAAA,CAAA,QAAK,CAAC,KAAK,CAAC,iCAAiC;oBAC3C,WAAW;oBACX,iBAAiB;oBACjB,cAAc;oBACd,cAAc;oBACd,WAAW;gBACb;YACF;YACA;QACF;QAEA,MAAM,iBAAiB,mJAAA,CAAA,QAAK,CAAC,OAAO,CAAC,6BAA6B;YAChE,iBAAiB;YACjB,cAAc;YACd,cAAc;YACd,WAAW;QACb;QACA,mBAAmB;QAEnB,IAAI;YACF,MAAM,SAAS;gBACb,iBAAiB,SAAS,eAAe;gBACzC,aAAa,SAAS,WAAW;YACnC;YAEA,YAAY;gBACV,iBAAiB;gBACjB,aAAa;gBACb,iBAAiB;YACnB;YAEA,mJAAA,CAAA,QAAK,CAAC,OAAO,CAAC;QAChB,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,6BAA6B;YAC3C,mJAAA,CAAA,QAAK,CAAC,OAAO,CAAC;YACd,mJAAA,CAAA,QAAK,CAAC,KAAK,CAAC,yBAAyB;gBACnC,WAAW;gBACX,iBAAiB;gBACjB,cAAc;gBACd,cAAc;gBACd,WAAW;YACb;QACF,SAAU;YACR,mBAAmB;QACrB;IACF;IAEA,qBACE,8OAAC;QAAK,UAAU;QAAc,WAAU;;0BACtC,8OAAC;gBAAI,WAAU;;kCACb,8OAAC,6IAAA,CAAA,UAAa;wBACZ,IAAG;wBACH,MAAK;wBACL,aAAY;wBACZ,OAAO,SAAS,eAAe;wBAC/B,UAAU;wBACV,OAAO,CAAC,CAAC,OAAO,eAAe;wBAC/B,cAAa;;;;;;kCAGf,8OAAC;wBAAI,WAAU;;0CACb,8OAAC,6IAAA,CAAA,UAAa;gCACZ,IAAG;gCACH,MAAK;gCACL,aAAY;gCACZ,OAAO,SAAS,WAAW;gCAC3B,UAAU;gCACV,OAAO,CAAC,CAAC,OAAO,WAAW;gCAC3B,cAAa;;;;;;4BAGd,SAAS,WAAW,kBACnB,8OAAC,+IAAA,CAAA,UAAyB;gCACxB,UAAU;gCACV,UAAU,SAAS,WAAW;;;;;;;;;;;;kCAKpC,8OAAC,6IAAA,CAAA,UAAa;wBACZ,IAAG;wBACH,MAAK;wBACL,aAAY;wBACZ,OAAO,SAAS,eAAe;wBAC/B,UAAU;wBACV,OAAO,CAAC,CAAC,OAAO,eAAe;wBAC/B,cAAa;;;;;;;;;;;;0BAIjB,8OAAC;gBACC,MAAK;gBACL,UAAU;gBACV,WAAU;0BAET,wBACC;;sCACE,8OAAC,iNAAA,CAAA,UAAO;4BAAC,WAAU;;;;;;wBAA8B;;iDAInD;;sCACE,8OAAC,kMAAA,CAAA,OAAI;4BAAC,WAAU;;;;;;wBAAiB;;;;;;;;;;;;;;AAO7C;uCAEe", "debugId": null}}, {"offset": {"line": 3452, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/IMPORTANT/work/export/front/src/app/account/components/PermissionsForm.tsx"], "sourcesContent": ["\"use client\";\n\nimport React, { useState, useEffect, ReactNode } from \"react\";\nimport { toast } from \"react-toastify\";\nimport {\n  Loader2,\n  Plus,\n  Shield,\n  X,\n  Image as ImageIcon,\n  Trash2,\n} from \"lucide-react\";\nimport Image from \"next/image\";\nimport Input from \"@/components/common/Input\";\nimport { useDropzone } from \"react-dropzone\";\nimport { ChildData } from \"@/types/auth\";\n\ninterface Permission {\n  approval_status: ReactNode;\n  id: number;\n  child: number;\n  child_name: string;\n  receiver_name: string;\n  id_image: string;\n  otp: string;\n  created_at: string;\n  is_active: boolean;\n}\n\ninterface PermissionFormData {\n  child_id: string;\n  receiver_name: string;\n  id_image: File | null;\n}\n\ninterface PermissionsFormProps {\n  userChildren: ChildData[];\n}\n\nconst PermissionsForm: React.FC<PermissionsFormProps> = ({ userChildren }) => {\n  const [permissions, setPermissions] = useState<Permission[]>([]);\n  const [loading, setLoading] = useState(true);\n  const [submitLoading, setSubmitLoading] = useState(false);\n  const [showForm, setShowForm] = useState(false);\n  const [showDeleteModal, setShowDeleteModal] = useState(false);\n  const [selectedPermission, setSelectedPermission] =\n    useState<Permission | null>(null);\n  const [deleteLoading, setDeleteLoading] = useState<{\n    [key: number]: boolean;\n  }>({});\n  const [formData, setFormData] = useState<PermissionFormData>({\n    child_id: \"\",\n    receiver_name: \"\",\n    id_image: null,\n  });\n  const [errors, setErrors] = useState<Record<string, string>>({});\n  const [previewUrl, setPreviewUrl] = useState<string>(\"\");\n\n  const { getRootProps, getInputProps } = useDropzone({\n    accept: {\n      \"image/*\": [\".jpeg\", \".jpg\", \".png\", \".gif\"],\n    },\n    maxFiles: 1,\n    onDrop: (acceptedFiles) => {\n      if (acceptedFiles.length > 0) {\n        const file = acceptedFiles[0];\n        setFormData({ ...formData, id_image: file });\n\n        const url = URL.createObjectURL(file);\n        setPreviewUrl(url);\n\n        if (errors.id_image) {\n          setErrors({ ...errors, id_image: \"\" });\n        }\n      }\n    },\n  });\n\n  useEffect(() => {\n    fetchPermissions();\n  }, []);\n\n  useEffect(() => {\n    return () => {\n      if (previewUrl) {\n        URL.revokeObjectURL(previewUrl);\n      }\n    };\n  }, [previewUrl]);\n\n  const fetchPermissions = async () => {\n    setLoading(true);\n    try {\n      const response = await fetch(\"http://localhost:8000/api/permissions/\", {\n        headers: {\n          Authorization: `Bearer ${localStorage.getItem(\"access_token\")}`,\n        },\n      });\n\n      if (response.ok) {\n        const data = await response.json();\n        setPermissions(data);\n      } else {\n        toast.error(\"فشل تحميل التصاريح\", {\n          autoClose: false,\n          hideProgressBar: true,\n          closeOnClick: true,\n          pauseOnHover: false,\n          draggable: true,\n        });\n      }\n    } catch (error) {\n      console.error(\"خطأ في تحميل التصاريح:\", error);\n      toast.error(\"فشل تحميل التصاريح\", {\n        autoClose: false,\n        hideProgressBar: true,\n        closeOnClick: true,\n        pauseOnHover: false,\n        draggable: true,\n      });\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  const validateForm = (): boolean => {\n    const newErrors: Record<string, string> = {};\n\n    if (!formData.child_id) {\n      newErrors.child_id = \"الرجاء اختيار طفل\";\n    }\n\n    if (!formData.receiver_name.trim()) {\n      newErrors.receiver_name = \"اسم المستلم مطلوب\";\n    } else if (formData.receiver_name.trim().length < 2) {\n      newErrors.receiver_name = \"يجب أن يكون اسم المستلم من حرفين على الأقل\";\n    }\n\n    if (!formData.id_image) {\n      newErrors.id_image = \"صورة الهوية مطلوبة\";\n    }\n\n    setErrors(newErrors);\n    return Object.keys(newErrors).length === 0;\n  };\n\n  const handleSubmit = async (e: React.FormEvent) => {\n    e.preventDefault();\n\n    if (!validateForm()) {\n      toast.error(\"الرجاء تصحيح الأخطاء في النموذج\", {\n        autoClose: false,\n        hideProgressBar: true,\n        closeOnClick: true,\n        pauseOnHover: false,\n        draggable: true,\n      });\n      return;\n    }\n\n    setSubmitLoading(true);\n\n    try {\n      const submitData = new FormData();\n      submitData.append(\"child\", formData.child_id);\n      submitData.append(\"receiver_name\", formData.receiver_name.trim());\n      if (formData.id_image) {\n        submitData.append(\n          \"id_image\",\n          formData.id_image,\n          formData.id_image.name\n        );\n      }\n\n      const response = await fetch(\"http://localhost:8000/api/permissions/\", {\n        method: \"POST\",\n        headers: {\n          Authorization: `Bearer ${localStorage.getItem(\"access_token\")}`,\n        },\n        body: submitData,\n      });\n\n      if (response.ok) {\n        const result = await response.json();\n\n        toast.success(`تم إنشاء التصريح بنجاح! رمز OTP: ${result.otp}`, {\n          autoClose: 5000,\n          hideProgressBar: true,\n          closeOnClick: true,\n          pauseOnHover: false,\n          draggable: true,\n        });\n\n        setFormData({\n          child_id: \"\",\n          receiver_name: \"\",\n          id_image: null,\n        });\n        setPreviewUrl(\"\");\n        setShowForm(false);\n\n        fetchPermissions();\n      } else {\n        const errorData = await response.json();\n        toast.error(errorData.message || \"فشل إنشاء التصريح\", {\n          autoClose: false,\n          hideProgressBar: true,\n          closeOnClick: true,\n          pauseOnHover: false,\n          draggable: true,\n        });\n      }\n    } catch (error) {\n      console.error(\"خطأ في إنشاء التصريح:\", error);\n      toast.error(\"فشل إنشاء التصريح\", {\n        autoClose: false,\n        hideProgressBar: true,\n        closeOnClick: true,\n        pauseOnHover: false,\n        draggable: true,\n      });\n    } finally {\n      setSubmitLoading(false);\n    }\n  };\n\n  const removeImage = () => {\n    setFormData({ ...formData, id_image: null });\n    setPreviewUrl(\"\");\n    if (errors.id_image) {\n      setErrors({ ...errors, id_image: \"\" });\n    }\n  };\n\n  const handleDeletePermission = async (\n    permissionId: number,\n    receiverName: string\n  ) => {\n    setDeleteLoading({ ...deleteLoading, [permissionId]: true });\n\n    try {\n      const response = await fetch(\n        `http://localhost:8000/api/permissions/${permissionId}/`,\n        {\n          method: \"DELETE\",\n          headers: {\n            Authorization: `Bearer ${localStorage.getItem(\"access_token\")}`,\n          },\n        }\n      );\n\n      if (response.ok) {\n        toast.success(\"تم حذف التصريح بنجاح\", {\n          autoClose: 1500,\n          hideProgressBar: true,\n          closeOnClick: true,\n          pauseOnHover: false,\n          draggable: true,\n        });\n\n        fetchPermissions();\n      } else {\n        const errorData = await response.json();\n        toast.error(errorData.message || \"فشل حذف التصريح\", {\n          autoClose: false,\n          hideProgressBar: true,\n          closeOnClick: true,\n          pauseOnHover: false,\n          draggable: true,\n        });\n      }\n    } catch (error) {\n      console.error(\"خطأ في حذف التصريح:\", error);\n      toast.error(\"فشل حذف التصريح\", {\n        autoClose: false,\n        hideProgressBar: true,\n        closeOnClick: true,\n        pauseOnHover: false,\n        draggable: true,\n      });\n    } finally {\n      setDeleteLoading({ ...deleteLoading, [permissionId]: false });\n    }\n  };\n\n  if (loading) {\n    return (\n      <div className=\"flex items-center justify-center py-8\">\n        <div className=\"text-center\">\n          <Loader2 className=\"w-8 h-8 animate-spin mx-auto text-slate-600\" />\n          <p className=\"mt-4 text-gray-600\">جاري تحميل التصاريح...</p>\n        </div>\n      </div>\n    );\n  }\n\n  return (\n    <div className=\"space-y-6\">\n      {/* Header with Add Button */}\n      <div className=\"flex justify-between items-center\">\n        <div>\n          <h4 className=\"text-lg font-semibold text-gray-900\">تصاريحك</h4>\n          <p className=\"text-sm text-gray-600\">إدارة تصاريح استلام الأطفال</p>\n        </div>\n        <button\n          onClick={() => setShowForm(!showForm)}\n          className=\"flex items-center px-4 py-2 bg-pink-500 text-white rounded-lg  transition-all duration-200 shadow-md\"\n        >\n          <Plus className=\"w-4 h-4 mr-2\" />\n          إضافة تصريح\n        </button>\n      </div>\n\n      {/* Add Permission Form */}\n      {showForm && (\n        <div className=\"border border-gray-200 rounded-xl overflow-hidden\">\n          <div className=\"bg-gradient-to-r from-slate-50 to-blue-50 px-6 py-4 border-b border-gray-200\">\n            <div className=\"flex justify-between items-center\">\n              <h5 className=\"text-lg font-semibold text-gray-900\">\n                إنشاء تصريح جديد\n              </h5>\n              <button\n                onClick={() => setShowForm(false)}\n                className=\"text-gray-500 hover:text-gray-700\"\n              >\n                <X className=\"w-5 h-5\" />\n              </button>\n            </div>\n          </div>\n\n          <form onSubmit={handleSubmit} className=\"p-6 space-y-6\">\n            {/* Child Selection */}\n            <div>\n              <label className=\"block text-sm font-medium text-gray-700 mb-2\">\n                اختر الطفل <span className=\"text-red-500\">*</span>\n              </label>\n              {userChildren.length === 0 ? (\n                <div className=\"bg-yellow-50 border border-yellow-200 rounded-lg p-4\">\n                  <p className=\"text-yellow-700 text-sm\">\n                    لم يتم العثور على أطفال. يرجى إضافة أطفال إلى ملفك الشخصي\n                    أولاً.\n                  </p>\n                </div>\n              ) : (\n                <div className=\"space-y-2\">\n                  {userChildren.map((child) => (\n                    <label\n                      key={child.id}\n                      className=\"flex items-center p-3 border border-gray-200 rounded-lg hover:bg-gray-50 cursor-pointer\"\n                    >\n                      <input\n                        type=\"radio\"\n                        name=\"child_id\"\n                        value={child.id}\n                        checked={formData.child_id === child.id}\n                        onChange={(e) =>\n                          setFormData({ ...formData, child_id: e.target.value })\n                        }\n                        className=\"mr-3\"\n                      />\n                      <div>\n                        <p className=\"font-medium text-gray-900\">\n                          {child.name}\n                        </p>\n                        <p className=\"text-sm text-gray-500\">\n                          الصف: {child.class} | المرحلة: {child.stage}\n                        </p>\n                      </div>\n                    </label>\n                  ))}\n                </div>\n              )}\n              {errors.child_id && (\n                <p className=\"text-sm text-red-500 mt-1\">{errors.child_id}</p>\n              )}\n            </div>\n\n            {/* Receiver Name */}\n            <Input\n              id=\"receiver_name\"\n              label=\"اسم المستلم\"\n              value={formData.receiver_name}\n              onChange={(e) =>\n                setFormData({ ...formData, receiver_name: e.target.value })\n              }\n              error={!!errors.receiver_name}\n              errorMessage={errors.receiver_name}\n              placeholder=\"أدخل اسم الشخص \"\n              required\n            />\n\n            {/* ID Image Upload */}\n            <div>\n              <label className=\"block text-sm font-medium text-gray-700 mb-2\">\n                صورة هوية المستلم <span className=\"text-red-500\">*</span>\n              </label>\n              <div\n                {...getRootProps()}\n                className={`relative border-2 border-dashed ${\n                  errors.id_image ? \"border-red-500\" : \"border-gray-300\"\n                } p-6 rounded-xl text-center cursor-pointer hover:border-slate-400 focus:outline-none focus:border-slate-600 transition-all duration-200 bg-gradient-to-br from-gray-50 to-slate-50`}\n              >\n                <input {...getInputProps()} />\n                {previewUrl ? (\n                  <div className=\"relative inline-block\">\n                    <Image\n                      src={previewUrl}\n                      alt=\"معاينة الهوية\"\n                      width={160}\n                      height={160}\n                      className=\"max-h-40 rounded-lg shadow-lg object-contain\"\n                    />\n                    <button\n                      type=\"button\"\n                      onClick={(e) => {\n                        e.stopPropagation();\n                        removeImage();\n                      }}\n                      className=\"absolute -top-2 -right-2 bg-red-500 text-white rounded-full p-1.5 hover:bg-red-600 transition-colors duration-200 shadow-lg\"\n                    >\n                      <X className=\"h-4 w-4\" />\n                    </button>\n                  </div>\n                ) : (\n                  <div className=\"flex flex-col items-center space-y-3\">\n                    <ImageIcon className=\"h-12 w-12 text-gray-400\" />\n                    <div>\n                      <p className=\"text-gray-600 font-medium\">\n                        اسحب وأسقط صورة هوية المستلم هنا\n                      </p>\n                      <p className=\"text-sm text-gray-500 mt-1\">\n                        أو انقر لتحديد من جهازك\n                      </p>\n                    </div>\n                  </div>\n                )}\n              </div>\n              {errors.id_image && (\n                <p className=\"text-sm text-red-500 mt-1\">{errors.id_image}</p>\n              )}\n            </div>\n\n            {/* Submit Button */}\n            <div className=\"flex justify-end space-x-4\">\n              <button\n                type=\"button\"\n                onClick={() => setShowForm(false)}\n                className=\"px-6 py-2 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 transition-colors duration-200\"\n              >\n                إلغاء\n              </button>\n              <button\n                type=\"submit\"\n                disabled={submitLoading || userChildren.length === 0}\n                className=\"flex items-center px-6 py-2  text-white rounded-lg bg-pink-500 hover:bg-pink-600 disabled:opacity-50 disabled:cursor-not-allowed transition-all duration-200 shadow-md\"\n              >\n                {submitLoading ? (\n                  <>\n                    <Loader2 className=\"w-4 h-4 mr-2 animate-spin\" />\n                    جاري الإنشاء...\n                  </>\n                ) : (\n                  <>\n                    <Shield className=\"w-4 h-4 mr-2\" />\n                    إنشاء تصريح\n                  </>\n                )}\n              </button>\n            </div>\n          </form>\n        </div>\n      )}\n\n      {/* Permissions List */}\n      <div className=\"space-y-4\">\n        {permissions.length === 0 ? (\n          <div className=\"text-center py-8\">\n            <Shield className=\"w-12 h-12 text-gray-400 mx-auto mb-4\" />\n            <p className=\"text-gray-500\">لا توجد تصاريح حتى الآن.</p>\n            <p className=\"text-sm text-gray-400\">\n              قم بإنشاء أول تصريح لك للسماح لشخص ما باستلام طفلك.\n            </p>\n          </div>\n        ) : (\n          permissions.map((permission) => (\n            <div\n              key={permission.id}\n              className=\"border border-gray-200 rounded-xl p-6 bg-gradient-to-br from-white to-gray-50\"\n            >\n              <div className=\"flex justify-between items-start\">\n                <div className=\"flex-1\">\n                  <div className=\"flex items-center mb-2\">\n                    <Shield className=\"w-5 h-5 text-pink-600 mr-2\" />\n                    <h6 className=\"font-semibold text-gray-900\">\n                      {permission.receiver_name}\n                    </h6>\n                    <span\n                      className={`ml-3 px-2 py-1 text-xs rounded-full ${\n                        permission.approval_status === \"approved\"\n                          ? \"bg-green-100 text-green-800\"\n                          : permission.approval_status === \"pending\"\n                          ? \"bg-yellow-100 text-yellow-800\"\n                          : \"bg-red-100 text-red-800\"\n                      }`}\n                    >\n                      {permission.approval_status === \"approved\"\n                        ? \"موافق عليه\"\n                        : permission.approval_status === \"pending\"\n                        ? \"قيد الانتظار\"\n                        : \"مرفوض\"}\n                    </span>\n                  </div>\n                  <p className=\"text-sm text-gray-600 mb-1\">\n                    الطفل:{\" \"}\n                    <span className=\"font-medium\">{permission.child_name}</span>\n                  </p>\n                  <p className=\"text-sm text-gray-600 mb-2\">\n                    تاريخ الإنشاء:{\" \"}\n                    {new Date(permission.created_at).toLocaleDateString()}\n                  </p>\n                  <div className=\"bg-slate-100 rounded-lg p-3 inline-block\">\n                    <p className=\"text-sm font-medium text-pink-700\">\n                      رمز OTP:{\" \"}\n                      <span className=\"font-mono text-lg\">\n                        {permission.otp}\n                      </span>\n                    </p>\n                  </div>\n                </div>\n\n                <div className=\"ml-4 flex items-center space-x-3\">\n                  {permission.id_image && (\n                    <Image\n                      src={permission.id_image}\n                      alt=\"هوية المستلم\"\n                      width={80}\n                      height={80}\n                      className=\"rounded-lg shadow-md object-cover\"\n                    />\n                  )}\n\n                  {/* Delete Button */}\n                  <button\n                    onClick={() => {\n                      setSelectedPermission(permission);\n                      setShowDeleteModal(true);\n                    }}\n                    className=\"text-red-600 hover:text-red-800 cursor-pointer bg-red-200 rounded-lg text-center flex items-center p-2\"\n                  >\n                    <Trash2 className=\"w-5 h-5 \" />\n                  </button>\n                </div>\n              </div>\n              {showDeleteModal && selectedPermission && (\n                <div className=\"fixed inset-0 z-50 flex items-center justify-center bg-black bg-opacity-50\">\n                  <div className=\"bg-white rounded-xl shadow-lg p-6 w-full max-w-md\">\n                    <h2 className=\"text-lg font-semibold text-gray-900 mb-4\">\n                      تأكيد الحذف\n                    </h2>\n                    <p className=\"text-sm text-gray-700 mb-6\">\n                      هل أنت متأكد أنك تريد حذف التصريح لـ{\" \"}\n                      <span className=\"font-bold\">\n                        {selectedPermission.receiver_name}\n                      </span>\n                      ؟\n                    </p>\n                    <div className=\"flex gap-3 justify-end space-x-3 rtl:space-x-reverse\">\n                      <button\n                        className=\"px-4 py-2 text-sm text-gray-700 border cursor-pointer border-gray-300 rounded-lg hover:bg-gray-100\"\n                        onClick={() => {\n                          setShowDeleteModal(false);\n                          setSelectedPermission(null);\n                        }}\n                      >\n                        إلغاء\n                      </button>\n                      <button\n                        className=\"px-4 py-2 text-sm text-white cursor-pointer bg-red-600 rounded-lg hover:bg-red-700\"\n                        onClick={() => {\n                          if (selectedPermission) {\n                            handleDeletePermission(\n                              selectedPermission.id,\n                              selectedPermission.receiver_name\n                            );\n                            setShowDeleteModal(false);\n                            setSelectedPermission(null);\n                          }\n                        }}\n                      >\n                        حذف\n                      </button>\n                    </div>\n                  </div>\n                </div>\n              )}\n            </div>\n          ))\n        )}\n      </div>\n    </div>\n  );\n};\n\nexport default PermissionsForm;\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAQA;AACA;AACA;AAAA;AAdA;;;;;;;;AAuCA,MAAM,kBAAkD,CAAC,EAAE,YAAY,EAAE;IACvE,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAgB,EAAE;IAC/D,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACnD,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACzC,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACvD,MAAM,CAAC,oBAAoB,sBAAsB,GAC/C,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAqB;IAC9B,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAE9C,CAAC;IACJ,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAsB;QAC3D,UAAU;QACV,eAAe;QACf,UAAU;IACZ;IACA,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAA0B,CAAC;IAC9D,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAU;IAErD,MAAM,EAAE,YAAY,EAAE,aAAa,EAAE,GAAG,CAAA,GAAA,wKAAA,CAAA,cAAW,AAAD,EAAE;QAClD,QAAQ;YACN,WAAW;gBAAC;gBAAS;gBAAQ;gBAAQ;aAAO;QAC9C;QACA,UAAU;QACV,QAAQ,CAAC;YACP,IAAI,cAAc,MAAM,GAAG,GAAG;gBAC5B,MAAM,OAAO,aAAa,CAAC,EAAE;gBAC7B,YAAY;oBAAE,GAAG,QAAQ;oBAAE,UAAU;gBAAK;gBAE1C,MAAM,MAAM,IAAI,eAAe,CAAC;gBAChC,cAAc;gBAEd,IAAI,OAAO,QAAQ,EAAE;oBACnB,UAAU;wBAAE,GAAG,MAAM;wBAAE,UAAU;oBAAG;gBACtC;YACF;QACF;IACF;IAEA,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR;IACF,GAAG,EAAE;IAEL,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,OAAO;YACL,IAAI,YAAY;gBACd,IAAI,eAAe,CAAC;YACtB;QACF;IACF,GAAG;QAAC;KAAW;IAEf,MAAM,mBAAmB;QACvB,WAAW;QACX,IAAI;YACF,MAAM,WAAW,MAAM,MAAM,0CAA0C;gBACrE,SAAS;oBACP,eAAe,CAAC,OAAO,EAAE,aAAa,OAAO,CAAC,iBAAiB;gBACjE;YACF;YAEA,IAAI,SAAS,EAAE,EAAE;gBACf,MAAM,OAAO,MAAM,SAAS,IAAI;gBAChC,eAAe;YACjB,OAAO;gBACL,mJAAA,CAAA,QAAK,CAAC,KAAK,CAAC,sBAAsB;oBAChC,WAAW;oBACX,iBAAiB;oBACjB,cAAc;oBACd,cAAc;oBACd,WAAW;gBACb;YACF;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,0BAA0B;YACxC,mJAAA,CAAA,QAAK,CAAC,KAAK,CAAC,sBAAsB;gBAChC,WAAW;gBACX,iBAAiB;gBACjB,cAAc;gBACd,cAAc;gBACd,WAAW;YACb;QACF,SAAU;YACR,WAAW;QACb;IACF;IAEA,MAAM,eAAe;QACnB,MAAM,YAAoC,CAAC;QAE3C,IAAI,CAAC,SAAS,QAAQ,EAAE;YACtB,UAAU,QAAQ,GAAG;QACvB;QAEA,IAAI,CAAC,SAAS,aAAa,CAAC,IAAI,IAAI;YAClC,UAAU,aAAa,GAAG;QAC5B,OAAO,IAAI,SAAS,aAAa,CAAC,IAAI,GAAG,MAAM,GAAG,GAAG;YACnD,UAAU,aAAa,GAAG;QAC5B;QAEA,IAAI,CAAC,SAAS,QAAQ,EAAE;YACtB,UAAU,QAAQ,GAAG;QACvB;QAEA,UAAU;QACV,OAAO,OAAO,IAAI,CAAC,WAAW,MAAM,KAAK;IAC3C;IAEA,MAAM,eAAe,OAAO;QAC1B,EAAE,cAAc;QAEhB,IAAI,CAAC,gBAAgB;YACnB,mJAAA,CAAA,QAAK,CAAC,KAAK,CAAC,mCAAmC;gBAC7C,WAAW;gBACX,iBAAiB;gBACjB,cAAc;gBACd,cAAc;gBACd,WAAW;YACb;YACA;QACF;QAEA,iBAAiB;QAEjB,IAAI;YACF,MAAM,aAAa,IAAI;YACvB,WAAW,MAAM,CAAC,SAAS,SAAS,QAAQ;YAC5C,WAAW,MAAM,CAAC,iBAAiB,SAAS,aAAa,CAAC,IAAI;YAC9D,IAAI,SAAS,QAAQ,EAAE;gBACrB,WAAW,MAAM,CACf,YACA,SAAS,QAAQ,EACjB,SAAS,QAAQ,CAAC,IAAI;YAE1B;YAEA,MAAM,WAAW,MAAM,MAAM,0CAA0C;gBACrE,QAAQ;gBACR,SAAS;oBACP,eAAe,CAAC,OAAO,EAAE,aAAa,OAAO,CAAC,iBAAiB;gBACjE;gBACA,MAAM;YACR;YAEA,IAAI,SAAS,EAAE,EAAE;gBACf,MAAM,SAAS,MAAM,SAAS,IAAI;gBAElC,mJAAA,CAAA,QAAK,CAAC,OAAO,CAAC,CAAC,iCAAiC,EAAE,OAAO,GAAG,EAAE,EAAE;oBAC9D,WAAW;oBACX,iBAAiB;oBACjB,cAAc;oBACd,cAAc;oBACd,WAAW;gBACb;gBAEA,YAAY;oBACV,UAAU;oBACV,eAAe;oBACf,UAAU;gBACZ;gBACA,cAAc;gBACd,YAAY;gBAEZ;YACF,OAAO;gBACL,MAAM,YAAY,MAAM,SAAS,IAAI;gBACrC,mJAAA,CAAA,QAAK,CAAC,KAAK,CAAC,UAAU,OAAO,IAAI,qBAAqB;oBACpD,WAAW;oBACX,iBAAiB;oBACjB,cAAc;oBACd,cAAc;oBACd,WAAW;gBACb;YACF;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,yBAAyB;YACvC,mJAAA,CAAA,QAAK,CAAC,KAAK,CAAC,qBAAqB;gBAC/B,WAAW;gBACX,iBAAiB;gBACjB,cAAc;gBACd,cAAc;gBACd,WAAW;YACb;QACF,SAAU;YACR,iBAAiB;QACnB;IACF;IAEA,MAAM,cAAc;QAClB,YAAY;YAAE,GAAG,QAAQ;YAAE,UAAU;QAAK;QAC1C,cAAc;QACd,IAAI,OAAO,QAAQ,EAAE;YACnB,UAAU;gBAAE,GAAG,MAAM;gBAAE,UAAU;YAAG;QACtC;IACF;IAEA,MAAM,yBAAyB,OAC7B,cACA;QAEA,iBAAiB;YAAE,GAAG,aAAa;YAAE,CAAC,aAAa,EAAE;QAAK;QAE1D,IAAI;YACF,MAAM,WAAW,MAAM,MACrB,CAAC,sCAAsC,EAAE,aAAa,CAAC,CAAC,EACxD;gBACE,QAAQ;gBACR,SAAS;oBACP,eAAe,CAAC,OAAO,EAAE,aAAa,OAAO,CAAC,iBAAiB;gBACjE;YACF;YAGF,IAAI,SAAS,EAAE,EAAE;gBACf,mJAAA,CAAA,QAAK,CAAC,OAAO,CAAC,wBAAwB;oBACpC,WAAW;oBACX,iBAAiB;oBACjB,cAAc;oBACd,cAAc;oBACd,WAAW;gBACb;gBAEA;YACF,OAAO;gBACL,MAAM,YAAY,MAAM,SAAS,IAAI;gBACrC,mJAAA,CAAA,QAAK,CAAC,KAAK,CAAC,UAAU,OAAO,IAAI,mBAAmB;oBAClD,WAAW;oBACX,iBAAiB;oBACjB,cAAc;oBACd,cAAc;oBACd,WAAW;gBACb;YACF;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,uBAAuB;YACrC,mJAAA,CAAA,QAAK,CAAC,KAAK,CAAC,mBAAmB;gBAC7B,WAAW;gBACX,iBAAiB;gBACjB,cAAc;gBACd,cAAc;gBACd,WAAW;YACb;QACF,SAAU;YACR,iBAAiB;gBAAE,GAAG,aAAa;gBAAE,CAAC,aAAa,EAAE;YAAM;QAC7D;IACF;IAEA,IAAI,SAAS;QACX,qBACE,8OAAC;YAAI,WAAU;sBACb,cAAA,8OAAC;gBAAI,WAAU;;kCACb,8OAAC,iNAAA,CAAA,UAAO;wBAAC,WAAU;;;;;;kCACnB,8OAAC;wBAAE,WAAU;kCAAqB;;;;;;;;;;;;;;;;;IAI1C;IAEA,qBACE,8OAAC;QAAI,WAAU;;0BAEb,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;;0CACC,8OAAC;gCAAG,WAAU;0CAAsC;;;;;;0CACpD,8OAAC;gCAAE,WAAU;0CAAwB;;;;;;;;;;;;kCAEvC,8OAAC;wBACC,SAAS,IAAM,YAAY,CAAC;wBAC5B,WAAU;;0CAEV,8OAAC,kMAAA,CAAA,OAAI;gCAAC,WAAU;;;;;;4BAAiB;;;;;;;;;;;;;YAMpC,0BACC,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAG,WAAU;8CAAsC;;;;;;8CAGpD,8OAAC;oCACC,SAAS,IAAM,YAAY;oCAC3B,WAAU;8CAEV,cAAA,8OAAC,4LAAA,CAAA,IAAC;wCAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;kCAKnB,8OAAC;wBAAK,UAAU;wBAAc,WAAU;;0CAEtC,8OAAC;;kDACC,8OAAC;wCAAM,WAAU;;4CAA+C;0DACnD,8OAAC;gDAAK,WAAU;0DAAe;;;;;;;;;;;;oCAE3C,aAAa,MAAM,KAAK,kBACvB,8OAAC;wCAAI,WAAU;kDACb,cAAA,8OAAC;4CAAE,WAAU;sDAA0B;;;;;;;;;;6DAMzC,8OAAC;wCAAI,WAAU;kDACZ,aAAa,GAAG,CAAC,CAAC,sBACjB,8OAAC;gDAEC,WAAU;;kEAEV,8OAAC;wDACC,MAAK;wDACL,MAAK;wDACL,OAAO,MAAM,EAAE;wDACf,SAAS,SAAS,QAAQ,KAAK,MAAM,EAAE;wDACvC,UAAU,CAAC,IACT,YAAY;gEAAE,GAAG,QAAQ;gEAAE,UAAU,EAAE,MAAM,CAAC,KAAK;4DAAC;wDAEtD,WAAU;;;;;;kEAEZ,8OAAC;;0EACC,8OAAC;gEAAE,WAAU;0EACV,MAAM,IAAI;;;;;;0EAEb,8OAAC;gEAAE,WAAU;;oEAAwB;oEAC5B,MAAM,KAAK;oEAAC;oEAAa,MAAM,KAAK;;;;;;;;;;;;;;+CAlB1C,MAAM,EAAE;;;;;;;;;;oCAyBpB,OAAO,QAAQ,kBACd,8OAAC;wCAAE,WAAU;kDAA6B,OAAO,QAAQ;;;;;;;;;;;;0CAK7D,8OAAC,qIAAA,CAAA,UAAK;gCACJ,IAAG;gCACH,OAAM;gCACN,OAAO,SAAS,aAAa;gCAC7B,UAAU,CAAC,IACT,YAAY;wCAAE,GAAG,QAAQ;wCAAE,eAAe,EAAE,MAAM,CAAC,KAAK;oCAAC;gCAE3D,OAAO,CAAC,CAAC,OAAO,aAAa;gCAC7B,cAAc,OAAO,aAAa;gCAClC,aAAY;gCACZ,QAAQ;;;;;;0CAIV,8OAAC;;kDACC,8OAAC;wCAAM,WAAU;;4CAA+C;0DAC5C,8OAAC;gDAAK,WAAU;0DAAe;;;;;;;;;;;;kDAEnD,8OAAC;wCACE,GAAG,cAAc;wCAClB,WAAW,CAAC,gCAAgC,EAC1C,OAAO,QAAQ,GAAG,mBAAmB,kBACtC,kLAAkL,CAAC;;0DAEpL,8OAAC;gDAAO,GAAG,eAAe;;;;;;4CACzB,2BACC,8OAAC;gDAAI,WAAU;;kEACb,8OAAC,6HAAA,CAAA,UAAK;wDACJ,KAAK;wDACL,KAAI;wDACJ,OAAO;wDACP,QAAQ;wDACR,WAAU;;;;;;kEAEZ,8OAAC;wDACC,MAAK;wDACL,SAAS,CAAC;4DACR,EAAE,eAAe;4DACjB;wDACF;wDACA,WAAU;kEAEV,cAAA,8OAAC,4LAAA,CAAA,IAAC;4DAAC,WAAU;;;;;;;;;;;;;;;;qEAIjB,8OAAC;gDAAI,WAAU;;kEACb,8OAAC,oMAAA,CAAA,QAAS;wDAAC,WAAU;;;;;;kEACrB,8OAAC;;0EACC,8OAAC;gEAAE,WAAU;0EAA4B;;;;;;0EAGzC,8OAAC;gEAAE,WAAU;0EAA6B;;;;;;;;;;;;;;;;;;;;;;;;oCAOjD,OAAO,QAAQ,kBACd,8OAAC;wCAAE,WAAU;kDAA6B,OAAO,QAAQ;;;;;;;;;;;;0CAK7D,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCACC,MAAK;wCACL,SAAS,IAAM,YAAY;wCAC3B,WAAU;kDACX;;;;;;kDAGD,8OAAC;wCACC,MAAK;wCACL,UAAU,iBAAiB,aAAa,MAAM,KAAK;wCACnD,WAAU;kDAET,8BACC;;8DACE,8OAAC,iNAAA,CAAA,UAAO;oDAAC,WAAU;;;;;;gDAA8B;;yEAInD;;8DACE,8OAAC,sMAAA,CAAA,SAAM;oDAAC,WAAU;;;;;;gDAAiB;;;;;;;;;;;;;;;;;;;;;;;;;;0BAWjD,8OAAC;gBAAI,WAAU;0BACZ,YAAY,MAAM,KAAK,kBACtB,8OAAC;oBAAI,WAAU;;sCACb,8OAAC,sMAAA,CAAA,SAAM;4BAAC,WAAU;;;;;;sCAClB,8OAAC;4BAAE,WAAU;sCAAgB;;;;;;sCAC7B,8OAAC;4BAAE,WAAU;sCAAwB;;;;;;;;;;;2BAKvC,YAAY,GAAG,CAAC,CAAC,2BACf,8OAAC;wBAEC,WAAU;;0CAEV,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAI,WAAU;;kEACb,8OAAC,sMAAA,CAAA,SAAM;wDAAC,WAAU;;;;;;kEAClB,8OAAC;wDAAG,WAAU;kEACX,WAAW,aAAa;;;;;;kEAE3B,8OAAC;wDACC,WAAW,CAAC,oCAAoC,EAC9C,WAAW,eAAe,KAAK,aAC3B,gCACA,WAAW,eAAe,KAAK,YAC/B,kCACA,2BACJ;kEAED,WAAW,eAAe,KAAK,aAC5B,eACA,WAAW,eAAe,KAAK,YAC/B,iBACA;;;;;;;;;;;;0DAGR,8OAAC;gDAAE,WAAU;;oDAA6B;oDACjC;kEACP,8OAAC;wDAAK,WAAU;kEAAe,WAAW,UAAU;;;;;;;;;;;;0DAEtD,8OAAC;gDAAE,WAAU;;oDAA6B;oDACzB;oDACd,IAAI,KAAK,WAAW,UAAU,EAAE,kBAAkB;;;;;;;0DAErD,8OAAC;gDAAI,WAAU;0DACb,cAAA,8OAAC;oDAAE,WAAU;;wDAAoC;wDACtC;sEACT,8OAAC;4DAAK,WAAU;sEACb,WAAW,GAAG;;;;;;;;;;;;;;;;;;;;;;;kDAMvB,8OAAC;wCAAI,WAAU;;4CACZ,WAAW,QAAQ,kBAClB,8OAAC,6HAAA,CAAA,UAAK;gDACJ,KAAK,WAAW,QAAQ;gDACxB,KAAI;gDACJ,OAAO;gDACP,QAAQ;gDACR,WAAU;;;;;;0DAKd,8OAAC;gDACC,SAAS;oDACP,sBAAsB;oDACtB,mBAAmB;gDACrB;gDACA,WAAU;0DAEV,cAAA,8OAAC,0MAAA,CAAA,SAAM;oDAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;4BAIvB,mBAAmB,oCAClB,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAG,WAAU;sDAA2C;;;;;;sDAGzD,8OAAC;4CAAE,WAAU;;gDAA6B;gDACH;8DACrC,8OAAC;oDAAK,WAAU;8DACb,mBAAmB,aAAa;;;;;;gDAC5B;;;;;;;sDAGT,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDACC,WAAU;oDACV,SAAS;wDACP,mBAAmB;wDACnB,sBAAsB;oDACxB;8DACD;;;;;;8DAGD,8OAAC;oDACC,WAAU;oDACV,SAAS;wDACP,IAAI,oBAAoB;4DACtB,uBACE,mBAAmB,EAAE,EACrB,mBAAmB,aAAa;4DAElC,mBAAmB;4DACnB,sBAAsB;wDACxB;oDACF;8DACD;;;;;;;;;;;;;;;;;;;;;;;;uBAtGJ,WAAW,EAAE;;;;;;;;;;;;;;;;AAmHhC;uCAEe", "debugId": null}}, {"offset": {"line": 4443, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/IMPORTANT/work/export/front/src/app/account/page.tsx"], "sourcesContent": ["\"use client\";\n\nimport React, { useState, useEffect } from \"react\";\nimport { useRouter } from \"next/navigation\";\nimport { toast } from \"react-toastify\";\nimport useTranslation from \"@/hooks/useTranslation\";\n\n// Import components\nimport Sidebar from \"./components/Sidebar\";\nimport ProfileForm from \"./components/ProfileForm\";\nimport PasswordForm from \"./components/PasswordForm\";\nimport PermissionsForm from \"./components/PermissionsForm\";\n\n// Import services and utilities\nimport {\n  getUserProfile,\n  deleteAccount,\n  logout,\n  changePassword,\n} from \"@/services/authService\";\nimport {\n  extractPhoneNumber,\n  getCountryCodeFromName,\n} from \"@/utils/accountUtils\";\nimport {\n  isEmailVerificationError,\n  triggerEmailVerificationPopup,\n} from \"@/utils/navigationUtils\";\nimport { ProfileFormData, User } from \"@/types/account\";\nimport { ChildData } from \"@/types/auth\";\n\n// Extended User interface to handle both phone and phone_number fields\ninterface ExtendedUser extends Omit<User, \"children\"> {\n  phone: string;\n  phone_number?: string;\n  children: ChildData[];\n}\n\nconst AccountPage = () => {\n  const router = useRouter();\n  const { t } = useTranslation();\n\n  // Get the last active tab from localStorage or default to \"profile\"\n  const [activeTab, setActiveTab] = useState(() => {\n    return localStorage.getItem(\"userAccountActiveTab\") ?? \"profile\";\n  });\n\n  // User state\n  const [user, setUser] = useState<ExtendedUser | null>(null);\n  const [loading, setLoading] = useState(true);\n  const [error, setError] = useState<string | null>(null);\n  const [isAdmin, setIsAdmin] = useState(false);\n  const [formData, setFormData] = useState<ProfileFormData>({\n    firstName: \"\",\n    lastName: \"\",\n    email: \"\",\n    phoneCountry: \"EG\",\n    phone: \"\",\n    countryCode: \"EG\",\n    governorate: \"\",\n    city: \"\",\n    relation: \"\",\n    otherRelation: \"\",\n    children: [],\n  });\n\n  // Delete account state\n  const [deletePassword, setDeletePassword] = useState<string>(\"\");\n  const [updateLoading, setUpdateLoading] = useState(false);\n\n  // Fetch user profile on component mount\n  useEffect(() => {\n    fetchUserProfile();\n    checkAdminStatus();\n  }, []);\n\n  // Check admin status\n  const checkAdminStatus = async () => {\n    try {\n      const response = await fetch(\n        \"http://localhost:8000/api/admin/check-status/\",\n        {\n          headers: {\n            Authorization: `Bearer ${localStorage.getItem(\"access_token\")}`,\n          },\n        }\n      );\n\n      if (response.ok) {\n        const data = await response.json();\n        setIsAdmin(data.is_admin);\n      }\n    } catch (error) {\n      console.error(\"Error checking admin status:\", error);\n    }\n  };\n\n  // Save active tab to localStorage when changed\n  useEffect(() => {\n    if (typeof window !== \"undefined\") {\n      localStorage.setItem(\"userAccountActiveTab\", activeTab);\n    }\n  }, [activeTab]);\n\n  // Fetch user profile from API\n  const fetchUserProfile = async () => {\n    setLoading(true);\n    try {\n      const data = await getUserProfile();\n\n      // Extract phone number without country code if it exists\n      let phoneNumber = \"\";\n      let phoneCountry = \"EG\"; // Default to Egypt\n\n      // Check both phone and phone_number fields\n      const phoneValue = data.phone_number || data.phone;\n\n      if (phoneValue) {\n        const { phoneNumber: extractedPhone, countryCode } =\n          extractPhoneNumber(phoneValue);\n        phoneNumber = extractedPhone;\n        phoneCountry = countryCode;\n        console.log(\n          \"Extracted phone:\",\n          phoneNumber,\n          \"Country:\",\n          phoneCountry,\n          \"From:\",\n          phoneValue\n        );\n      } else {\n        console.warn(\"No phone number found in user data:\", data);\n      }\n\n      // Log the full user data for debugging\n      console.log(\"User data:\", data);\n\n      // Get country code from country name if it exists\n      let countryCode = \"EG\"; // Default to Egypt\n      if (\n        data &&\n        typeof data === \"object\" &&\n        \"country\" in data &&\n        data.country\n      ) {\n        countryCode = getCountryCodeFromName(data.country);\n      }\n\n      // Convert Child[] to ChildData[] by ensuring id is string\n      const children = (data.children || []).map((child) => ({\n        ...child,\n        id: child.id.toString(),\n      }));\n\n      const extendedUser: ExtendedUser = {\n        id: data.id,\n        first_name: data.first_name,\n        last_name: data.last_name,\n        email: data.email,\n        phone: data.phone || \"\",\n        relation: data.relation || \"\",\n        other_relation: data.other_relation,\n        governorate: data.governorate,\n        city: data.city,\n        children,\n        id_image:\n          data.id_image && typeof data.id_image === \"string\"\n            ? data.id_image\n            : undefined,\n        country: data.country,\n        country_code: data.country_code,\n        address: data.address,\n        phone_number: data.phone,\n      };\n\n      setUser(extendedUser);\n\n      // Update profile form with user data\n      const updatedFormData: ProfileFormData = {\n        firstName: data.first_name || \"\",\n        lastName: data.last_name || \"\",\n        email: data.email || \"\",\n        phoneCountry: phoneCountry,\n        phone: phoneNumber,\n        relation: data.relation || \"\",\n        otherRelation: data.other_relation || \"\",\n        children,\n        countryCode: countryCode,\n        governorate: data.governorate || \"\",\n        city: data.city || \"\",\n        idImage: data.id_image || \"\",\n      };\n\n      setFormData(updatedFormData);\n      setLoading(false);\n    } catch (error: unknown) {\n      console.error(\"Error fetching user profile:\", error);\n      setError(\"Failed to load your profile. Please try again later.\");\n      setLoading(false);\n    }\n  };\n\n  // Handle user update from ProfileForm component\n  const handleUserUpdate = (updatedUser: ExtendedUser) => {\n    setUser(updatedUser);\n\n    // Update form data to reflect the changes\n    const updatedFormData: ProfileFormData = {\n      firstName: updatedUser.first_name || \"\",\n      lastName: updatedUser.last_name || \"\",\n      email: updatedUser.email || \"\",\n      phoneCountry: formData.phoneCountry, // Keep existing phone country\n      phone: formData.phone, // Keep existing phone format\n      relation: updatedUser.relation || \"\",\n      otherRelation: updatedUser.other_relation || \"\",\n      children: updatedUser.children || [],\n      countryCode: formData.countryCode, // Keep existing country code\n      governorate: updatedUser.governorate || \"\",\n      city: updatedUser.city || \"\",\n      idImage: updatedUser.id_image || \"\",\n    };\n    setFormData(updatedFormData);\n  };\n\n  // Handle password update\n  const handlePasswordUpdate = async (data: {\n    currentPassword: string;\n    newPassword: string;\n  }) => {\n    setUpdateLoading(true);\n    try {\n      await changePassword(data.currentPassword, data.newPassword);\n\n      setUpdateLoading(false);\n\n      // Show success message with a custom configuration\n      toast.success(t(\"messages.success.passwordChanged\"), {\n        autoClose: 1500, // Close after 1.5 seconds\n        hideProgressBar: true, // Remove timer bar\n        closeOnClick: true,\n        pauseOnHover: false,\n        draggable: true,\n      });\n\n      // Refresh page after 1.5 seconds\n      setTimeout(() => {\n        window.location.reload();\n      }, 1500);\n    } catch (error: unknown) {\n      setUpdateLoading(false);\n      console.error(\"Error updating password:\", error);\n\n      // Check if this is an email verification error\n      if (isEmailVerificationError(error)) {\n        triggerEmailVerificationPopup(error);\n        return;\n      }\n\n      toast.error(\"Failed to update password\");\n    }\n  };\n\n  // Handle delete account dialog\n  const handleDeleteAccount = async (password: FormData) => {\n    setUpdateLoading(true);\n    try {\n      // Extract the password from the FormData\n      const passwordValue = password.get(\"password\") as string;\n\n      // Call the deleteAccount function with the password string\n      await deleteAccount(passwordValue);\n\n      // Logout and redirect to login page\n      toast.success(\"Account deleted successfully\");\n\n      logout();\n      router.push(\"/login\");\n    } catch (error: unknown) {\n      console.error(\"Error deleting account:\", error);\n      toast.error(\"Failed to delete account\");\n      setUpdateLoading(false);\n    }\n  };\n\n  // Prevent form resubmission on page refresh\n  useEffect(() => {\n    const handleBeforeUnload = (e: BeforeUnloadEvent) => {\n      if (updateLoading) {\n        // Cancel the event\n        e.preventDefault();\n        // Chrome requires returnValue to be set\n        e.returnValue = \"\";\n      }\n    };\n\n    window.addEventListener(\"beforeunload\", handleBeforeUnload);\n\n    return () => {\n      window.removeEventListener(\"beforeunload\", handleBeforeUnload);\n    };\n  }, [updateLoading]);\n  // Render content based on active tab\n  const renderContent = () => {\n    switch (activeTab) {\n      case \"profile\":\n        return (\n          <ProfileForm\n            initialFormData={formData}\n            user={user}\n            onUserUpdate={handleUserUpdate}\n          />\n        );\n      case \"password\":\n        return (\n          <div className=\"card-nursery overflow-hidden\">\n            {/* Header */}\n            <div className=\"bg-pink-500 px-6 py-4\">\n              <h3 className=\"text-xl font-bold text-white\">\n                {t(\"account.password.title\")}\n              </h3>\n              <p className=\"text-sm text-pink-100 mt-1\">\n                {t(\"account.password.subtitle\")}\n              </p>\n            </div>\n\n            <div className=\"p-6\">\n              <PasswordForm\n                onSubmit={handlePasswordUpdate}\n                loading={updateLoading}\n              />\n            </div>\n          </div>\n        );\n\n      case \"permissions\":\n        return (\n          <div className=\"card-nursery overflow-hidden\">\n            {/* Header */}\n            <div className=\"bg-pink-500 px-6 py-4\">\n              <h3 className=\"text-xl font-bold text-white\">\n                {t(\"account.permissions.title\")}\n              </h3>\n              <p className=\"text-sm text-purple-100 mt-1\">\n                {t(\"account.permissions.subtitle\")}\n              </p>\n            </div>\n\n            <div className=\"p-6\">\n              <PermissionsForm userChildren={user?.children || []} />\n            </div>\n          </div>\n        );\n\n      case \"settings\":\n        return (\n          <div className=\"card-nursery overflow-hidden\">\n            {/* Header */}\n            <div className=\"bg-pink-500 px-6 py-4\">\n              <h3 className=\"text-xl font-bold text-white\">إعدادات الحساب</h3>\n              <p className=\"text-sm text-pink-100 mt-1\">إدارة تفضيلات حسابك</p>\n            </div>\n\n            <div className=\"p-6\">\n              {/* Admin Panel Link */}\n              {isAdmin && (\n                <div className=\"border border-pink-200 rounded-xl overflow-hidden mb-6\">\n                  <div className=\"bg-pink-50 px-6 py-4 border-b border-pink-200\">\n                    <h4 className=\"text-lg font-semibold text-pink-800\">\n                      لوحة الإدارة\n                    </h4>\n                    <p className=\"text-sm text-pink-600 mt-1\">\n                      الوصول إلى الميزات الإدارية\n                    </p>\n                  </div>\n\n                  <div className=\"p-6\">\n                    <p className=\"text-gray-700 mb-4\">\n                      لديك صلاحيات إدارية. ادخل إلى لوحة الإدارة لإدارة\n                      المستخدمين والتصاريح.\n                    </p>\n                    <button\n                      onClick={() => router.push(\"/admin\")}\n                      className=\"btn-nursery\"\n                    >\n                      الذهاب إلى لوحة الإدارة\n                    </button>\n                  </div>\n                </div>\n              )}\n\n              {/* Delete Account Section */}\n              <div className=\"border border-red-200 rounded-xl overflow-hidden\">\n                <div className=\"bg-red-50 px-6 py-4 border-b border-red-200\">\n                  <h4 className=\"text-lg font-semibold text-red-800\">\n                    منطقة الخطر\n                  </h4>\n                  <p className=\"text-sm text-red-600 mt-1\">\n                    إجراءات لا يمكن التراجع عنها تؤثر على حسابك\n                  </p>\n                </div>\n\n                <div className=\"p-6\">\n                  <h5 className=\"text-lg font-semibold text-red-700 mb-3\">\n                    حذف الحساب\n                  </h5>\n                  <div className=\"bg-red-50 border border-red-200 rounded-lg p-4 mb-4\">\n                    <p className=\"text-red-700 text-sm font-medium\">\n                      ⚠️ تحذير: هذا الإجراء لا يمكن التراجع عنه. سيتم حذف جميع\n                      بياناتك بشكل دائم.\n                    </p>\n                  </div>\n\n                  <div className=\"space-y-4\">\n                    <input\n                      type=\"password\"\n                      value={deletePassword}\n                      onChange={(e) => setDeletePassword(e.target.value)}\n                      placeholder=\"أدخل كلمة المرور لتأكيد الحذف\"\n                      className=\"w-full py-3 px-4 border border-gray-300 rounded-lg shadow-sm focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500 focus:border-red-500\"\n                    />\n                    <button\n                      onClick={() => {\n                        const formData = new FormData();\n                        formData.append(\"password\", deletePassword);\n                        handleDeleteAccount(formData);\n                      }}\n                      disabled={!deletePassword || updateLoading}\n                      className=\"w-full py-3 px-4 border border-transparent rounded-lg shadow-sm text-base font-semibold text-white bg-gradient-to-r from-red-600 to-red-700 hover:from-red-700 hover:to-red-800 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500 disabled:opacity-50 disabled:cursor-not-allowed transition-all duration-200\"\n                    >\n                      {updateLoading ? \"جاري حذف الحساب...\" : \"حذف الحساب\"}\n                    </button>\n                  </div>\n                </div>\n              </div>\n            </div>\n          </div>\n        );\n\n      default:\n        return null;\n    }\n  };\n\n  // Navigation handler for sidebar\n  const handleNavigation = (tab: string) => {\n    setActiveTab(tab);\n  };\n\n  return (\n    <div className=\"min-h-screen py-8 px-4 sm:px-6 lg:px-8\">\n      <div className=\"max-w-7xl mx-auto\">\n        {/* Page Header */}\n        <div className=\"text-center mb-8\">\n          <h1 className=\"text-3xl md:text-4xl font-bold text-pink-600 mb-2\">\n            إعدادات الحساب\n          </h1>\n          <p className=\"text-gray-600 text-lg\">\n            إدارة ملفك الشخصي ومعلومات أطفالك\n          </p>\n        </div>\n\n        <div className=\"flex flex-col lg:flex-row gap-8\">\n          {/* Sidebar */}\n          <div className=\"lg:w-80 shrink-0\">\n            <div className=\"card-nursery overflow-hidden\">\n              <Sidebar activeTab={activeTab} onTabChange={handleNavigation} />\n            </div>\n          </div>\n\n          {/* Main Content */}\n          <div className=\"flex-1\">\n            {loading ? (\n              <div className=\"card-nursery p-8\">\n                <div className=\"flex items-center justify-center h-64\">\n                  <div className=\"text-center\">\n                    <div className=\"spinner-nursery mx-auto mb-4\"></div>\n                    <p className=\"text-gray-600\">{t(\"common.loading\")}</p>\n                  </div>\n                </div>\n              </div>\n            ) : error ? (\n              <div className=\"card-nursery p-8\">\n                <div className=\"bg-red-50 border border-red-200 rounded-lg p-4\">\n                  <div className=\"text-red-700 font-medium\">{error}</div>\n                </div>\n              </div>\n            ) : (\n              renderContent()\n            )}\n          </div>\n        </div>\n      </div>\n    </div>\n  );\n};\n\nexport default AccountPage;\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AAEA,oBAAoB;AACpB;AACA;AACA;AACA;AAEA,gCAAgC;AAChC;AAMA;AAIA;AAxBA;;;;;;;;;;;;;AAsCA,MAAM,cAAc;IAClB,MAAM,SAAS,CAAA,GAAA,kIAAA,CAAA,YAAS,AAAD;IACvB,MAAM,EAAE,CAAC,EAAE,GAAG,CAAA,GAAA,8HAAA,CAAA,UAAc,AAAD;IAE3B,oEAAoE;IACpE,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;QACzC,OAAO,aAAa,OAAO,CAAC,2BAA2B;IACzD;IAEA,aAAa;IACb,MAAM,CAAC,MAAM,QAAQ,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAuB;IACtD,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAiB;IAClD,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAmB;QACxD,WAAW;QACX,UAAU;QACV,OAAO;QACP,cAAc;QACd,OAAO;QACP,aAAa;QACb,aAAa;QACb,MAAM;QACN,UAAU;QACV,eAAe;QACf,UAAU,EAAE;IACd;IAEA,uBAAuB;IACvB,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAU;IAC7D,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAEnD,wCAAwC;IACxC,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR;QACA;IACF,GAAG,EAAE;IAEL,qBAAqB;IACrB,MAAM,mBAAmB;QACvB,IAAI;YACF,MAAM,WAAW,MAAM,MACrB,iDACA;gBACE,SAAS;oBACP,eAAe,CAAC,OAAO,EAAE,aAAa,OAAO,CAAC,iBAAiB;gBACjE;YACF;YAGF,IAAI,SAAS,EAAE,EAAE;gBACf,MAAM,OAAO,MAAM,SAAS,IAAI;gBAChC,WAAW,KAAK,QAAQ;YAC1B;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,gCAAgC;QAChD;IACF;IAEA,+CAA+C;IAC/C,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,uCAAmC;;QAEnC;IACF,GAAG;QAAC;KAAU;IAEd,8BAA8B;IAC9B,MAAM,mBAAmB;QACvB,WAAW;QACX,IAAI;YACF,MAAM,OAAO,MAAM,CAAA,GAAA,8HAAA,CAAA,iBAAc,AAAD;YAEhC,yDAAyD;YACzD,IAAI,cAAc;YAClB,IAAI,eAAe,MAAM,mBAAmB;YAE5C,2CAA2C;YAC3C,MAAM,aAAa,KAAK,YAAY,IAAI,KAAK,KAAK;YAElD,IAAI,YAAY;gBACd,MAAM,EAAE,aAAa,cAAc,EAAE,WAAW,EAAE,GAChD,CAAA,GAAA,4HAAA,CAAA,qBAAkB,AAAD,EAAE;gBACrB,cAAc;gBACd,eAAe;gBACf,QAAQ,GAAG,CACT,oBACA,aACA,YACA,cACA,SACA;YAEJ,OAAO;gBACL,QAAQ,IAAI,CAAC,uCAAuC;YACtD;YAEA,uCAAuC;YACvC,QAAQ,GAAG,CAAC,cAAc;YAE1B,kDAAkD;YAClD,IAAI,cAAc,MAAM,mBAAmB;YAC3C,IACE,QACA,OAAO,SAAS,YAChB,aAAa,QACb,KAAK,OAAO,EACZ;gBACA,cAAc,CAAA,GAAA,4HAAA,CAAA,yBAAsB,AAAD,EAAE,KAAK,OAAO;YACnD;YAEA,0DAA0D;YAC1D,MAAM,WAAW,CAAC,KAAK,QAAQ,IAAI,EAAE,EAAE,GAAG,CAAC,CAAC,QAAU,CAAC;oBACrD,GAAG,KAAK;oBACR,IAAI,MAAM,EAAE,CAAC,QAAQ;gBACvB,CAAC;YAED,MAAM,eAA6B;gBACjC,IAAI,KAAK,EAAE;gBACX,YAAY,KAAK,UAAU;gBAC3B,WAAW,KAAK,SAAS;gBACzB,OAAO,KAAK,KAAK;gBACjB,OAAO,KAAK,KAAK,IAAI;gBACrB,UAAU,KAAK,QAAQ,IAAI;gBAC3B,gBAAgB,KAAK,cAAc;gBACnC,aAAa,KAAK,WAAW;gBAC7B,MAAM,KAAK,IAAI;gBACf;gBACA,UACE,KAAK,QAAQ,IAAI,OAAO,KAAK,QAAQ,KAAK,WACtC,KAAK,QAAQ,GACb;gBACN,SAAS,KAAK,OAAO;gBACrB,cAAc,KAAK,YAAY;gBAC/B,SAAS,KAAK,OAAO;gBACrB,cAAc,KAAK,KAAK;YAC1B;YAEA,QAAQ;YAER,qCAAqC;YACrC,MAAM,kBAAmC;gBACvC,WAAW,KAAK,UAAU,IAAI;gBAC9B,UAAU,KAAK,SAAS,IAAI;gBAC5B,OAAO,KAAK,KAAK,IAAI;gBACrB,cAAc;gBACd,OAAO;gBACP,UAAU,KAAK,QAAQ,IAAI;gBAC3B,eAAe,KAAK,cAAc,IAAI;gBACtC;gBACA,aAAa;gBACb,aAAa,KAAK,WAAW,IAAI;gBACjC,MAAM,KAAK,IAAI,IAAI;gBACnB,SAAS,KAAK,QAAQ,IAAI;YAC5B;YAEA,YAAY;YACZ,WAAW;QACb,EAAE,OAAO,OAAgB;YACvB,QAAQ,KAAK,CAAC,gCAAgC;YAC9C,SAAS;YACT,WAAW;QACb;IACF;IAEA,gDAAgD;IAChD,MAAM,mBAAmB,CAAC;QACxB,QAAQ;QAER,0CAA0C;QAC1C,MAAM,kBAAmC;YACvC,WAAW,YAAY,UAAU,IAAI;YACrC,UAAU,YAAY,SAAS,IAAI;YACnC,OAAO,YAAY,KAAK,IAAI;YAC5B,cAAc,SAAS,YAAY;YACnC,OAAO,SAAS,KAAK;YACrB,UAAU,YAAY,QAAQ,IAAI;YAClC,eAAe,YAAY,cAAc,IAAI;YAC7C,UAAU,YAAY,QAAQ,IAAI,EAAE;YACpC,aAAa,SAAS,WAAW;YACjC,aAAa,YAAY,WAAW,IAAI;YACxC,MAAM,YAAY,IAAI,IAAI;YAC1B,SAAS,YAAY,QAAQ,IAAI;QACnC;QACA,YAAY;IACd;IAEA,yBAAyB;IACzB,MAAM,uBAAuB,OAAO;QAIlC,iBAAiB;QACjB,IAAI;YACF,MAAM,CAAA,GAAA,8HAAA,CAAA,iBAAc,AAAD,EAAE,KAAK,eAAe,EAAE,KAAK,WAAW;YAE3D,iBAAiB;YAEjB,mDAAmD;YACnD,mJAAA,CAAA,QAAK,CAAC,OAAO,CAAC,EAAE,qCAAqC;gBACnD,WAAW;gBACX,iBAAiB;gBACjB,cAAc;gBACd,cAAc;gBACd,WAAW;YACb;YAEA,iCAAiC;YACjC,WAAW;gBACT,OAAO,QAAQ,CAAC,MAAM;YACxB,GAAG;QACL,EAAE,OAAO,OAAgB;YACvB,iBAAiB;YACjB,QAAQ,KAAK,CAAC,4BAA4B;YAE1C,+CAA+C;YAC/C,IAAI,CAAA,GAAA,+HAAA,CAAA,2BAAwB,AAAD,EAAE,QAAQ;gBACnC,CAAA,GAAA,+HAAA,CAAA,gCAA6B,AAAD,EAAE;gBAC9B;YACF;YAEA,mJAAA,CAAA,QAAK,CAAC,KAAK,CAAC;QACd;IACF;IAEA,+BAA+B;IAC/B,MAAM,sBAAsB,OAAO;QACjC,iBAAiB;QACjB,IAAI;YACF,yCAAyC;YACzC,MAAM,gBAAgB,SAAS,GAAG,CAAC;YAEnC,2DAA2D;YAC3D,MAAM,CAAA,GAAA,8HAAA,CAAA,gBAAa,AAAD,EAAE;YAEpB,oCAAoC;YACpC,mJAAA,CAAA,QAAK,CAAC,OAAO,CAAC;YAEd,CAAA,GAAA,8HAAA,CAAA,SAAM,AAAD;YACL,OAAO,IAAI,CAAC;QACd,EAAE,OAAO,OAAgB;YACvB,QAAQ,KAAK,CAAC,2BAA2B;YACzC,mJAAA,CAAA,QAAK,CAAC,KAAK,CAAC;YACZ,iBAAiB;QACnB;IACF;IAEA,4CAA4C;IAC5C,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,MAAM,qBAAqB,CAAC;YAC1B,IAAI,eAAe;gBACjB,mBAAmB;gBACnB,EAAE,cAAc;gBAChB,wCAAwC;gBACxC,EAAE,WAAW,GAAG;YAClB;QACF;QAEA,OAAO,gBAAgB,CAAC,gBAAgB;QAExC,OAAO;YACL,OAAO,mBAAmB,CAAC,gBAAgB;QAC7C;IACF,GAAG;QAAC;KAAc;IAClB,qCAAqC;IACrC,MAAM,gBAAgB;QACpB,OAAQ;YACN,KAAK;gBACH,qBACE,8OAAC,mJAAA,CAAA,UAAW;oBACV,iBAAiB;oBACjB,MAAM;oBACN,cAAc;;;;;;YAGpB,KAAK;gBACH,qBACE,8OAAC;oBAAI,WAAU;;sCAEb,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAG,WAAU;8CACX,EAAE;;;;;;8CAEL,8OAAC;oCAAE,WAAU;8CACV,EAAE;;;;;;;;;;;;sCAIP,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC,oJAAA,CAAA,UAAY;gCACX,UAAU;gCACV,SAAS;;;;;;;;;;;;;;;;;YAMnB,KAAK;gBACH,qBACE,8OAAC;oBAAI,WAAU;;sCAEb,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAG,WAAU;8CACX,EAAE;;;;;;8CAEL,8OAAC;oCAAE,WAAU;8CACV,EAAE;;;;;;;;;;;;sCAIP,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC,uJAAA,CAAA,UAAe;gCAAC,cAAc,MAAM,YAAY,EAAE;;;;;;;;;;;;;;;;;YAK3D,KAAK;gBACH,qBACE,8OAAC;oBAAI,WAAU;;sCAEb,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAG,WAAU;8CAA+B;;;;;;8CAC7C,8OAAC;oCAAE,WAAU;8CAA6B;;;;;;;;;;;;sCAG5C,8OAAC;4BAAI,WAAU;;gCAEZ,yBACC,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAG,WAAU;8DAAsC;;;;;;8DAGpD,8OAAC;oDAAE,WAAU;8DAA6B;;;;;;;;;;;;sDAK5C,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAE,WAAU;8DAAqB;;;;;;8DAIlC,8OAAC;oDACC,SAAS,IAAM,OAAO,IAAI,CAAC;oDAC3B,WAAU;8DACX;;;;;;;;;;;;;;;;;;8CAQP,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAG,WAAU;8DAAqC;;;;;;8DAGnD,8OAAC;oDAAE,WAAU;8DAA4B;;;;;;;;;;;;sDAK3C,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAG,WAAU;8DAA0C;;;;;;8DAGxD,8OAAC;oDAAI,WAAU;8DACb,cAAA,8OAAC;wDAAE,WAAU;kEAAmC;;;;;;;;;;;8DAMlD,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DACC,MAAK;4DACL,OAAO;4DACP,UAAU,CAAC,IAAM,kBAAkB,EAAE,MAAM,CAAC,KAAK;4DACjD,aAAY;4DACZ,WAAU;;;;;;sEAEZ,8OAAC;4DACC,SAAS;gEACP,MAAM,WAAW,IAAI;gEACrB,SAAS,MAAM,CAAC,YAAY;gEAC5B,oBAAoB;4DACtB;4DACA,UAAU,CAAC,kBAAkB;4DAC7B,WAAU;sEAET,gBAAgB,uBAAuB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;YASxD;gBACE,OAAO;QACX;IACF;IAEA,iCAAiC;IACjC,MAAM,mBAAmB,CAAC;QACxB,aAAa;IACf;IAEA,qBACE,8OAAC;QAAI,WAAU;kBACb,cAAA,8OAAC;YAAI,WAAU;;8BAEb,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAG,WAAU;sCAAoD;;;;;;sCAGlE,8OAAC;4BAAE,WAAU;sCAAwB;;;;;;;;;;;;8BAKvC,8OAAC;oBAAI,WAAU;;sCAEb,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC,+IAAA,CAAA,UAAO;oCAAC,WAAW;oCAAW,aAAa;;;;;;;;;;;;;;;;sCAKhD,8OAAC;4BAAI,WAAU;sCACZ,wBACC,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC;oCAAI,WAAU;8CACb,cAAA,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAI,WAAU;;;;;;0DACf,8OAAC;gDAAE,WAAU;0DAAiB,EAAE;;;;;;;;;;;;;;;;;;;;;uCAIpC,sBACF,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC;oCAAI,WAAU;8CACb,cAAA,8OAAC;wCAAI,WAAU;kDAA4B;;;;;;;;;;;;;;;uCAI/C;;;;;;;;;;;;;;;;;;;;;;;AAOd;uCAEe", "debugId": null}}]}
{"version": 3, "sources": [], "sections": [{"offset": {"line": 15, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/IMPORTANT/work/export/front/src/components/Providers.tsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport { ToastContainer, Slide } from \"react-toastify\";\r\nimport \"react-toastify/dist/ReactToastify.css\";\r\n\r\nexport function Providers({ children }: { children: React.ReactNode }) {\r\n  return (\r\n    <>\r\n      <ToastContainer\r\n        position=\"top-center\"\r\n        autoClose={3000}\r\n        hideProgressBar={true}\r\n        newestOnTop\r\n        closeOnClick\r\n        rtl={false}\r\n        pauseOnFocusLoss={false}\r\n        draggable={false}\r\n        pauseOnHover\r\n        theme=\"light\"\r\n        transition={Slide}\r\n        className=\"custom-toast-container\"\r\n        toastClassName=\"custom-toast\"\r\n        closeButton={({ closeToast }) => (\r\n          <button\r\n            onClick={closeToast}\r\n            className=\"flex items-center justify-center text-lg ml-2\"\r\n          >\r\n            ×\r\n          </button>\r\n        )}\r\n      />\r\n      {children}\r\n    </>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;AAEA;AAFA;;;;AAKO,SAAS,UAAU,EAAE,QAAQ,EAAiC;IACnE,qBACE;;0BACE,8OAAC,mJAAA,CAAA,iBAAc;gBACb,UAAS;gBACT,WAAW;gBACX,iBAAiB;gBACjB,WAAW;gBACX,YAAY;gBACZ,KAAK;gBACL,kBAAkB;gBAClB,WAAW;gBACX,YAAY;gBACZ,OAAM;gBACN,YAAY,mJAAA,CAAA,QAAK;gBACjB,WAAU;gBACV,gBAAe;gBACf,aAAa,CAAC,EAAE,UAAU,EAAE,iBAC1B,8OAAC;wBACC,SAAS;wBACT,WAAU;kCACX;;;;;;;;;;;YAKJ;;;AAGP", "debugId": null}}, {"offset": {"line": 89, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/IMPORTANT/work/export/front/src/components/EmailVerificationPopup.tsx"], "sourcesContent": ["\"use client\";\n\nimport React from \"react\";\nimport { useRouter } from \"next/navigation\";\nimport { AlertCircle, X } from \"lucide-react\";\n\ninterface EmailVerificationPopupProps {\n  isOpen: boolean;\n  onClose: () => void;\n  email: string;\n  message: string;\n}\n\nconst EmailVerificationPopup: React.FC<EmailVerificationPopupProps> = ({\n  isOpen,\n  onClose,\n  email,\n  message,\n}) => {\n  const router = useRouter();\n\n  const handleGoToActivation = () => {\n    // Store the email in localStorage for the activation page\n    if (email) {\n      localStorage.setItem(\"pendingActivationEmail\", email);\n    }\n    \n    // Store the message for display on the activation page\n    if (message) {\n      localStorage.setItem(\"emailVerificationMessage\", message);\n    }\n    \n    // Close the popup\n    onClose();\n    \n    // Navigate to the activation page\n    router.push(\"/activate-account\");\n  };\n\n  if (!isOpen) return null;\n\n  return (\n    <div className=\"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50\">\n      <div className=\"bg-white rounded-lg p-6 max-w-md w-full mx-4 shadow-xl\">\n        <div className=\"flex items-start justify-between mb-4\">\n          <div className=\"flex items-center\">\n            <AlertCircle className=\"h-6 w-6 text-yellow-500 mr-3\" />\n            <h3 className=\"text-lg font-semibold text-gray-900\">\n              Email Verification Required\n            </h3>\n          </div>\n          <button\n            onClick={onClose}\n            className=\"text-gray-400 hover:text-gray-600\"\n          >\n            <X className=\"h-5 w-5\" />\n          </button>\n        </div>\n        \n        <div className=\"mb-6\">\n          <p className=\"text-gray-700 mb-2\">{message}</p>\n          {email && (\n            <p className=\"text-sm text-gray-500\">\n              Email: <span className=\"font-medium\">{email}</span>\n            </p>\n          )}\n        </div>\n        \n        <div className=\"flex space-x-3\">\n          <button\n            onClick={handleGoToActivation}\n            className=\"flex-1 bg-black text-white py-2 px-4 rounded-md hover:bg-gray-800 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-black\"\n          >\n            Verify Email\n          </button>\n          <button\n            onClick={onClose}\n            className=\"flex-1 bg-gray-200 text-gray-800 py-2 px-4 rounded-md hover:bg-gray-300 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-gray-500\"\n          >\n            Later\n          </button>\n        </div>\n      </div>\n    </div>\n  );\n};\n\nexport default EmailVerificationPopup;\n"], "names": [], "mappings": ";;;;AAGA;AACA;AAAA;AAJA;;;;AAaA,MAAM,yBAAgE,CAAC,EACrE,MAAM,EACN,OAAO,EACP,KAAK,EACL,OAAO,EACR;IACC,MAAM,SAAS,CAAA,GAAA,kIAAA,CAAA,YAAS,AAAD;IAEvB,MAAM,uBAAuB;QAC3B,0DAA0D;QAC1D,IAAI,OAAO;YACT,aAAa,OAAO,CAAC,0BAA0B;QACjD;QAEA,uDAAuD;QACvD,IAAI,SAAS;YACX,aAAa,OAAO,CAAC,4BAA4B;QACnD;QAEA,kBAAkB;QAClB;QAEA,kCAAkC;QAClC,OAAO,IAAI,CAAC;IACd;IAEA,IAAI,CAAC,QAAQ,OAAO;IAEpB,qBACE,8OAAC;QAAI,WAAU;kBACb,cAAA,8OAAC;YAAI,WAAU;;8BACb,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAI,WAAU;;8CACb,8OAAC,oNAAA,CAAA,cAAW;oCAAC,WAAU;;;;;;8CACvB,8OAAC;oCAAG,WAAU;8CAAsC;;;;;;;;;;;;sCAItD,8OAAC;4BACC,SAAS;4BACT,WAAU;sCAEV,cAAA,8OAAC,4LAAA,CAAA,IAAC;gCAAC,WAAU;;;;;;;;;;;;;;;;;8BAIjB,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAE,WAAU;sCAAsB;;;;;;wBAClC,uBACC,8OAAC;4BAAE,WAAU;;gCAAwB;8CAC5B,8OAAC;oCAAK,WAAU;8CAAe;;;;;;;;;;;;;;;;;;8BAK5C,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BACC,SAAS;4BACT,WAAU;sCACX;;;;;;sCAGD,8OAAC;4BACC,SAAS;4BACT,WAAU;sCACX;;;;;;;;;;;;;;;;;;;;;;;AAOX;uCAEe", "debugId": null}}, {"offset": {"line": 251, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/IMPORTANT/work/export/front/src/hooks/useEmailVerificationHandler.ts"], "sourcesContent": ["\"use client\";\n\nimport { useState, useCallback } from \"react\";\n\ninterface EmailVerificationError {\n  email?: string;\n  message?: string;\n  code?: string;\n  email_verified?: boolean | string;\n  email_not_verified?: string;\n}\n\ninterface EmailVerificationState {\n  isPopupOpen: boolean;\n  email: string;\n  message: string;\n}\n\nexport const useEmailVerificationHandler = () => {\n  const [verificationState, setVerificationState] = useState<EmailVerificationState>({\n    isPopupOpen: false,\n    email: \"\",\n    message: \"\",\n  });\n\n  const handleEmailVerificationError = useCallback((error: any) => {\n    if (!error || !error.response || !error.response.data) {\n      return false;\n    }\n\n    const data = error.response.data as EmailVerificationError;\n    \n    // Check if this is an email verification error\n    const isEmailVerificationError = \n      data.code === \"email_not_verified\" ||\n      data.email_not_verified !== undefined ||\n      (data.email_verified !== undefined && \n       (data.email_verified === false || data.email_verified === \"False\"));\n\n    if (isEmailVerificationError) {\n      const email = data.email || \"\";\n      const message = data.message || \n                     data.email || \n                     data.email_not_verified || \n                     \"Your email address has not been verified. Please check your inbox for the verification link or request a new one.\";\n\n      setVerificationState({\n        isPopupOpen: true,\n        email,\n        message,\n      });\n\n      return true; // Indicates that this was an email verification error\n    }\n\n    return false; // Not an email verification error\n  }, []);\n\n  const closePopup = useCallback(() => {\n    setVerificationState(prev => ({\n      ...prev,\n      isPopupOpen: false,\n    }));\n  }, []);\n\n  return {\n    verificationState,\n    handleEmailVerificationError,\n    closePopup,\n  };\n};\n"], "names": [], "mappings": ";;;AAEA;AAFA;;AAkBO,MAAM,8BAA8B;IACzC,MAAM,CAAC,mBAAmB,qBAAqB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAA0B;QACjF,aAAa;QACb,OAAO;QACP,SAAS;IACX;IAEA,MAAM,+BAA+B,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE,CAAC;QAChD,IAAI,CAAC,SAAS,CAAC,MAAM,QAAQ,IAAI,CAAC,MAAM,QAAQ,CAAC,IAAI,EAAE;YACrD,OAAO;QACT;QAEA,MAAM,OAAO,MAAM,QAAQ,CAAC,IAAI;QAEhC,+CAA+C;QAC/C,MAAM,2BACJ,KAAK,IAAI,KAAK,wBACd,KAAK,kBAAkB,KAAK,aAC3B,KAAK,cAAc,KAAK,aACxB,CAAC,KAAK,cAAc,KAAK,SAAS,KAAK,cAAc,KAAK,OAAO;QAEpE,IAAI,0BAA0B;YAC5B,MAAM,QAAQ,KAAK,KAAK,IAAI;YAC5B,MAAM,UAAU,KAAK,OAAO,IACb,KAAK,KAAK,IACV,KAAK,kBAAkB,IACvB;YAEf,qBAAqB;gBACnB,aAAa;gBACb;gBACA;YACF;YAEA,OAAO,MAAM,sDAAsD;QACrE;QAEA,OAAO,OAAO,kCAAkC;IAClD,GAAG,EAAE;IAEL,MAAM,aAAa,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE;QAC7B,qBAAqB,CAAA,OAAQ,CAAC;gBAC5B,GAAG,IAAI;gBACP,aAAa;YACf,CAAC;IACH,GAAG,EAAE;IAEL,OAAO;QACL;QACA;QACA;IACF;AACF", "debugId": null}}, {"offset": {"line": 300, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/IMPORTANT/work/export/front/src/components/EmailVerificationHandler.tsx"], "sourcesContent": ["\"use client\";\n\nimport React, { useEffect } from \"react\";\nimport EmailVerificationPopup from \"./EmailVerificationPopup\";\nimport { useEmailVerificationHandler } from \"@/hooks/useEmailVerificationHandler\";\n\nconst EmailVerificationHandler: React.FC = () => {\n  const { verificationState, handleEmailVerificationError, closePopup } = useEmailVerificationHandler();\n\n  useEffect(() => {\n    // Listen for email verification errors from anywhere in the app\n    const handleEmailVerificationEvent = (event: CustomEvent) => {\n      handleEmailVerificationError(event.detail);\n    };\n\n    window.addEventListener(\"emailVerificationError\", handleEmailVerificationEvent as EventListener);\n\n    return () => {\n      window.removeEventListener(\"emailVerificationError\", handleEmailVerificationEvent as EventListener);\n    };\n  }, [handleEmailVerificationError]);\n\n  return (\n    <EmailVerificationPopup\n      isOpen={verificationState.isPopupOpen}\n      onClose={closePopup}\n      email={verificationState.email}\n      message={verificationState.message}\n    />\n  );\n};\n\nexport default EmailVerificationHandler;\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAJA;;;;;AAMA,MAAM,2BAAqC;IACzC,MAAM,EAAE,iBAAiB,EAAE,4BAA4B,EAAE,UAAU,EAAE,GAAG,CAAA,GAAA,2IAAA,CAAA,8BAA2B,AAAD;IAElG,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,gEAAgE;QAChE,MAAM,+BAA+B,CAAC;YACpC,6BAA6B,MAAM,MAAM;QAC3C;QAEA,OAAO,gBAAgB,CAAC,0BAA0B;QAElD,OAAO;YACL,OAAO,mBAAmB,CAAC,0BAA0B;QACvD;IACF,GAAG;QAAC;KAA6B;IAEjC,qBACE,8OAAC,4IAAA,CAAA,UAAsB;QACrB,QAAQ,kBAAkB,WAAW;QACrC,SAAS;QACT,OAAO,kBAAkB,KAAK;QAC9B,SAAS,kBAAkB,OAAO;;;;;;AAGxC;uCAEe", "debugId": null}}, {"offset": {"line": 344, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/IMPORTANT/work/export/front/src/locales/ar.ts"], "sourcesContent": ["// Arabic translations for the nursery app\nexport const ar = {\n  // Common\n  common: {\n    loading: \"جاري التحميل...\",\n    save: \"حفظ\",\n    cancel: \"إلغاء\",\n    delete: \"حذف\",\n    edit: \"تعديل\",\n    add: \"إضافة\",\n    submit: \"إرسال\",\n    back: \"رجوع\",\n    next: \"التالي\",\n    previous: \"السابق\",\n    close: \"إغلاق\",\n    confirm: \"تأكيد\",\n    yes: \"نعم\",\n    no: \"لا\",\n    search: \"بحث\",\n    filter: \"تصفية\",\n    all: \"الكل\",\n    none: \"لا يوجد\",\n    required: \"مطلوب\",\n    optional: \"اختياري\",\n    success: \"نجح\",\n    error: \"خطأ\",\n    warning: \"تحذير\",\n    info: \"معلومات\",\n  },\n\n  // Navigation\n  nav: {\n    home: \"الرئيسية\",\n    account: \"حسابي\",\n    permissions: \"التصاريح\",\n    children: \"الأطفال\",\n    profile: \"الملف الشخصي\",\n    settings: \"الإعدادات\",\n    admin: \"لوحة الإدارة\",\n    logout: \"تسجيل الخروج\",\n    login: \"تسجيل الدخول\",\n    register: \"إنشاء حساب\",\n  },\n\n  // Authentication\n  auth: {\n    login: {\n      title: \"تسجيل الدخول إلى حضانة الأمل\",\n      subtitle: \"مرحباً بك في نظام إدارة التصاريح\",\n      email: \"البريد الإلكتروني\",\n      password: \"كلمة المرور\",\n      rememberMe: \"تذكرني\",\n      forgotPassword: \"نسيت كلمة المرور؟\",\n      loginButton: \"تسجيل الدخول\",\n      noAccount: \"ليس لديك حساب؟\",\n      createAccount: \"إنشاء حساب جديد\",\n      emailPlaceholder: \"أدخل بريدك الإلكتروني\",\n      passwordPlaceholder: \"أدخل كلمة المرور\",\n    },\n    register: {\n      title: \"إنشاء حساب جديد\",\n      subtitle: \"انضم إلى عائلة حضانة الأمل\",\n      firstName: \"الاسم الأول\",\n      lastName: \"اسم العائلة\",\n      email: \"البريد الإلكتروني\",\n      password: \"كلمة المرور\",\n      confirmPassword: \"تأكيد كلمة المرور\",\n      phone: \"رقم الهاتف\",\n      city: \"المدينة\",\n      governorate: \"المحافظة\",\n      registerButton: \"إنشاء الحساب\",\n      haveAccount: \"لديك حساب بالفعل؟\",\n      loginHere: \"سجل دخولك هنا\",\n      firstNamePlaceholder: \"أدخل اسمك الأول\",\n      lastNamePlaceholder: \"أدخل اسم العائلة\",\n      emailPlaceholder: \"أدخل بريدك الإلكتروني\",\n      passwordPlaceholder: \"أدخل كلمة مرور قوية\",\n      confirmPasswordPlaceholder: \"أعد إدخال كلمة المرور\",\n      phonePlaceholder: \"أدخل رقم هاتفك\",\n      cityPlaceholder: \"أدخل مدينتك\",\n      governoratePlaceholder: \"اختر محافظتك\",\n    },\n    activation: {\n      title: \"تفعيل الحساب\",\n      subtitle: \"أدخل رمز التفعيل المرسل إلى بريدك الإلكتروني\",\n      code: \"رمز التفعيل\",\n      codePlaceholder: \"أدخل الرمز المكون من 6 أرقام\",\n      activateButton: \"تفعيل الحساب\",\n      resendCode: \"إعادة إرسال الرمز\",\n      changeEmail: \"تغيير البريد الإلكتروني\",\n    },\n    forgotPassword: {\n      title: \"استعادة كلمة المرور\",\n      subtitle: \"أدخل بريدك الإلكتروني لاستعادة كلمة المرور\",\n      email: \"البريد الإلكتروني\",\n      emailPlaceholder: \"أدخل بريدك الإلكتروني\",\n      sendCode: \"إرسال رمز الاستعادة\",\n      backToLogin: \"العودة لتسجيل الدخول\",\n    },\n    resetPassword: {\n      title: \"إعادة تعيين كلمة المرور\",\n      subtitle: \"أدخل الرمز وكلمة المرور الجديدة\",\n      code: \"رمز الاستعادة\",\n      newPassword: \"كلمة المرور الجديدة\",\n      confirmPassword: \"تأكيد كلمة المرور الجديدة\",\n      codePlaceholder: \"أدخل رمز الاستعادة\",\n      newPasswordPlaceholder: \"أدخل كلمة المرور الجديدة\",\n      confirmPasswordPlaceholder: \"أعد إدخال كلمة المرور الجديدة\",\n      resetButton: \"إعادة تعيين كلمة المرور\",\n    },\n  },\n\n  // Account\n  account: {\n    title: \"حسابي\",\n    subtitle: \"إدارة معلوماتك الشخصية وتصاريح الأطفال\",\n    profile: {\n      title: \"الملف الشخصي\",\n      subtitle: \"معلوماتك الشخصية\",\n      personalInfo: \"المعلومات الشخصية\",\n      contactInfo: \"معلومات الاتصال\",\n      location: \"الموقع\",\n      accountStatus: \"حالة الحساب\",\n      approved: \"مُعتمد\",\n      pending: \"في الانتظار\",\n      emailVerified: \"البريد مُفعل\",\n      emailNotVerified: \"البريد غير مُفعل\",\n      admin: \"مدير\",\n      joinedDate: \"تاريخ الانضمام\",\n    },\n    children: {\n      title: \"الأطفال\",\n      subtitle: \"إدارة معلومات أطفالك\",\n      addChild: \"إضافة طفل\",\n      noChildren: \"لم تتم إضافة أطفال بعد\",\n      addFirstChild: \"أضف طفلك الأول لبدء إنشاء التصاريح\",\n      childName: \"اسم الطفل\",\n      childClass: \"الفصل\",\n      childStage: \"المرحلة\",\n      childNamePlaceholder: \"أدخل اسم الطفل\",\n      childClassPlaceholder: \"أدخل فصل الطفل\",\n      childStagePlaceholder: \"اختر مرحلة الطفل\",\n      stages: {\n        nursery: \"حضانة\",\n        kg1: \"روضة أولى\",\n        kg2: \"روضة ثانية\",\n      },\n    },\n    permissions: {\n      title: \"التصاريح\",\n      subtitle: \"إدارة تصاريح استلام الأطفال\",\n      addPermission: \"إضافة تصريح\",\n      noPermissions: \"لم يتم إنشاء تصاريح بعد\",\n      createFirstPermission: \"أنشئ تصريحك الأول للسماح لشخص باستلام طفلك\",\n      selectChild: \"اختر الطفل\",\n      receiverName: \"اسم المستلم\",\n      receiverNamePlaceholder: \"أدخل اسم الشخص الذي سيستلم الطفل\",\n      idImage: \"صورة الهوية\",\n      idImagePlaceholder: \"اسحب وأفلت صورة هوية المستلم هنا أو انقر للاختيار\",\n      createPermission: \"إنشاء التصريح\",\n      otp: \"رمز التصريح\",\n      status: {\n        pending: \"في الانتظار\",\n        approved: \"مُعتمد\",\n        declined: \"مرفوض\",\n        expired: \"منتهي الصلاحية\",\n      },\n      createdAt: \"تاريخ الإنشاء\",\n      expiresAt: \"تاريخ انتهاء الصلاحية\",\n      deleteConfirm: \"هل أنت متأكد من حذف التصريح لـ {name}؟ لا يمكن التراجع عن هذا الإجراء.\",\n    },\n    password: {\n      title: \"تغيير كلمة المرور\",\n      subtitle: \"تحديث كلمة مرور حسابك\",\n      currentPassword: \"كلمة المرور الحالية\",\n      newPassword: \"كلمة المرور الجديدة\",\n      confirmPassword: \"تأكيد كلمة المرور الجديدة\",\n      currentPasswordPlaceholder: \"أدخل كلمة المرور الحالية\",\n      newPasswordPlaceholder: \"أدخل كلمة المرور الجديدة\",\n      confirmPasswordPlaceholder: \"أعد إدخال كلمة المرور الجديدة\",\n      changePassword: \"تغيير كلمة المرور\",\n      strength: {\n        weak: \"ضعيفة\",\n        medium: \"متوسطة\",\n        strong: \"قوية\",\n      },\n    },\n  },\n\n  // Admin\n  admin: {\n    title: \"لوحة الإدارة\",\n    subtitle: \"إدارة النظام\",\n    users: {\n      title: \"إدارة المستخدمين\",\n      subtitle: \"إدارة حسابات المستخدمين والموافقات\",\n      totalUsers: \"إجمالي المستخدمين\",\n      pendingApproval: \"في انتظار الموافقة\",\n      approvedUsers: \"المستخدمون المُعتمدون\",\n      approve: \"اعتماد\",\n      delete: \"حذف\",\n      userDetails: \"تفاصيل المستخدم\",\n      contactInfo: \"معلومات الاتصال\",\n      children: \"الأطفال\",\n      noChildren: \"لا يوجد أطفال مسجلون\",\n      approvalInfo: \"معلومات الاعتماد\",\n      approvedBy: \"اعتمد بواسطة\",\n      approvedOn: \"تاريخ الاعتماد\",\n    },\n    permissions: {\n      title: \"إدارة التصاريح\",\n      subtitle: \"الموافقة على تصاريح استلام الأطفال أو رفضها\",\n      allPermissions: \"جميع التصاريح\",\n      pendingPermissions: \"التصاريح المعلقة\",\n      approvedPermissions: \"التصاريح المُعتمدة\",\n      declinedPermissions: \"التصاريح المرفوضة\",\n      expiredPermissions: \"التصاريح المنتهية\",\n      approve: \"اعتماد\",\n      decline: \"رفض\",\n      parent: \"ولي الأمر\",\n      child: \"الطفل\",\n      receiver: \"المستلم\",\n      created: \"تاريخ الإنشاء\",\n      expires: \"تاريخ انتهاء الصلاحية\",\n      approvedBy: \"اعتمد بواسطة\",\n      approvedOn: \"تاريخ الاعتماد\",\n    },\n    settings: {\n      title: \"إعدادات النظام\",\n      subtitle: \"إعدادات إدارة النظام\",\n      systemMaintenance: \"صيانة النظام\",\n      cleanupExpired: \"تنظيف التصاريح المنتهية\",\n      cleanupDescription: \"تنظيف التصاريح المنتهية والحفاظ على صحة النظام\",\n      adminInfo: \"معلومات المدير\",\n      adminType: \"نوع المدير\",\n      superAdmin: \"مدير عام\",\n      normalAdmin: \"مدير عادي\",\n      superAdminDescription: \"لديك صلاحيات إدارية كاملة تشمل إدارة المستخدمين واعتماد/رفض التصاريح وصيانة النظام\",\n      normalAdminDescription: \"لديك صلاحيات إدارية محدودة. يمكنك عرض التصاريح المُعتمدة بالتفصيل الكامل ولكن لا يمكنك إدارة المستخدمين أو اعتماد/رفض التصاريح\",\n    },\n  },\n\n  // Nursery specific\n  nursery: {\n    name: \"حضانة الأمل\",\n    tagline: \"مكان آمن لنمو أطفالكم\",\n    welcome: \"مرحباً بكم في حضانة الأمل\",\n    description: \"نوفر بيئة تعليمية آمنة ومحبة لأطفالكم مع أحدث أنظمة الأمان والمتابعة\",\n    features: {\n      safety: \"الأمان أولاً\",\n      education: \"تعليم متميز\",\n      care: \"رعاية شاملة\",\n      technology: \"تقنية حديثة\",\n    },\n  },\n\n  // Pages\n  pages: {\n    home: {\n      hero: {\n        title: \"مرحباً بكم في حضانة الأمل\",\n        subtitle: \"نوفر بيئة تعليمية آمنة ومحبة لأطفالكم مع أحدث أنظمة الأمان والمتابعة\",\n        getStarted: \"ابدأ الآن\",\n        learnMore: \"اعرف المزيد\",\n      },\n      features: {\n        title: \"لماذا تختار حضانة الأمل؟\",\n        subtitle: \"نوفر أفضل الخدمات والمميزات لضمان راحة وأمان أطفالكم\",\n        safety: {\n          title: \"الأمان أولاً\",\n          description: \"نظام أمان متطور مع تصاريح إلكترونية وتتبع دقيق لضمان سلامة الأطفال\",\n        },\n        education: {\n          title: \"تعليم متميز\",\n          description: \"برامج تعليمية حديثة ومتطورة تناسب جميع المراحل العمرية للأطفال\",\n        },\n        care: {\n          title: \"رعاية شاملة\",\n          description: \"رعاية شاملة ومحبة من فريق مؤهل ومدرب على أعلى المستويات\",\n        },\n        technology: {\n          title: \"تقنية حديثة\",\n          description: \"تقنيات حديثة لمتابعة تطور الأطفال والتواصل المستمر مع الأهالي\",\n        },\n      },\n      howItWorks: {\n        title: \"كيف يعمل نظام التصاريح؟\",\n        subtitle: \"خطوات بسيطة لضمان أمان استلام أطفالكم\",\n        step1: {\n          title: \"إنشاء حساب\",\n          description: \"سجل حسابك وأضف معلومات أطفالك بشكل آمن\",\n        },\n        step2: {\n          title: \"إنشاء تصريح\",\n          description: \"أنشئ تصريح استلام مع صورة هوية الشخص المخول\",\n        },\n        step3: {\n          title: \"استلام آمن\",\n          description: \"استخدم رمز التصريح لاستلام آمن ومؤكد\",\n        },\n      },\n      stats: {\n        families: \"عائلة سعيدة\",\n        children: \"طفل محبوب\",\n        satisfaction: \"رضا الأهالي\",\n        support: \"دعم مستمر\",\n      },\n    },\n  },\n\n  // Messages\n  messages: {\n    success: {\n      loginSuccess: \"تم تسجيل الدخول بنجاح\",\n      registerSuccess: \"تم إنشاء الحساب بنجاح\",\n      activationSuccess: \"تم تفعيل الحساب بنجاح\",\n      passwordChanged: \"تم تغيير كلمة المرور بنجاح\",\n      profileUpdated: \"تم تحديث الملف الشخصي بنجاح\",\n      childAdded: \"تم إضافة الطفل بنجاح\",\n      childUpdated: \"تم تحديث معلومات الطفل بنجاح\",\n      childDeleted: \"تم حذف الطفل بنجاح\",\n      permissionCreated: \"تم إنشاء التصريح بنجاح\",\n      permissionDeleted: \"تم حذف التصريح بنجاح\",\n      permissionApproved: \"تم اعتماد التصريح بنجاح\",\n      permissionDeclined: \"تم رفض التصريح بنجاح\",\n      userApproved: \"تم اعتماد المستخدم بنجاح\",\n      userDeleted: \"تم حذف المستخدم بنجاح\",\n    },\n    error: {\n      loginFailed: \"فشل في تسجيل الدخول\",\n      registerFailed: \"فشل في إنشاء الحساب\",\n      activationFailed: \"فشل في تفعيل الحساب\",\n      passwordChangeFailed: \"فشل في تغيير كلمة المرور\",\n      profileUpdateFailed: \"فشل في تحديث الملف الشخصي\",\n      childAddFailed: \"فشل في إضافة الطفل\",\n      childUpdateFailed: \"فشل في تحديث معلومات الطفل\",\n      childDeleteFailed: \"فشل في حذف الطفل\",\n      permissionCreateFailed: \"فشل في إنشاء التصريح\",\n      permissionDeleteFailed: \"فشل في حذف التصريح\",\n      permissionApproveFailed: \"فشل في اعتماد التصريح\",\n      permissionDeclineFailed: \"فشل في رفض التصريح\",\n      userApproveFailed: \"فشل في اعتماد المستخدم\",\n      userDeleteFailed: \"فشل في حذف المستخدم\",\n      accessDenied: \"تم رفض الوصول\",\n      adminRequired: \"مطلوب صلاحيات إدارية\",\n      superAdminRequired: \"مطلوب صلاحيات مدير عام\",\n      emailNotVerified: \"البريد الإلكتروني غير مُفعل\",\n      accountNotApproved: \"الحساب غير مُعتمد\",\n      networkError: \"خطأ في الشبكة\",\n      serverError: \"خطأ في الخادم\",\n      unknownError: \"خطأ غير معروف\",\n    },\n  },\n\n  // Validation\n  validation: {\n    required: \"هذا الحقل مطلوب\",\n    email: \"يرجى إدخال بريد إلكتروني صحيح\",\n    password: \"كلمة المرور يجب أن تكون 6 أحرف على الأقل\",\n    passwordMatch: \"كلمات المرور غير متطابقة\",\n    phone: \"يرجى إدخال رقم هاتف صحيح\",\n    minLength: \"يجب أن يكون {min} أحرف على الأقل\",\n    maxLength: \"يجب أن يكون {max} أحرف كحد أقصى\",\n    numeric: \"يجب أن يحتوي على أرقام فقط\",\n    alphanumeric: \"يجب أن يحتوي على أحرف وأرقام فقط\",\n  },\n};\n\nexport type TranslationKey = keyof typeof ar;\nexport default ar;\n"], "names": [], "mappings": "AAAA,0CAA0C;;;;;AACnC,MAAM,KAAK;IAChB,SAAS;IACT,QAAQ;QACN,SAAS;QACT,MAAM;QACN,QAAQ;QACR,QAAQ;QACR,MAAM;QACN,KAAK;QACL,QAAQ;QACR,MAAM;QACN,MAAM;QACN,UAAU;QACV,OAAO;QACP,SAAS;QACT,KAAK;QACL,IAAI;QACJ,QAAQ;QACR,QAAQ;QACR,KAAK;QACL,MAAM;QACN,UAAU;QACV,UAAU;QACV,SAAS;QACT,OAAO;QACP,SAAS;QACT,MAAM;IACR;IAEA,aAAa;IACb,KAAK;QACH,MAAM;QACN,SAAS;QACT,aAAa;QACb,UAAU;QACV,SAAS;QACT,UAAU;QACV,OAAO;QACP,QAAQ;QACR,OAAO;QACP,UAAU;IACZ;IAEA,iBAAiB;IACjB,MAAM;QACJ,OAAO;YACL,OAAO;YACP,UAAU;YACV,OAAO;YACP,UAAU;YACV,YAAY;YACZ,gBAAgB;YAChB,aAAa;YACb,WAAW;YACX,eAAe;YACf,kBAAkB;YAClB,qBAAqB;QACvB;QACA,UAAU;YACR,OAAO;YACP,UAAU;YACV,WAAW;YACX,UAAU;YACV,OAAO;YACP,UAAU;YACV,iBAAiB;YACjB,OAAO;YACP,MAAM;YACN,aAAa;YACb,gBAAgB;YAChB,aAAa;YACb,WAAW;YACX,sBAAsB;YACtB,qBAAqB;YACrB,kBAAkB;YAClB,qBAAqB;YACrB,4BAA4B;YAC5B,kBAAkB;YAClB,iBAAiB;YACjB,wBAAwB;QAC1B;QACA,YAAY;YACV,OAAO;YACP,UAAU;YACV,MAAM;YACN,iBAAiB;YACjB,gBAAgB;YAChB,YAAY;YACZ,aAAa;QACf;QACA,gBAAgB;YACd,OAAO;YACP,UAAU;YACV,OAAO;YACP,kBAAkB;YAClB,UAAU;YACV,aAAa;QACf;QACA,eAAe;YACb,OAAO;YACP,UAAU;YACV,MAAM;YACN,aAAa;YACb,iBAAiB;YACjB,iBAAiB;YACjB,wBAAwB;YACxB,4BAA4B;YAC5B,aAAa;QACf;IACF;IAEA,UAAU;IACV,SAAS;QACP,OAAO;QACP,UAAU;QACV,SAAS;YACP,OAAO;YACP,UAAU;YACV,cAAc;YACd,aAAa;YACb,UAAU;YACV,eAAe;YACf,UAAU;YACV,SAAS;YACT,eAAe;YACf,kBAAkB;YAClB,OAAO;YACP,YAAY;QACd;QACA,UAAU;YACR,OAAO;YACP,UAAU;YACV,UAAU;YACV,YAAY;YACZ,eAAe;YACf,WAAW;YACX,YAAY;YACZ,YAAY;YACZ,sBAAsB;YACtB,uBAAuB;YACvB,uBAAuB;YACvB,QAAQ;gBACN,SAAS;gBACT,KAAK;gBACL,KAAK;YACP;QACF;QACA,aAAa;YACX,OAAO;YACP,UAAU;YACV,eAAe;YACf,eAAe;YACf,uBAAuB;YACvB,aAAa;YACb,cAAc;YACd,yBAAyB;YACzB,SAAS;YACT,oBAAoB;YACpB,kBAAkB;YAClB,KAAK;YACL,QAAQ;gBACN,SAAS;gBACT,UAAU;gBACV,UAAU;gBACV,SAAS;YACX;YACA,WAAW;YACX,WAAW;YACX,eAAe;QACjB;QACA,UAAU;YACR,OAAO;YACP,UAAU;YACV,iBAAiB;YACjB,aAAa;YACb,iBAAiB;YACjB,4BAA4B;YAC5B,wBAAwB;YACxB,4BAA4B;YAC5B,gBAAgB;YAChB,UAAU;gBACR,MAAM;gBACN,QAAQ;gBACR,QAAQ;YACV;QACF;IACF;IAEA,QAAQ;IACR,OAAO;QACL,OAAO;QACP,UAAU;QACV,OAAO;YACL,OAAO;YACP,UAAU;YACV,YAAY;YACZ,iBAAiB;YACjB,eAAe;YACf,SAAS;YACT,QAAQ;YACR,aAAa;YACb,aAAa;YACb,UAAU;YACV,YAAY;YACZ,cAAc;YACd,YAAY;YACZ,YAAY;QACd;QACA,aAAa;YACX,OAAO;YACP,UAAU;YACV,gBAAgB;YAChB,oBAAoB;YACpB,qBAAqB;YACrB,qBAAqB;YACrB,oBAAoB;YACpB,SAAS;YACT,SAAS;YACT,QAAQ;YACR,OAAO;YACP,UAAU;YACV,SAAS;YACT,SAAS;YACT,YAAY;YACZ,YAAY;QACd;QACA,UAAU;YACR,OAAO;YACP,UAAU;YACV,mBAAmB;YACnB,gBAAgB;YAChB,oBAAoB;YACpB,WAAW;YACX,WAAW;YACX,YAAY;YACZ,aAAa;YACb,uBAAuB;YACvB,wBAAwB;QAC1B;IACF;IAEA,mBAAmB;IACnB,SAAS;QACP,MAAM;QACN,SAAS;QACT,SAAS;QACT,aAAa;QACb,UAAU;YACR,QAAQ;YACR,WAAW;YACX,MAAM;YACN,YAAY;QACd;IACF;IAEA,QAAQ;IACR,OAAO;QACL,MAAM;YACJ,MAAM;gBACJ,OAAO;gBACP,UAAU;gBACV,YAAY;gBACZ,WAAW;YACb;YACA,UAAU;gBACR,OAAO;gBACP,UAAU;gBACV,QAAQ;oBACN,OAAO;oBACP,aAAa;gBACf;gBACA,WAAW;oBACT,OAAO;oBACP,aAAa;gBACf;gBACA,MAAM;oBACJ,OAAO;oBACP,aAAa;gBACf;gBACA,YAAY;oBACV,OAAO;oBACP,aAAa;gBACf;YACF;YACA,YAAY;gBACV,OAAO;gBACP,UAAU;gBACV,OAAO;oBACL,OAAO;oBACP,aAAa;gBACf;gBACA,OAAO;oBACL,OAAO;oBACP,aAAa;gBACf;gBACA,OAAO;oBACL,OAAO;oBACP,aAAa;gBACf;YACF;YACA,OAAO;gBACL,UAAU;gBACV,UAAU;gBACV,cAAc;gBACd,SAAS;YACX;QACF;IACF;IAEA,WAAW;IACX,UAAU;QACR,SAAS;YACP,cAAc;YACd,iBAAiB;YACjB,mBAAmB;YACnB,iBAAiB;YACjB,gBAAgB;YAChB,YAAY;YACZ,cAAc;YACd,cAAc;YACd,mBAAmB;YACnB,mBAAmB;YACnB,oBAAoB;YACpB,oBAAoB;YACpB,cAAc;YACd,aAAa;QACf;QACA,OAAO;YACL,aAAa;YACb,gBAAgB;YAChB,kBAAkB;YAClB,sBAAsB;YACtB,qBAAqB;YACrB,gBAAgB;YAChB,mBAAmB;YACnB,mBAAmB;YACnB,wBAAwB;YACxB,wBAAwB;YACxB,yBAAyB;YACzB,yBAAyB;YACzB,mBAAmB;YACnB,kBAAkB;YAClB,cAAc;YACd,eAAe;YACf,oBAAoB;YACpB,kBAAkB;YAClB,oBAAoB;YACpB,cAAc;YACd,aAAa;YACb,cAAc;QAChB;IACF;IAEA,aAAa;IACb,YAAY;QACV,UAAU;QACV,OAAO;QACP,UAAU;QACV,eAAe;QACf,OAAO;QACP,WAAW;QACX,WAAW;QACX,SAAS;QACT,cAAc;IAChB;AACF;uCAGe", "debugId": null}}, {"offset": {"line": 714, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/IMPORTANT/work/export/front/src/hooks/useTranslation.ts"], "sourcesContent": ["import { ar } from '@/locales/ar';\n\n// Type for nested object keys\ntype NestedKeyOf<ObjectType extends object> = {\n  [Key in keyof ObjectType & (string | number)]: ObjectType[Key] extends object\n    ? `${Key}` | `${Key}.${NestedKeyOf<ObjectType[Key]>}`\n    : `${Key}`;\n}[keyof ObjectType & (string | number)];\n\ntype TranslationKey = NestedKeyOf<typeof ar>;\n\n// Function to get nested value from object using dot notation\nfunction getNestedValue(obj: any, path: string): string {\n  return path.split('.').reduce((current, key) => current?.[key], obj) || path;\n}\n\n// Function to replace placeholders in translation strings\nfunction replacePlaceholders(text: string, params: Record<string, string | number> = {}): string {\n  return text.replace(/\\{(\\w+)\\}/g, (match, key) => {\n    return params[key]?.toString() || match;\n  });\n}\n\nexport function useTranslation() {\n  const t = (key: TranslationKey, params?: Record<string, string | number>): string => {\n    const translation = getNestedValue(ar, key);\n    return replacePlaceholders(translation, params);\n  };\n\n  return { t };\n}\n\nexport default useTranslation;\n"], "names": [], "mappings": ";;;;AAAA;;AAWA,8DAA8D;AAC9D,SAAS,eAAe,GAAQ,EAAE,IAAY;IAC5C,OAAO,KAAK,KAAK,CAAC,KAAK,MAAM,CAAC,CAAC,SAAS,MAAQ,SAAS,CAAC,IAAI,EAAE,QAAQ;AAC1E;AAEA,0DAA0D;AAC1D,SAAS,oBAAoB,IAAY,EAAE,SAA0C,CAAC,CAAC;IACrF,OAAO,KAAK,OAAO,CAAC,cAAc,CAAC,OAAO;QACxC,OAAO,MAAM,CAAC,IAAI,EAAE,cAAc;IACpC;AACF;AAEO,SAAS;IACd,MAAM,IAAI,CAAC,KAAqB;QAC9B,MAAM,cAAc,eAAe,oHAAA,CAAA,KAAE,EAAE;QACvC,OAAO,oBAAoB,aAAa;IAC1C;IAEA,OAAO;QAAE;IAAE;AACb;uCAEe", "debugId": null}}, {"offset": {"line": 746, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/IMPORTANT/work/export/front/src/components/layout/NurseryLayout.tsx"], "sourcesContent": ["\"use client\";\n\nimport React, { useState, useEffect } from \"react\";\nimport Link from \"next/link\";\nimport { useRouter, usePathname } from \"next/navigation\";\nimport {\n  Home,\n  User,\n  Shield,\n  Settings,\n  LogOut,\n  Menu,\n  X,\n  Baby,\n  Heart,\n  Star,\n  Users,\n} from \"lucide-react\";\nimport useTranslation from \"@/hooks/useTranslation\";\n\ninterface NurseryLayoutProps {\n  children: React.ReactNode;\n}\n\nconst NurseryLayout: React.FC<NurseryLayoutProps> = ({ children }) => {\n  const { t } = useTranslation();\n  const router = useRouter();\n  const pathname = usePathname();\n  const [isMenuOpen, setIsMenuOpen] = useState(false);\n  const [isAuthenticated, setIsAuthenticated] = useState(false);\n  const [isAdmin, setIsAdmin] = useState(false);\n\n  useEffect(() => {\n    const checkAuthStatus = () => {\n      const token = localStorage.getItem(\"access_token\");\n      setIsAuthenticated(!!token);\n\n      // Check admin status\n      if (token) {\n        checkAdminStatus();\n      } else {\n        setIsAdmin(false);\n      }\n    };\n\n    // Check auth status on mount\n    checkAuthStatus();\n\n    // Listen for storage changes (login/logout in other tabs)\n    const handleStorageChange = (e: StorageEvent) => {\n      if (e.key === \"access_token\") {\n        checkAuthStatus();\n      }\n    };\n\n    window.addEventListener(\"storage\", handleStorageChange);\n\n    // Listen for custom auth events (login/logout in same tab)\n    const handleAuthChange = () => {\n      checkAuthStatus();\n    };\n\n    window.addEventListener(\"authChange\", handleAuthChange);\n\n    return () => {\n      window.removeEventListener(\"storage\", handleStorageChange);\n      window.removeEventListener(\"authChange\", handleAuthChange);\n    };\n  }, []);\n\n  const checkAdminStatus = async () => {\n    try {\n      const response = await fetch(\n        \"http://localhost:8000/api/admin/check-status/\",\n        {\n          headers: {\n            Authorization: `Bearer ${localStorage.getItem(\"access_token\")}`,\n          },\n        }\n      );\n\n      if (response.ok) {\n        const data = await response.json();\n        setIsAdmin(data.is_admin);\n      }\n    } catch (error) {\n      console.error(\"Error checking admin status:\", error);\n    }\n  };\n\n  const handleLogout = () => {\n    localStorage.removeItem(\"access_token\");\n    localStorage.removeItem(\"refresh_token\");\n    setIsAuthenticated(false);\n    setIsAdmin(false);\n\n    // Trigger auth change event for navbar update\n    window.dispatchEvent(new Event(\"authChange\"));\n\n    router.push(\"/login\");\n  };\n\n  const navigationItems = [\n    {\n      name: \"الرئيسية\",\n      href: \"/\",\n      icon: Home,\n      show: true,\n    },\n    {\n      name: \"حسابي\",\n      href: \"/account\",\n      icon: User,\n      show: isAuthenticated,\n    },\n    {\n      name: \"لوحة الإدارة\",\n      href: \"/admin\",\n      icon: Settings,\n      show: isAuthenticated && isAdmin,\n    },\n  ];\n\n  const authItems = isAuthenticated\n    ? [\n        {\n          name: \"تسجيل الخروج\",\n          action: handleLogout,\n          icon: LogOut,\n        },\n      ]\n    : [\n        {\n          name: \"تسجيل الدخول\",\n          href: \"/login\",\n          icon: User,\n        },\n        {\n          name: \"إنشاء حساب\",\n          href: \"/signup\",\n          icon: Users,\n        },\n      ];\n\n  return (\n    <div className=\"min-h-screen bg-gray-50\" dir=\"rtl\">\n      {/* Header */}\n      <header className=\"bg-white border-b border-gray-200 sticky top-0 z-50\">\n        <div className=\"container mx-auto px-4 sm:px-6 lg:px-8\">\n          <div className=\"flex items-center justify-between h-16\">\n            {/* Logo */}\n            <Link\n              href=\"/\"\n              className=\"flex items-center space-x-3 space-x-reverse\"\n            >\n              <div className=\"bg-pink-500 p-2 rounded-lg\">\n                <Baby className=\"w-6 h-6 text-white\" />\n              </div>\n              <div className=\"hidden sm:block\">\n                <h1 className=\"text-xl font-bold text-pink-600\">حضانة الأمل</h1>\n                <p className=\"text-xs text-gray-500\">مكان آمن لنمو أطفالكم</p>\n              </div>\n            </Link>\n\n            {/* Desktop Navigation */}\n            <nav className=\"hidden md:flex items-center space-x-6 space-x-reverse\">\n              {navigationItems\n                .filter((item) => item.show)\n                .map((item) => (\n                  <Link\n                    key={item.name}\n                    href={item.href}\n                    className={`flex items-center space-x-2 space-x-reverse px-3 py-2 rounded-lg transition-colors duration-200 ${\n                      pathname === item.href\n                        ? \"bg-pink-500 text-white\"\n                        : \"text-gray-600 hover:text-pink-600 hover:bg-gray-100\"\n                    }`}\n                  >\n                    <item.icon className=\"w-4 h-4\" />\n                    <span className=\"font-medium\">{item.name}</span>\n                  </Link>\n                ))}\n            </nav>\n\n            {/* Auth Items */}\n            <div className=\"hidden md:flex items-center gap-4 space-x-4 space-x-reverse\">\n              {authItems.map((item) => (\n                <div key={item.name}>\n                  {item.href ? (\n                    <Link\n                      href={item.href}\n                      className=\"flex items-center space-x-2 space-x-reverse px-4 py-2 bg-pink-500 text-white rounded-lg hover:bg-pink-600 transition-colors duration-200\"\n                    >\n                      <item.icon className=\"w-4 h-4\" />\n                      <span className=\"font-medium\">{item.name}</span>\n                    </Link>\n                  ) : (\n                    <button\n                      onClick={item.action}\n                      className=\"flex items-center space-x-2 space-x-reverse px-4 py-2 text-gray-600 hover:text-pink-600 hover:bg-gray-100 rounded-lg transition-colors duration-200\"\n                    >\n                      <item.icon className=\"w-4 h-4\" />\n                      <span className=\"font-medium\">{item.name}</span>\n                    </button>\n                  )}\n                </div>\n              ))}\n            </div>\n\n            {/* Mobile Menu Button */}\n            <button\n              onClick={() => setIsMenuOpen(!isMenuOpen)}\n              className=\"md:hidden p-2 text-gray-600 hover:text-pink-600 hover:bg-gray-100 rounded-lg transition-colors duration-200\"\n            >\n              {isMenuOpen ? (\n                <X className=\"w-6 h-6\" />\n              ) : (\n                <Menu className=\"w-6 h-6\" />\n              )}\n            </button>\n          </div>\n        </div>\n\n        {/* Mobile Menu */}\n        {isMenuOpen && (\n          <div className=\"md:hidden bg-white border-t border-gray-200\">\n            <div className=\"container mx-auto px-4 py-4 space-y-2\">\n              {navigationItems\n                .filter((item) => item.show)\n                .map((item) => (\n                  <Link\n                    key={item.name}\n                    href={item.href}\n                    onClick={() => setIsMenuOpen(false)}\n                    className={`flex items-center space-x-3 space-x-reverse px-4 py-3 rounded-lg transition-colors duration-200 ${\n                      pathname === item.href\n                        ? \"bg-pink-500 text-white\"\n                        : \"text-gray-600 hover:text-pink-600 hover:bg-gray-100\"\n                    }`}\n                  >\n                    <item.icon className=\"w-5 h-5\" />\n                    <span className=\"font-medium\">{item.name}</span>\n                  </Link>\n                ))}\n\n              <div className=\"border-t border-gray-200 pt-2 mt-2\">\n                {authItems.map((item) => (\n                  <div key={item.name}>\n                    {item.href ? (\n                      <Link\n                        href={item.href}\n                        onClick={() => setIsMenuOpen(false)}\n                        className=\"flex items-center space-x-3 space-x-reverse px-4 py-3 text-gray-600 hover:text-pink-600 hover:bg-gray-100 rounded-lg transition-colors duration-200\"\n                      >\n                        <item.icon className=\"w-5 h-5\" />\n                        <span className=\"font-medium\">{item.name}</span>\n                      </Link>\n                    ) : (\n                      <button\n                        onClick={() => {\n                          item.action?.();\n                          setIsMenuOpen(false);\n                        }}\n                        className=\"flex items-center space-x-3 space-x-reverse px-4 py-3 text-gray-600 hover:text-pink-600 hover:bg-gray-100 rounded-lg transition-colors duration-200 w-full text-right\"\n                      >\n                        <item.icon className=\"w-5 h-5\" />\n                        <span className=\"font-medium\">{item.name}</span>\n                      </button>\n                    )}\n                  </div>\n                ))}\n              </div>\n            </div>\n          </div>\n        )}\n      </header>\n\n      {/* Main Content */}\n      <main className=\"flex-1\">{children}</main>\n\n      {/* Footer */}\n      <footer className=\"bg-white border-t border-gray-200 mt-auto\">\n        <div className=\"container mx-auto px-4 sm:px-6 lg:px-8 py-8\">\n          <div className=\"grid grid-cols-1 md:grid-cols-3 gap-8\">\n            {/* Nursery Info */}\n            <div className=\"text-center md:text-right\">\n              <div className=\"flex items-center justify-center md:justify-start space-x-3 space-x-reverse mb-4\">\n                <div className=\"bg-pink-500 p-2 rounded-lg\">\n                  <Baby className=\"w-6 h-6 text-white\" />\n                </div>\n                <div>\n                  <h3 className=\"text-lg font-bold text-pink-600\">\n                    حضانة الأمل\n                  </h3>\n                  <p className=\"text-sm text-gray-500\">مكان آمن لنمو أطفالكم</p>\n                </div>\n              </div>\n              <p className=\"text-gray-600 text-sm leading-relaxed\">\n                نوفر بيئة تعليمية آمنة ومحبة لأطفالكم مع أحدث أنظمة الأمان\n                والمتابعة\n              </p>\n            </div>\n\n            {/* Features */}\n            <div className=\"text-center\">\n              <h4 className=\"font-semibold text-gray-800 mb-4\">مميزاتنا</h4>\n              <div className=\"space-y-2\">\n                <div className=\"flex items-center justify-center space-x-2 space-x-reverse text-sm text-gray-600\">\n                  <Shield className=\"w-4 h-4 text-pink-500\" />\n                  <span>الأمان أولاً</span>\n                </div>\n                <div className=\"flex items-center justify-center space-x-2 space-x-reverse text-sm text-gray-600\">\n                  <Star className=\"w-4 h-4 text-pink-500\" />\n                  <span>تعليم متميز</span>\n                </div>\n                <div className=\"flex items-center justify-center space-x-2 space-x-reverse text-sm text-gray-600\">\n                  <Heart className=\"w-4 h-4 text-pink-500\" />\n                  <span>رعاية شاملة</span>\n                </div>\n              </div>\n            </div>\n\n            {/* Contact */}\n            <div className=\"text-center md:text-left\">\n              <h4 className=\"font-semibold text-gray-800 mb-4\">تواصل معنا</h4>\n              <div className=\"space-y-2 text-sm text-gray-600\">\n                <p>📞 +966 50 123 4567</p>\n                <p>📧 <EMAIL></p>\n                <p>📍 الرياض، المملكة العربية السعودية</p>\n              </div>\n            </div>\n          </div>\n\n          <div className=\"border-t border-gray-200 mt-8 pt-6 text-center\">\n            <p className=\"text-sm text-gray-500\">\n              © 2024 حضانة الأمل. جميع الحقوق محفوظة.\n            </p>\n          </div>\n        </div>\n      </footer>\n    </div>\n  );\n};\n\nexport default NurseryLayout;\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAaA;AAlBA;;;;;;;AAwBA,MAAM,gBAA8C,CAAC,EAAE,QAAQ,EAAE;IAC/D,MAAM,EAAE,CAAC,EAAE,GAAG,CAAA,GAAA,8HAAA,CAAA,UAAc,AAAD;IAC3B,MAAM,SAAS,CAAA,GAAA,kIAAA,CAAA,YAAS,AAAD;IACvB,MAAM,WAAW,CAAA,GAAA,kIAAA,CAAA,cAAW,AAAD;IAC3B,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC7C,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACvD,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAEvC,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,MAAM,kBAAkB;YACtB,MAAM,QAAQ,aAAa,OAAO,CAAC;YACnC,mBAAmB,CAAC,CAAC;YAErB,qBAAqB;YACrB,IAAI,OAAO;gBACT;YACF,OAAO;gBACL,WAAW;YACb;QACF;QAEA,6BAA6B;QAC7B;QAEA,0DAA0D;QAC1D,MAAM,sBAAsB,CAAC;YAC3B,IAAI,EAAE,GAAG,KAAK,gBAAgB;gBAC5B;YACF;QACF;QAEA,OAAO,gBAAgB,CAAC,WAAW;QAEnC,2DAA2D;QAC3D,MAAM,mBAAmB;YACvB;QACF;QAEA,OAAO,gBAAgB,CAAC,cAAc;QAEtC,OAAO;YACL,OAAO,mBAAmB,CAAC,WAAW;YACtC,OAAO,mBAAmB,CAAC,cAAc;QAC3C;IACF,GAAG,EAAE;IAEL,MAAM,mBAAmB;QACvB,IAAI;YACF,MAAM,WAAW,MAAM,MACrB,iDACA;gBACE,SAAS;oBACP,eAAe,CAAC,OAAO,EAAE,aAAa,OAAO,CAAC,iBAAiB;gBACjE;YACF;YAGF,IAAI,SAAS,EAAE,EAAE;gBACf,MAAM,OAAO,MAAM,SAAS,IAAI;gBAChC,WAAW,KAAK,QAAQ;YAC1B;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,gCAAgC;QAChD;IACF;IAEA,MAAM,eAAe;QACnB,aAAa,UAAU,CAAC;QACxB,aAAa,UAAU,CAAC;QACxB,mBAAmB;QACnB,WAAW;QAEX,8CAA8C;QAC9C,OAAO,aAAa,CAAC,IAAI,MAAM;QAE/B,OAAO,IAAI,CAAC;IACd;IAEA,MAAM,kBAAkB;QACtB;YACE,MAAM;YACN,MAAM;YACN,MAAM,mMAAA,CAAA,OAAI;YACV,MAAM;QACR;QACA;YACE,MAAM;YACN,MAAM;YACN,MAAM,kMAAA,CAAA,OAAI;YACV,MAAM;QACR;QACA;YACE,MAAM;YACN,MAAM;YACN,MAAM,0MAAA,CAAA,WAAQ;YACd,MAAM,mBAAmB;QAC3B;KACD;IAED,MAAM,YAAY,kBACd;QACE;YACE,MAAM;YACN,QAAQ;YACR,MAAM,0MAAA,CAAA,SAAM;QACd;KACD,GACD;QACE;YACE,MAAM;YACN,MAAM;YACN,MAAM,kMAAA,CAAA,OAAI;QACZ;QACA;YACE,MAAM;YACN,MAAM;YACN,MAAM,oMAAA,CAAA,QAAK;QACb;KACD;IAEL,qBACE,8OAAC;QAAI,WAAU;QAA0B,KAAI;;0BAE3C,8OAAC;gBAAO,WAAU;;kCAChB,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BAAI,WAAU;;8CAEb,8OAAC,4JAAA,CAAA,UAAI;oCACH,MAAK;oCACL,WAAU;;sDAEV,8OAAC;4CAAI,WAAU;sDACb,cAAA,8OAAC,kMAAA,CAAA,OAAI;gDAAC,WAAU;;;;;;;;;;;sDAElB,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAG,WAAU;8DAAkC;;;;;;8DAChD,8OAAC;oDAAE,WAAU;8DAAwB;;;;;;;;;;;;;;;;;;8CAKzC,8OAAC;oCAAI,WAAU;8CACZ,gBACE,MAAM,CAAC,CAAC,OAAS,KAAK,IAAI,EAC1B,GAAG,CAAC,CAAC,qBACJ,8OAAC,4JAAA,CAAA,UAAI;4CAEH,MAAM,KAAK,IAAI;4CACf,WAAW,CAAC,gGAAgG,EAC1G,aAAa,KAAK,IAAI,GAClB,2BACA,uDACJ;;8DAEF,8OAAC,KAAK,IAAI;oDAAC,WAAU;;;;;;8DACrB,8OAAC;oDAAK,WAAU;8DAAe,KAAK,IAAI;;;;;;;2CATnC,KAAK,IAAI;;;;;;;;;;8CAetB,8OAAC;oCAAI,WAAU;8CACZ,UAAU,GAAG,CAAC,CAAC,qBACd,8OAAC;sDACE,KAAK,IAAI,iBACR,8OAAC,4JAAA,CAAA,UAAI;gDACH,MAAM,KAAK,IAAI;gDACf,WAAU;;kEAEV,8OAAC,KAAK,IAAI;wDAAC,WAAU;;;;;;kEACrB,8OAAC;wDAAK,WAAU;kEAAe,KAAK,IAAI;;;;;;;;;;;qEAG1C,8OAAC;gDACC,SAAS,KAAK,MAAM;gDACpB,WAAU;;kEAEV,8OAAC,KAAK,IAAI;wDAAC,WAAU;;;;;;kEACrB,8OAAC;wDAAK,WAAU;kEAAe,KAAK,IAAI;;;;;;;;;;;;2CAfpC,KAAK,IAAI;;;;;;;;;;8CAuBvB,8OAAC;oCACC,SAAS,IAAM,cAAc,CAAC;oCAC9B,WAAU;8CAET,2BACC,8OAAC,4LAAA,CAAA,IAAC;wCAAC,WAAU;;;;;6DAEb,8OAAC,kMAAA,CAAA,OAAI;wCAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;oBAOvB,4BACC,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BAAI,WAAU;;gCACZ,gBACE,MAAM,CAAC,CAAC,OAAS,KAAK,IAAI,EAC1B,GAAG,CAAC,CAAC,qBACJ,8OAAC,4JAAA,CAAA,UAAI;wCAEH,MAAM,KAAK,IAAI;wCACf,SAAS,IAAM,cAAc;wCAC7B,WAAW,CAAC,gGAAgG,EAC1G,aAAa,KAAK,IAAI,GAClB,2BACA,uDACJ;;0DAEF,8OAAC,KAAK,IAAI;gDAAC,WAAU;;;;;;0DACrB,8OAAC;gDAAK,WAAU;0DAAe,KAAK,IAAI;;;;;;;uCAVnC,KAAK,IAAI;;;;;8CAcpB,8OAAC;oCAAI,WAAU;8CACZ,UAAU,GAAG,CAAC,CAAC,qBACd,8OAAC;sDACE,KAAK,IAAI,iBACR,8OAAC,4JAAA,CAAA,UAAI;gDACH,MAAM,KAAK,IAAI;gDACf,SAAS,IAAM,cAAc;gDAC7B,WAAU;;kEAEV,8OAAC,KAAK,IAAI;wDAAC,WAAU;;;;;;kEACrB,8OAAC;wDAAK,WAAU;kEAAe,KAAK,IAAI;;;;;;;;;;;qEAG1C,8OAAC;gDACC,SAAS;oDACP,KAAK,MAAM;oDACX,cAAc;gDAChB;gDACA,WAAU;;kEAEV,8OAAC,KAAK,IAAI;wDAAC,WAAU;;;;;;kEACrB,8OAAC;wDAAK,WAAU;kEAAe,KAAK,IAAI;;;;;;;;;;;;2CAnBpC,KAAK,IAAI;;;;;;;;;;;;;;;;;;;;;;;;;;;0BA+B/B,8OAAC;gBAAK,WAAU;0BAAU;;;;;;0BAG1B,8OAAC;gBAAO,WAAU;0BAChB,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAI,WAAU;;8CAEb,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAI,WAAU;8DACb,cAAA,8OAAC,kMAAA,CAAA,OAAI;wDAAC,WAAU;;;;;;;;;;;8DAElB,8OAAC;;sEACC,8OAAC;4DAAG,WAAU;sEAAkC;;;;;;sEAGhD,8OAAC;4DAAE,WAAU;sEAAwB;;;;;;;;;;;;;;;;;;sDAGzC,8OAAC;4CAAE,WAAU;sDAAwC;;;;;;;;;;;;8CAOvD,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAG,WAAU;sDAAmC;;;;;;sDACjD,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAI,WAAU;;sEACb,8OAAC,sMAAA,CAAA,SAAM;4DAAC,WAAU;;;;;;sEAClB,8OAAC;sEAAK;;;;;;;;;;;;8DAER,8OAAC;oDAAI,WAAU;;sEACb,8OAAC,kMAAA,CAAA,OAAI;4DAAC,WAAU;;;;;;sEAChB,8OAAC;sEAAK;;;;;;;;;;;;8DAER,8OAAC;oDAAI,WAAU;;sEACb,8OAAC,oMAAA,CAAA,QAAK;4DAAC,WAAU;;;;;;sEACjB,8OAAC;sEAAK;;;;;;;;;;;;;;;;;;;;;;;;8CAMZ,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAG,WAAU;sDAAmC;;;;;;sDACjD,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;8DAAE;;;;;;8DACH,8OAAC;8DAAE;;;;;;8DACH,8OAAC;8DAAE;;;;;;;;;;;;;;;;;;;;;;;;sCAKT,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC;gCAAE,WAAU;0CAAwB;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQjD;uCAEe", "debugId": null}}]}